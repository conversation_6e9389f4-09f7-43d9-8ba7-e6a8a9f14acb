#!/usr/bin/env python3
"""
Test User Role Assignment API Endpoints
"""

import asyncio
import asyncpg
import sys
from pathlib import Path
import httpx

# Add the app directory to Python path
sys.path.append(str(Path(__file__).parent))

from app.config import settings
from app.utils.security import create_token_response

async def test_user_role_assignment_api():
    """Test user role assignment API endpoints."""
    
    print("👥 Testing User Role Assignment API Endpoints")
    print("=" * 50)
    
    try:
        # Connect to database
        db_url = settings.DATABASE_URL.replace("postgresql+asyncpg://", "postgresql://")
        conn = await asyncpg.connect(db_url)
        print("✅ Database connection successful")
        
        # Test 1: Setup authentication
        print("\n👤 Test 1: Setting up authentication...")
        
        # Get a test user (use any existing user and make them superuser for testing)
        user_record = await conn.fetchrow("""
            SELECT u.*, array_agg(DISTINCT r.name) as roles, u.is_superuser
            FROM users u
            LEFT JOIN user_roles ur ON u.id = ur.user_id AND ur.is_active = TRUE
            LEFT JOIN roles r ON ur.role_id = r.id AND r.is_active = TRUE
            GROUP BY u.id, u.email, u.hashed_password, u.full_name, u.is_active, u.is_superuser, u.created_at, u.updated_at
            LIMIT 1
        """)
        
        if not user_record:
            print("❌ No users found")
            return False
        
        print(f"✅ Test user: {user_record['email']}")
        
        # Temporarily make user superuser for testing if not already
        if not user_record['is_superuser']:
            await conn.execute("UPDATE users SET is_superuser = TRUE WHERE id = $1", user_record['id'])
            print("✅ Temporarily granted superuser privileges for testing")
        
        # Create authentication token
        token_data = create_token_response(
            user_id=user_record['id'],
            email=user_record['email'],
            roles=user_record['roles'] or [],
            permissions=[],  # Will be loaded by the system
            is_superuser=True  # Use superuser for testing
        )
        
        auth_token = token_data['access_token']
        print("✅ Authentication token created")
        print(f"✅ User is_superuser: True (for testing)")
        
        # Test 2: Get test role and user for assignment
        print("\n🔍 Test 2: Getting test role and user...")
        
        # Get a test role
        test_role = await conn.fetchrow("SELECT id, name FROM roles WHERE name = 'user' LIMIT 1")
        if not test_role:
            print("❌ No 'user' role found")
            return False
        
        print(f"✅ Test role: {test_role['name']} ({test_role['id']})")
        
        # Get another user for testing (not the admin user)
        test_user = await conn.fetchrow("""
            SELECT id, email FROM users 
            WHERE id != $1 
            LIMIT 1
        """, user_record['id'])
        
        if not test_user:
            print("❌ No additional test user found")
            return False
        
        print(f"✅ Test target user: {test_user['email']} ({test_user['id']})")
        
        # Test 3: Assign role to user
        print("\n➕ Test 3: Testing role assignment...")
        
        assignment_data = {
            "user_id": str(test_user['id']),
            "role_id": str(test_role['id'])
        }
        
        async with httpx.AsyncClient() as client:
            response = await client.post(
                "http://localhost:8000/api/v1/user-roles/assign",
                headers={"Authorization": f"Bearer {auth_token}"},
                json=assignment_data
            )
            
            if response.status_code == 201:
                assignment = response.json()
                print("✅ Role assignment successful")
                print(f"✅ Assignment ID: {assignment['id']}")
                print(f"✅ User ID: {assignment['user_id']}")
                print(f"✅ Role ID: {assignment['role_id']}")
                assignment_id = assignment['id']
            else:
                print(f"❌ Role assignment failed: {response.status_code}")
                print(f"Response: {response.text}")
                return False
        
        # Test 4: Get user roles
        print("\n👤 Test 4: Testing get user roles...")
        
        async with httpx.AsyncClient() as client:
            response = await client.get(
                f"http://localhost:8000/api/v1/user-roles/user/{test_user['id']}",
                headers={"Authorization": f"Bearer {auth_token}"}
            )
            
            if response.status_code == 200:
                user_with_roles = response.json()
                print("✅ Get user roles successful")
                print(f"✅ User: {user_with_roles['email']}")
                print(f"✅ Roles: {user_with_roles['roles']}")
                print(f"✅ Permissions: {len(user_with_roles['permissions'])} permissions")
            else:
                print(f"❌ Get user roles failed: {response.status_code}")
                print(f"Response: {response.text}")
        
        # Test 5: Get role users
        print("\n👥 Test 5: Testing get role users...")
        
        async with httpx.AsyncClient() as client:
            response = await client.get(
                f"http://localhost:8000/api/v1/user-roles/role/{test_role['id']}/users",
                headers={"Authorization": f"Bearer {auth_token}"}
            )
            
            if response.status_code == 200:
                role_users = response.json()
                print("✅ Get role users successful")
                print(f"✅ Total users with role: {role_users['total']}")
                print(f"✅ Users returned: {len(role_users['assignments'])}")
            else:
                print(f"❌ Get role users failed: {response.status_code}")
                print(f"Response: {response.text}")
        
        # Test 6: List all assignments
        print("\n📋 Test 6: Testing list assignments...")
        
        async with httpx.AsyncClient() as client:
            response = await client.get(
                "http://localhost:8000/api/v1/user-roles/assignments",
                headers={"Authorization": f"Bearer {auth_token}"}
            )
            
            if response.status_code == 200:
                assignments = response.json()
                print("✅ List assignments successful")
                print(f"✅ Total assignments: {assignments['total']}")
                print(f"✅ Assignments returned: {len(assignments['assignments'])}")
            else:
                print(f"❌ List assignments failed: {response.status_code}")
                print(f"Response: {response.text}")
        
        # Test 7: Update assignment
        print("\n✏️  Test 7: Testing update assignment...")
        
        update_data = {
            "notes": "Updated via API test",
            "is_active": True
        }
        
        async with httpx.AsyncClient() as client:
            response = await client.put(
                f"http://localhost:8000/api/v1/user-roles/assignments/{assignment_id}",
                headers={"Authorization": f"Bearer {auth_token}"},
                json=update_data
            )
            
            if response.status_code == 200:
                updated_assignment = response.json()
                print("✅ Update assignment successful")
                print(f"✅ Notes: {updated_assignment['notes']}")
                print(f"✅ Is active: {updated_assignment['is_active']}")
            else:
                print(f"❌ Update assignment failed: {response.status_code}")
                print(f"Response: {response.text}")
        
        # Test 8: Revoke role from user
        print("\n➖ Test 8: Testing role revocation...")
        
        async with httpx.AsyncClient() as client:
            response = await client.delete(
                f"http://localhost:8000/api/v1/user-roles/revoke/{test_user['id']}/{test_role['id']}",
                headers={"Authorization": f"Bearer {auth_token}"}
            )
            
            if response.status_code == 204:
                print("✅ Role revocation successful")
            else:
                print(f"❌ Role revocation failed: {response.status_code}")
                print(f"Response: {response.text}")
        
        # Test 9: Verify revocation
        print("\n🔍 Test 9: Verifying role revocation...")
        
        async with httpx.AsyncClient() as client:
            response = await client.get(
                f"http://localhost:8000/api/v1/user-roles/user/{test_user['id']}",
                headers={"Authorization": f"Bearer {auth_token}"}
            )
            
            if response.status_code == 200:
                user_with_roles = response.json()
                print("✅ Verification successful")
                print(f"✅ User roles after revocation: {user_with_roles['roles']}")
                
                # Check if role was actually revoked
                if test_role['name'] not in user_with_roles['roles']:
                    print("✅ Role successfully revoked from user")
                else:
                    print("⚠️  Role still appears in user's roles")
            else:
                print(f"❌ Verification failed: {response.status_code}")
        
        print("\n🎉 User Role Assignment API Test Completed!")
        print("=" * 50)
        print("✅ Role assignment endpoint works")
        print("✅ Get user roles endpoint works")
        print("✅ Get role users endpoint works")
        print("✅ List assignments endpoint works")
        print("✅ Update assignment endpoint works")
        print("✅ Role revocation endpoint works")
        print("✅ Role revocation verification works")
        
        return True
        
    except Exception as e:
        print(f"\n❌ Failed to test user role assignment API: {e}")
        import traceback
        traceback.print_exc()
        return False
        
    finally:
        if 'conn' in locals():
            await conn.close()
            print("\n📡 Database connection closed")

if __name__ == "__main__":
    success = asyncio.run(test_user_role_assignment_api())
    sys.exit(0 if success else 1)

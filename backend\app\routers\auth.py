"""
Authentication API routes
"""

from fastapi import API<PERSON>out<PERSON>, Depends, HTTPException, status, Request
from fastapi.security import H<PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from asyncpg import Connection

try:
    from ..database import get_db_connection
    from ..dependencies import get_redis_connection
    from ..services.auth_service import AuthService
    from ..schemas.auth import (
        UserCreate, UserLogin, UserUpdate, UserResponse,
        AuthResponse, Token, RefreshToken, PasswordChange,
        APIResponse, ErrorResponse
    )
    from ..models.user import User
    from ..middleware.rate_limiting import check_auth_rate_limit
    from ..utils.csrf import generate_csrf_token, verify_csrf_token, CSRFTokenResponse
except ImportError:
    from database import get_db_connection
    from dependencies import get_redis_connection
    from services.auth_service import AuthService
    from schemas.auth import (
        UserCreate, UserLogin, UserUpdate, UserResponse,
        AuthResponse, Token, <PERSON><PERSON>reshToken, PasswordChange,
        APIResponse, ErrorResponse
    )
    from models.user import User
    from middleware.rate_limiting import check_auth_rate_limit
    from utils.csrf import generate_csrf_token, verify_csrf_token, CSRFTokenResponse

# Create router
router = APIRouter(prefix="/auth", tags=["authentication"])

# Security scheme
security = HTTPBearer()


@router.get("/csrf-token")
async def get_csrf_token():
    """
    Get a CSRF token for authentication operations.

    Returns:
        CSRF token that should be included in subsequent requests
    """
    token = generate_csrf_token()
    csrf_response = CSRFTokenResponse(token)

    return {
        "success": True,
        "data": csrf_response.to_dict(),
        "message": "CSRF token generated successfully"
    }


@router.post("/validate-password")
async def validate_password_strength(password_data: dict):
    """
    Validate password strength and provide real-time feedback.

    Args:
        password_data: Dictionary containing 'password' field

    Returns:
        Password strength analysis with score, level, and feedback
    """
    try:
        from ..utils.validation import InputValidator

        password = password_data.get("password", "")

        # Get comprehensive password strength analysis
        strength_analysis = InputValidator.get_password_strength_score(password)

        # Also get basic validation results
        is_valid, validation_errors = InputValidator.validate_password(password)

        return {
            "success": True,
            "data": {
                "is_valid": is_valid,
                "validation_errors": validation_errors,
                "strength": strength_analysis
            },
            "message": "Password validation completed"
        }

    except Exception as e:
        return {
            "success": False,
            "error": "Password validation failed",
            "message": str(e)
        }


async def get_current_user(
    credentials: HTTPAuthorizationCredentials = Depends(security),
    conn: Connection = Depends(get_db_connection)
) -> User:
    """Dependency to get current authenticated user."""
    token = credentials.credentials
    return await AuthService.get_current_user(conn, token)


async def get_current_active_user(current_user: User = Depends(get_current_user)) -> User:
    """Dependency to get current active user."""
    if not current_user.is_active:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Inactive user"
        )
    return current_user


@router.post("/register", response_model=AuthResponse, status_code=status.HTTP_201_CREATED)
async def register(
    request: Request,
    user_data: UserCreate,
    conn: Connection = Depends(get_db_connection)
):
    """Register a new user with enhanced input validation."""
    # Check rate limit for registration attempts
    await check_auth_rate_limit(request)

    # Verify CSRF token for registration
    await verify_csrf_token(request)

    try:
        # Enhanced input validation
        from ..utils.validation import AuthInputValidator

        validation_result = AuthInputValidator.validate_registration_input(
            email=user_data.email,
            password=user_data.password,
            full_name=user_data.full_name
        )

        if not validation_result["is_valid"]:
            # Return detailed validation errors for registration
            error_details = []
            for field, errors in validation_result["errors"].items():
                for error in errors:
                    error_details.append(f"{field}: {error}")

            raise HTTPException(
                status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
                detail=f"Validation failed: {'; '.join(error_details)}"
            )

        # Use sanitized data for registration
        sanitized_data = validation_result["sanitized_data"]
        user_data.email = sanitized_data.get("email", user_data.email)
        user_data.password = sanitized_data.get("password", user_data.password)
        if "full_name" in sanitized_data:
            user_data.full_name = sanitized_data["full_name"]

        user, tokens = await AuthService.register_user(conn, user_data)

        return AuthResponse(
            user=UserResponse(**user.to_dict()),
            token=Token(**tokens)
        )

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Registration failed: {str(e)}"
        )


@router.post("/login", response_model=AuthResponse)
async def login(
    login_data: UserLogin,
    conn: Connection = Depends(get_db_connection)
):
    """Authenticate user."""
    # Note: Rate limiting temporarily disabled due to JSON body parsing issue
    # await check_auth_rate_limit(request)

    try:
        user, tokens = await AuthService.authenticate_user(conn, login_data)

        return AuthResponse(
            user=UserResponse(**user.to_dict()),
            token=Token(**tokens)
        )

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Authentication failed"
        )


@router.post("/refresh", response_model=Token)
async def refresh_token(
    refresh_data: RefreshToken,
    conn: Connection = Depends(get_db_connection)
):
    """Refresh access token."""
    try:
        tokens = await AuthService.refresh_access_token(conn, refresh_data.refresh_token)
        return Token(**tokens)
    
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Token refresh failed: {str(e)}"
        )


@router.get("/me", response_model=UserResponse)
async def get_current_user_info(
    current_user: User = Depends(get_current_active_user)
):
    """Get current user information."""
    return UserResponse(**current_user.to_dict())


@router.put("/me", response_model=UserResponse)
async def update_current_user(
    update_data: UserUpdate,
    current_user: User = Depends(get_current_active_user),
    conn: Connection = Depends(get_db_connection)
):
    """Update current user profile."""
    try:
        updated_user = await AuthService.update_user_profile(
            conn, current_user.id, update_data
        )
        return UserResponse(**updated_user.to_dict())
    
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Profile update failed: {str(e)}"
        )


@router.post("/change-password", response_model=APIResponse)
async def change_password(
    password_data: PasswordChange,
    current_user: User = Depends(get_current_active_user),
    conn: Connection = Depends(get_db_connection)
):
    """Change user password."""
    try:
        await AuthService.change_password(
            conn,
            current_user.id,
            password_data.current_password,
            password_data.new_password
        )
        
        return APIResponse(
            success=True,
            message="Password changed successfully"
        )
    
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Password change failed: {str(e)}"
        )


@router.delete("/deactivate", response_model=APIResponse)
async def deactivate_account(
    current_user: User = Depends(get_current_active_user),
    conn: Connection = Depends(get_db_connection)
):
    """Deactivate current user account."""
    try:
        await AuthService.deactivate_user(conn, current_user.id)
        
        return APIResponse(
            success=True,
            message="Account deactivated successfully"
        )
    
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Account deactivation failed: {str(e)}"
        )


@router.post("/logout", response_model=APIResponse)
async def logout(
    credentials: HTTPAuthorizationCredentials = Depends(security),
    redis_client = Depends(get_redis_connection)
):
    """Logout user by blacklisting their token."""
    if not credentials:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Authentication required for logout",
            headers={"WWW-Authenticate": "Bearer"},
        )

    try:
        token = credentials.credentials
        success = await AuthService.logout_user(redis_client, token)

        if success:
            return APIResponse(
                success=True,
                message="Logged out successfully. Token has been invalidated."
            )
        else:
            return APIResponse(
                success=False,
                message="Logout failed. Please try again."
            )

    except Exception as e:
        print(f"Logout error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Logout failed due to server error"
        )


# Health check for authentication service
@router.get("/health", response_model=APIResponse)
async def auth_health_check():
    """Authentication service health check."""
    return APIResponse(
        success=True,
        message="Authentication service is healthy"
    )

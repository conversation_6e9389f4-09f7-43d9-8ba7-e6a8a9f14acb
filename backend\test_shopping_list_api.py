#!/usr/bin/env python3
"""
Test script for shopping list API
Verifies CRUD operations for shopping lists and items
"""

import asyncio
import sys
import time
from pathlib import Path

# Add the app directory to Python path
sys.path.insert(0, str(Path(__file__).parent / "app"))

from app.database import DatabaseManager
from app.services.shopping_list_service import ShoppingListService
from app.schemas.shopping_list import ShoppingListCreate, ShoppingListItemCreate


async def test_shopping_list_service_import():
    """Test shopping list service imports."""
    print("🔍 Testing shopping list service imports...")
    
    try:
        from app.services.shopping_list_service import ShoppingListService
        from app.models.shopping_list import ShoppingListRepository, ShoppingListItemRepository
        from app.schemas.shopping_list import ShoppingListCreate, ShoppingListResponse
        
        print("✅ Shopping list service imports successful")
        return True
        
    except Exception as e:
        print(f"❌ Shopping list service import failed: {e}")
        return False


async def test_shopping_list_database_functions():
    """Test shopping list database functions."""
    print("\n🔍 Testing shopping list database functions...")
    
    try:
        db = DatabaseManager()
        await db.initialize()
        
        async with db.get_connection() as conn:
            # Test if we have the required tables
            tables = await conn.fetch("""
                SELECT tablename FROM pg_tables 
                WHERE schemaname = 'public' 
                AND tablename IN ('shopping_lists', 'shopping_list_items')
            """)
            
            table_names = [t["tablename"] for t in tables]
            print(f"✅ Required tables found: {table_names}")
            
            if len(table_names) < 2:
                print("❌ Missing required tables")
                return False
            
            # Test shopping list repository functions
            from app.models.shopping_list import ShoppingListRepository
            
            # Create a test user first
            import uuid
            import time
            test_user_id = str(uuid.uuid4())
            unique_email = f"test_{int(time.time())}@example.com"

            try:
                await conn.execute("""
                    INSERT INTO users (id, email, hashed_password, full_name)
                    VALUES ($1, $2, $3, $4)
                """, test_user_id, unique_email, "hashed_password", "Test User")
            except Exception as e:
                if "duplicate key" in str(e):
                    # User already exists, just use existing one
                    existing_user = await conn.fetchrow("SELECT id FROM users WHERE email = $1", unique_email)
                    if existing_user:
                        test_user_id = str(existing_user["id"])
                else:
                    raise

            # Create a test shopping list
            shopping_list = await ShoppingListRepository.create_shopping_list(
                conn, test_user_id, "Test Shopping List", "Test description"
            )
            print(f"✅ Shopping list created: {shopping_list.name}")
            
            # Get shopping list
            retrieved_list = await ShoppingListRepository.get_shopping_list_by_id(
                conn, shopping_list.id, test_user_id
            )
            print(f"✅ Shopping list retrieved: {retrieved_list.name}")
            
            # Get user shopping lists
            user_lists, total = await ShoppingListRepository.get_user_shopping_lists(
                conn, test_user_id, limit=10, offset=0
            )
            print(f"✅ User shopping lists: {total} total, {len(user_lists)} retrieved")
            
            # Clean up
            await ShoppingListRepository.delete_shopping_list(conn, shopping_list.id, test_user_id)
            print("✅ Test shopping list cleaned up")
        
        await db.close()
        return True
        
    except Exception as e:
        print(f"❌ Shopping list database functions test failed: {e}")
        return False


async def test_shopping_list_service_methods():
    """Test shopping list service methods."""
    print("\n🔍 Testing shopping list service methods...")
    
    try:
        db = DatabaseManager()
        await db.initialize()
        
        # Mock Redis client for testing
        class MockRedis:
            async def get(self, key):
                return None
            async def setex(self, key, ttl, value):
                return True
            async def delete(self, *keys):
                return 0
            async def ping(self):
                return True
        
        redis_client = MockRedis()
        
        async with db.get_connection() as conn:
            import uuid
            test_user_id = str(uuid.uuid4())

            # Create a test user first
            unique_email = f"test2_{int(time.time())}@example.com"

            try:
                await conn.execute("""
                    INSERT INTO users (id, email, hashed_password, full_name)
                    VALUES ($1, $2, $3, $4)
                """, test_user_id, unique_email, "hashed_password", "Test User 2")
            except Exception as e:
                if "duplicate key" in str(e):
                    # User already exists, just use existing one
                    existing_user = await conn.fetchrow("SELECT id FROM users WHERE email = $1", unique_email)
                    if existing_user:
                        test_user_id = str(existing_user["id"])
                else:
                    raise

            # Test create shopping list
            list_data = ShoppingListCreate(
                name="Service Test List",
                description="Testing shopping list service"
            )
            
            shopping_list = await ShoppingListService.create_shopping_list(
                conn, test_user_id, list_data
            )
            print(f"✅ Shopping list service create: {shopping_list.name}")
            
            # Test get user shopping lists
            user_lists, total = await ShoppingListService.get_user_shopping_lists(
                conn, redis_client, test_user_id, limit=10, offset=0
            )
            print(f"✅ Shopping list service get user lists: {total} total")
            
            # Test get shopping list detail
            detail = await ShoppingListService.get_shopping_list_detail(
                conn, redis_client, shopping_list.id, test_user_id
            )
            print(f"✅ Shopping list service get detail: {detail.name}")
            print(f"✅ Shopping list summary: {detail.summary.total_items} items")
            
            # Clean up
            await ShoppingListService.delete_shopping_list(
                conn, redis_client, shopping_list.id, test_user_id
            )
            print("✅ Test shopping list cleaned up")
        
        await db.close()
        return True
        
    except Exception as e:
        print(f"❌ Shopping list service methods test failed: {e}")
        return False


async def test_shopping_list_items():
    """Test shopping list item operations."""
    print("\n🔍 Testing shopping list item operations...")
    
    try:
        db = DatabaseManager()
        await db.initialize()
        
        class MockRedis:
            async def get(self, key):
                return None
            async def setex(self, key, ttl, value):
                return True
            async def delete(self, *keys):
                return 0
        
        redis_client = MockRedis()
        
        async with db.get_connection() as conn:
            import uuid
            test_user_id = str(uuid.uuid4())

            # Create a test user first
            unique_email = f"test3_{int(time.time())}@example.com"

            try:
                await conn.execute("""
                    INSERT INTO users (id, email, hashed_password, full_name)
                    VALUES ($1, $2, $3, $4)
                """, test_user_id, unique_email, "hashed_password", "Test User 3")
            except Exception as e:
                if "duplicate key" in str(e):
                    # User already exists, just use existing one
                    existing_user = await conn.fetchrow("SELECT id FROM users WHERE email = $1", unique_email)
                    if existing_user:
                        test_user_id = str(existing_user["id"])
                else:
                    raise

            # Create a test shopping list
            list_data = ShoppingListCreate(
                name="Items Test List",
                description="Testing shopping list items"
            )
            
            shopping_list = await ShoppingListService.create_shopping_list(
                conn, test_user_id, list_data
            )
            
            # Check if we have any products to add
            product_id = await conn.fetchval("SELECT id FROM products LIMIT 1")
            
            if product_id:
                # Test add item to list
                item_data = ShoppingListItemCreate(
                    product_id=product_id,
                    quantity=2,
                    notes="Test item"
                )
                
                item = await ShoppingListService.add_item_to_list(
                    conn, redis_client, shopping_list.id, test_user_id, item_data
                )
                print(f"✅ Item added to list: product_id={item.product_id}, quantity={item.quantity}")
                
                # Test get list with items
                detail = await ShoppingListService.get_shopping_list_detail(
                    conn, redis_client, shopping_list.id, test_user_id
                )
                print(f"✅ List with items: {len(detail.items)} items")
                print(f"✅ Summary: {detail.summary.total_items} total, {detail.summary.remaining_items} remaining")
                
                # Test analyze shopping list
                analysis = await ShoppingListService.analyze_shopping_list_prices(
                    conn, redis_client, shopping_list.id, test_user_id
                )
                print(f"✅ Price analysis: ${analysis.total_estimated_cost:.2f} estimated cost")
                print(f"✅ Price analysis: {analysis.items_with_prices} items with prices")
            else:
                print("⚠️ No products available for item testing")
            
            # Clean up
            await ShoppingListService.delete_shopping_list(
                conn, redis_client, shopping_list.id, test_user_id
            )
            print("✅ Test shopping list with items cleaned up")
        
        await db.close()
        return True
        
    except Exception as e:
        print(f"❌ Shopping list items test failed: {e}")
        return False


async def test_shopping_list_api_endpoints():
    """Test shopping list API endpoints."""
    print("\n🔍 Testing shopping list API endpoints...")
    
    try:
        from fastapi.testclient import TestClient
        from app.main import app
        
        with TestClient(app) as client:
            # Test shopping lists endpoint structure
            response = client.get("/api/v1/shopping-lists/")
            if response.status_code in [200, 401]:  # 401 without auth
                print("✅ Shopping lists endpoint structure working")
            else:
                print(f"❌ Shopping lists endpoint failed: {response.status_code}")
                return False
            
            # Test create shopping list endpoint
            response = client.post("/api/v1/shopping-lists/", json={
                "name": "Test List",
                "description": "Test description"
            })
            if response.status_code in [201, 401, 422]:  # 401 without auth, 422 validation
                print("✅ Create shopping list endpoint structure working")
            else:
                print(f"❌ Create shopping list endpoint failed: {response.status_code}")
                return False
            
            # Test get shopping list detail endpoint
            response = client.get("/api/v1/shopping-lists/test-id")
            if response.status_code in [200, 401, 404]:  # 401 without auth, 404 not found
                print("✅ Get shopping list detail endpoint structure working")
            else:
                print(f"❌ Get shopping list detail endpoint failed: {response.status_code}")
                return False
            
            # Test add item to list endpoint
            response = client.post("/api/v1/shopping-lists/test-id/items", json={
                "product_id": 1,
                "quantity": 1
            })
            if response.status_code in [201, 401, 404, 422]:
                print("✅ Add item to list endpoint structure working")
            else:
                print(f"❌ Add item to list endpoint failed: {response.status_code}")
                return False
            
            # Test shopping lists health endpoint
            response = client.get("/api/v1/shopping-lists/health/")
            if response.status_code in [200, 503]:  # 503 if database not ready
                print("✅ Shopping lists health endpoint working")
            else:
                print(f"❌ Shopping lists health endpoint failed: {response.status_code}")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ Shopping list API endpoints test failed: {e}")
        return False


async def cleanup():
    """Clean up resources."""
    print("\n🧹 Cleaning up...")
    
    try:
        # Clean up any remaining test data
        db = DatabaseManager()
        await db.initialize()
        
        async with db.get_connection() as conn:
            # Clean up test shopping lists (delete any lists created in last hour)
            await conn.execute("""
                DELETE FROM shopping_lists
                WHERE created_at > NOW() - INTERVAL '1 hour'
                AND name LIKE '%Test%'
            """)
            print("✅ Test data cleaned up")
        
        await db.close()
    except Exception as e:
        print(f"⚠️ Cleanup warning: {e}")


async def main():
    """Run all shopping list API tests."""
    print("🚀 Starting ProfiDent Shopping List API Tests\n")
    print("Testing CRUD operations for shopping lists and items")
    print("=" * 60)
    
    tests = [
        ("Shopping List Service Import", test_shopping_list_service_import),
        ("Shopping List Database Functions", test_shopping_list_database_functions),
        ("Shopping List Service Methods", test_shopping_list_service_methods),
        ("Shopping List Items", test_shopping_list_items),
        ("Shopping List API Endpoints", test_shopping_list_api_endpoints),
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = await test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} failed with exception: {e}")
            results.append((test_name, False))
    
    # Cleanup
    await cleanup()
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 TEST SUMMARY")
    print("=" * 60)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{status} - {test_name}")
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All shopping list API tests passed!")
        print("✅ Shopping list CRUD operations are working correctly")
        print("🛒 Ready for shopping list management functionality")
        return True
    else:
        print("❌ Some tests failed. Check shopping list API configuration.")
        return False


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)

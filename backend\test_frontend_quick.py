#!/usr/bin/env python3
"""
Quick Frontend Test
Test if the React infinite loop is fixed
"""

import asyncio
from playwright.async_api import async_playwright


async def quick_frontend_test():
    """Quick test to check if React infinite loop is fixed."""
    print("🧪 Quick Frontend Test - Checking React Infinite Loop Fix")
    
    async with async_playwright() as p:
        browser = await p.chromium.launch(headless=False)
        page = await browser.new_page()
        
        # Track console messages
        console_messages = []
        page.on("console", lambda msg: console_messages.append(f"{msg.type}: {msg.text}"))
        page.on("pageerror", lambda error: print(f"❌ PAGE ERROR: {error}"))
        
        try:
            # Navigate to frontend
            print("📍 Loading http://localhost:3000...")
            await page.goto("http://localhost:3000", wait_until="networkidle")
            await page.wait_for_timeout(5000)  # Wait 5 seconds
            
            # Check for React infinite loop errors
            infinite_loop_errors = [msg for msg in console_messages if "Maximum update depth exceeded" in msg]
            
            if infinite_loop_errors:
                print(f"❌ Still has infinite loop errors: {len(infinite_loop_errors)} errors")
                for error in infinite_loop_errors[:3]:  # Show first 3
                    print(f"  {error}")
                return False
            else:
                print("✅ No infinite loop errors detected!")
            
            # Check if page loaded properly
            title = await page.title()
            print(f"📄 Page title: {title}")
            
            # Look for search input
            search_input = page.locator('input[type="search"], input[placeholder*="search" i]').first
            if await search_input.is_visible():
                print("✅ Search input found!")
                
                # Try typing in search
                await search_input.click()
                await search_input.fill("test")
                await page.wait_for_timeout(2000)
                
                print("✅ Search input is functional!")
                return True
            else:
                print("❌ Search input not found")
                return False
                
        except Exception as e:
            print(f"❌ Test failed: {e}")
            return False
        finally:
            await browser.close()


if __name__ == "__main__":
    success = asyncio.run(quick_frontend_test())
    if success:
        print("\n🎉 Frontend is working! React infinite loop fixed!")
    else:
        print("\n❌ Frontend still has issues")

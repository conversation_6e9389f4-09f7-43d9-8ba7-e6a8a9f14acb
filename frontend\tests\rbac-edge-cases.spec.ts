import { test, expect } from "@playwright/test";

/**
 * RBAC Edge Cases and Security Tests
 * 
 * Tests edge cases, security scenarios, and error conditions for RBAC:
 * - Token expiration handling
 * - Invalid token scenarios
 * - Permission boundary testing
 * - UI state consistency
 * - Network error handling
 */

const SUPERADMIN_CREDENTIALS = {
  email: "<EMAIL>",
  password: "SuperYzn123!",
};

const BASE_URL = "http://localhost:3000";

test.describe.configure({ mode: "serial" });

test.describe("RBAC Edge Cases and Security", () => {
  test.beforeEach(async ({ page }) => {
    await page.goto(BASE_URL);
    await page.evaluate(() => {
      localStorage.clear();
      sessionStorage.clear();
    });
    await page.waitForLoadState("networkidle");
  });

  test.describe("Token and Authentication Edge Cases", () => {
    test("should handle invalid token gracefully", async ({ page }) => {
      // Set invalid token in localStorage
      await page.evaluate(() => {
        localStorage.setItem("auth-token", "invalid-token-12345");
      });

      // Try to access protected route
      await page.goto(`${BASE_URL}/search`);
      
      // Should redirect to login due to invalid token
      await expect(page).toHaveURL(`${BASE_URL}/login`);
    });

    test("should handle malformed token gracefully", async ({ page }) => {
      // Set malformed token
      await page.evaluate(() => {
        localStorage.setItem("auth-token", "not.a.valid.jwt.token");
      });

      await page.goto(`${BASE_URL}/admin`);
      
      // Should redirect to login
      await expect(page).toHaveURL(`${BASE_URL}/login`);
    });

    test("should handle missing user data gracefully", async ({ page }) => {
      // Login first to get valid token
      await page.goto(`${BASE_URL}/login`);
      await page.waitForLoadState("networkidle");

      await page
        .getByRole("textbox", { name: "Email address" })
        .fill(SUPERADMIN_CREDENTIALS.email);
      await page
        .getByRole("textbox", { name: "Password" })
        .fill(SUPERADMIN_CREDENTIALS.password);

      await Promise.all([
        page.waitForURL(`${BASE_URL}/`, { timeout: 15000 }),
        page.getByRole("button", { name: "Sign in" }).click(),
      ]);

      // Corrupt user data in localStorage while keeping token
      await page.evaluate(() => {
        const token = localStorage.getItem("auth-token");
        localStorage.clear();
        localStorage.setItem("auth-token", token || "");
        localStorage.setItem("auth-user", "invalid-json-data");
      });

      // Refresh page to trigger auth state reload
      await page.reload();
      await page.waitForLoadState("networkidle");

      // Should handle gracefully - either redirect to login or show error
      const currentUrl = page.url();
      expect(
        currentUrl === `${BASE_URL}/login` || 
        currentUrl === `${BASE_URL}/`
      ).toBeTruthy();
    });
  });

  test.describe("Permission Boundary Testing", () => {
    test.beforeEach(async ({ page }) => {
      // Login as superadmin for permission testing
      await page.goto(`${BASE_URL}/login`);
      await page.waitForLoadState("networkidle");

      await page
        .getByRole("textbox", { name: "Email address" })
        .fill(SUPERADMIN_CREDENTIALS.email);
      await page
        .getByRole("textbox", { name: "Password" })
        .fill(SUPERADMIN_CREDENTIALS.password);

      await Promise.all([
        page.waitForURL(`${BASE_URL}/`, { timeout: 15000 }),
        page.getByRole("button", { name: "Sign in" }).click(),
      ]);

      await page.waitForLoadState("networkidle");
    });

    test("should properly handle route guards", async ({ page }) => {
      // Test that route guards work consistently
      const protectedRoutes = [
        "/admin",
        "/admin/users", 
        "/admin/roles",
        "/search",
        "/shopping-lists",
        "/profile"
      ];

      for (const route of protectedRoutes) {
        await page.goto(`${BASE_URL}${route}`);
        // Should stay on the route (not redirect) since user is authenticated
        await expect(page).toHaveURL(`${BASE_URL}${route}`);
      }
    });

    test("should handle permission checks consistently", async ({ page }) => {
      // Test admin dashboard access
      await page.goto(`${BASE_URL}/admin`);
      await expect(
        page.getByRole("heading", { name: "Admin Dashboard" })
      ).toBeVisible();

      // Test user management access
      await page.goto(`${BASE_URL}/admin/users`);
      // Should load without errors
      await page.waitForLoadState("networkidle");

      // Test role management access
      await page.goto(`${BASE_URL}/admin/roles`);
      // Should load without errors
      await page.waitForLoadState("networkidle");
    });

    test("should maintain UI state consistency across navigation", async ({ page }) => {
      // Navigate between different pages and check UI consistency
      await page.goto(`${BASE_URL}/search`);
      await expect(
        page.getByRole("link", { name: "Admin Dashboard" })
      ).toBeVisible();

      await page.goto(`${BASE_URL}/shopping-lists`);
      await expect(
        page.getByRole("link", { name: "Admin Dashboard" })
      ).toBeVisible();

      await page.goto(`${BASE_URL}/profile`);
      await expect(
        page.getByRole("link", { name: "Admin Dashboard" })
      ).toBeVisible();

      // Check header dropdown consistency
      await page.locator("header button").nth(2).click();
      await expect(page.getByText("Super Administrator")).toBeVisible();
    });
  });

  test.describe("Network and Error Handling", () => {
    test("should handle network errors during authentication", async ({ page }) => {
      // Intercept and fail auth requests
      await page.route("**/auth/login", route => {
        route.abort("failed");
      });

      await page.goto(`${BASE_URL}/login`);
      await page
        .getByRole("textbox", { name: "Email address" })
        .fill(SUPERADMIN_CREDENTIALS.email);
      await page
        .getByRole("textbox", { name: "Password" })
        .fill(SUPERADMIN_CREDENTIALS.password);

      await page.getByRole("button", { name: "Sign in" }).click();

      // Should handle network error gracefully
      await page.waitForTimeout(2000);
      // Should remain on login page or show error
      const currentUrl = page.url();
      expect(currentUrl).toContain("/login");
    });

    test("should handle API errors gracefully", async ({ page }) => {
      // Login first
      await page.goto(`${BASE_URL}/login`);
      await page.waitForLoadState("networkidle");

      await page
        .getByRole("textbox", { name: "Email address" })
        .fill(SUPERADMIN_CREDENTIALS.email);
      await page
        .getByRole("textbox", { name: "Password" })
        .fill(SUPERADMIN_CREDENTIALS.password);

      await Promise.all([
        page.waitForURL(`${BASE_URL}/`, { timeout: 15000 }),
        page.getByRole("button", { name: "Sign in" }).click(),
      ]);

      // Intercept and fail API requests
      await page.route("**/api/**", route => {
        if (route.request().url().includes("/users") || route.request().url().includes("/roles")) {
          route.abort("failed");
        } else {
          route.continue();
        }
      });

      // Try to access admin pages that depend on API calls
      await page.goto(`${BASE_URL}/admin/users`);
      await page.waitForLoadState("networkidle");

      // Should handle API errors gracefully (not crash the app)
      // The page should load even if API calls fail
      await expect(page).toHaveURL(`${BASE_URL}/admin/users`);
    });
  });

  test.describe("UI State and Consistency", () => {
    test.beforeEach(async ({ page }) => {
      await page.goto(`${BASE_URL}/login`);
      await page.waitForLoadState("networkidle");

      await page
        .getByRole("textbox", { name: "Email address" })
        .fill(SUPERADMIN_CREDENTIALS.email);
      await page
        .getByRole("textbox", { name: "Password" })
        .fill(SUPERADMIN_CREDENTIALS.password);

      await Promise.all([
        page.waitForURL(`${BASE_URL}/`, { timeout: 15000 }),
        page.getByRole("button", { name: "Sign in" }).click(),
      ]);

      await page.waitForLoadState("networkidle");
    });

    test("should maintain role-based UI after page refresh", async ({ page }) => {
      // Navigate to admin page
      await page.goto(`${BASE_URL}/admin`);
      await expect(
        page.getByRole("heading", { name: "Admin Dashboard" })
      ).toBeVisible();

      // Refresh page
      await page.reload();
      await page.waitForLoadState("networkidle");

      // Should still show admin content
      await expect(
        page.getByRole("heading", { name: "Admin Dashboard" })
      ).toBeVisible();
      await expect(
        page.getByRole("link", { name: "User Management" })
      ).toBeVisible();
    });

    test("should handle rapid navigation between protected routes", async ({ page }) => {
      const routes = ["/search", "/shopping-lists", "/profile", "/admin", "/admin/users"];
      
      // Rapidly navigate between routes
      for (let i = 0; i < 3; i++) {
        for (const route of routes) {
          await page.goto(`${BASE_URL}${route}`);
          await page.waitForLoadState("domcontentloaded");
        }
      }

      // Final check - should still be authenticated and on last route
      await expect(page).toHaveURL(`${BASE_URL}/admin/users`);
      await expect(
        page.getByRole("link", { name: "Admin Dashboard" })
      ).toBeVisible();
    });

    test("should handle browser back/forward with role-based routes", async ({ page }) => {
      // Navigate through several pages
      await page.goto(`${BASE_URL}/search`);
      await page.goto(`${BASE_URL}/admin`);
      await page.goto(`${BASE_URL}/shopping-lists`);

      // Use browser back button
      await page.goBack();
      await expect(page).toHaveURL(`${BASE_URL}/admin`);
      await expect(
        page.getByRole("heading", { name: "Admin Dashboard" })
      ).toBeVisible();

      // Use browser forward button
      await page.goForward();
      await expect(page).toHaveURL(`${BASE_URL}/shopping-lists`);
      await expect(
        page.getByRole("heading", { name: "Shopping Lists" })
      ).toBeVisible();
    });
  });
});

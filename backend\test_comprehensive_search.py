#!/usr/bin/env python3
"""
Comprehensive Search Functionality Test
Test all implemented search features and improvements
"""

import asyncio
from playwright.async_api import async_playwright


async def test_comprehensive_search():
    """Test all search functionality comprehensively."""
    print("🔍 Comprehensive Search Functionality Test")
    
    async with async_playwright() as p:
        browser = await p.chromium.launch(headless=False, slow_mo=200)
        page = await browser.new_page()
        
        # Track network requests
        api_calls = []
        page.on("response", lambda response: api_calls.append(f"{response.status} {response.url}"))
        
        # Track console messages
        console_messages = []
        page.on("console", lambda msg: console_messages.append(f"{msg.type}: {msg.text}"))
        
        test_results = {
            "homepage_loads": False,
            "api_endpoints_working": False,
            "search_suggestions": False,
            "filter_dropdowns": False,
            "add_to_list_buttons": False,
            "search_results": False,
            "z_index_fixed": False
        }
        
        try:
            print("📍 1. Testing Homepage Load...")
            await page.goto("http://localhost:3000", wait_until="networkidle")
            await page.wait_for_timeout(3000)
            
            # Check if homepage loads without errors
            error_overlay = page.locator('vite-error-overlay')
            if not await error_overlay.is_visible():
                test_results["homepage_loads"] = True
                print("✅ Homepage loads without errors")
            else:
                print("❌ Homepage has error overlay")
            
            print("\n📍 2. Testing API Endpoints...")
            # Check if API calls are successful
            successful_apis = [call for call in api_calls if "200 http://localhost:3000/api/v1/" in call]
            if len(successful_apis) >= 4:  # categories, brands, sellers, popular
                test_results["api_endpoints_working"] = True
                print(f"✅ API endpoints working ({len(successful_apis)} successful calls)")
                for api in successful_apis:
                    print(f"  - {api}")
            else:
                print(f"❌ API endpoints issues ({len(successful_apis)} successful calls)")
            
            print("\n📍 3. Testing Search Suggestions...")
            # Test search suggestions
            search_input = page.locator('input[placeholder="Search dental products..."]').first
            await search_input.click()
            await search_input.type("comp", delay=100)
            await page.wait_for_timeout(2000)
            
            # Check if suggestions appear
            suggestions_container = page.locator('.absolute.top-full')
            if await suggestions_container.is_visible():
                test_results["search_suggestions"] = True
                print("✅ Search suggestions appear")
                
                # Check for "Add to List" buttons
                add_buttons = page.locator('button[title="Add to shopping list"]')
                add_button_count = await add_buttons.count()
                if add_button_count > 0:
                    test_results["add_to_list_buttons"] = True
                    print(f"✅ Add to List buttons present ({add_button_count} found)")
                    
                    # Test clicking an add button
                    try:
                        await add_buttons.first.click()
                        print("✅ Add to List button clickable")
                    except:
                        print("❌ Add to List button not clickable")
                else:
                    print("❌ Add to List buttons not found")
            else:
                print("❌ Search suggestions not appearing")
            
            # Clear search input
            await search_input.clear()
            await page.wait_for_timeout(1000)
            
            print("\n📍 4. Testing Filter Dropdowns...")
            # Test filter dropdowns
            filter_section = page.locator('h2:has-text("Find Exactly What You Need")')
            if await filter_section.is_visible():
                print("✅ Filter section visible")
                
                # Test Categories dropdown
                try:
                    categories_dropdown = page.locator('button').filter(has_text="Categories").first
                    await categories_dropdown.click()
                    await page.wait_for_timeout(1500)
                    
                    # Check if dropdown options appear
                    instruments_option = page.locator('button:has-text("Instruments")').first
                    if await instruments_option.is_visible():
                        test_results["filter_dropdowns"] = True
                        print("✅ Filter dropdowns working")
                        
                        # Select an option
                        await instruments_option.click()
                        await page.wait_for_timeout(1000)
                        
                        # Test search with filters
                        search_button = page.locator('button:has-text("Search with Filters")')
                        if await search_button.is_visible():
                            await search_button.click()
                            await page.wait_for_load_state("networkidle")
                            await page.wait_for_timeout(3000)
                            
                            # Check if we're on search page with results
                            current_url = page.url
                            if "/search" in current_url and "category=" in current_url:
                                results_text = page.locator('text=results')
                                if await results_text.is_visible():
                                    test_results["search_results"] = True
                                    print("✅ Search with filters working")
                                else:
                                    print("❌ Search results not displayed")
                            else:
                                print("❌ Filter search navigation failed")
                        else:
                            print("❌ Search with Filters button not found")
                    else:
                        print("❌ Filter dropdown options not appearing")
                except Exception as e:
                    print(f"❌ Filter dropdown test failed: {e}")
            else:
                print("❌ Filter section not visible")
            
            print("\n📍 5. Testing Z-Index Fix...")
            # Go back to homepage to test z-index
            await page.goto("http://localhost:3000")
            await page.wait_for_timeout(2000)
            
            # Test if suggestions appear above other elements
            search_input = page.locator('input[placeholder="Search dental products..."]').first
            await search_input.click()
            await search_input.type("test", delay=100)
            await page.wait_for_timeout(1500)
            
            suggestions_container = page.locator('.absolute.top-full')
            if await suggestions_container.is_visible():
                # Check z-index value
                z_index = await suggestions_container.evaluate("el => getComputedStyle(el).zIndex")
                if z_index == "9999":
                    test_results["z_index_fixed"] = True
                    print("✅ Z-index fix applied (z-index: 9999)")
                else:
                    print(f"❌ Z-index not fixed (z-index: {z_index})")
            
            # Take final screenshot
            await page.screenshot(path="debug_screenshots/comprehensive_test_final.png")
            
            print("\n📊 Test Results Summary:")
            passed_tests = sum(test_results.values())
            total_tests = len(test_results)
            
            for test_name, result in test_results.items():
                status = "✅ PASS" if result else "❌ FAIL"
                print(f"  {test_name}: {status}")
            
            print(f"\n🎯 Overall Score: {passed_tests}/{total_tests} tests passed")
            
            if passed_tests >= 5:  # At least 5 out of 7 tests should pass
                print("🎉 COMPREHENSIVE TEST SUCCESSFUL!")
                return True
            else:
                print("❌ Some tests failed - needs attention")
                return False
                
        except Exception as e:
            print(f"❌ Test failed with error: {e}")
            await page.screenshot(path="debug_screenshots/comprehensive_test_error.png")
            import traceback
            traceback.print_exc()
            return False
        finally:
            await browser.close()


if __name__ == "__main__":
    success = asyncio.run(test_comprehensive_search())
    if success:
        print("\n🎉 All major functionality working!")
    else:
        print("\n❌ Some issues need to be addressed")

#!/usr/bin/env python3
"""
Simple API Tests for ProfiDent
Tests API endpoints using direct HTTP requests
"""

import requests
import time
import json
import sys


class SimpleAPITester:
    """Simple API tester using requests library."""
    
    def __init__(self, base_url="http://localhost:8000"):
        self.base_url = base_url
        self.session = requests.Session()
        self.test_user_token = None
        self.test_user_email = None
        
    def test_health_endpoints(self):
        """Test health check endpoints."""
        print("🧪 Testing health endpoints...")
        
        health_endpoints = [
            "/api/v1/search/health",
            "/api/v1/products/health/",
            "/api/v1/shopping-lists/health/",
            "/api/v1/auth/health"
        ]
        
        for endpoint in health_endpoints:
            try:
                response = self.session.get(f"{self.base_url}{endpoint}")
                if response.status_code == 200:
                    print(f"  ✅ {endpoint} - OK")
                else:
                    print(f"  ❌ {endpoint} - {response.status_code}")
                    return False
            except Exception as e:
                print(f"  ❌ {endpoint} - Connection error: {e}")
                return False
        
        print("🎉 Health endpoints test PASSED")
        return True
    
    def test_user_registration_and_auth(self):
        """Test user registration and authentication."""
        print("🧪 Testing user registration and authentication...")
        
        # Generate unique email
        unique_email = f"api_test_{int(time.time())}@example.com"
        self.test_user_email = unique_email
        
        # Test registration
        register_data = {
            "email": unique_email,
            "password": "TestPassword123!",
            "full_name": "API Test User"
        }
        
        try:
            response = self.session.post(f"{self.base_url}/api/v1/auth/register", json=register_data)
            if response.status_code != 201:
                print(f"  ❌ Registration failed: {response.status_code} - {response.text}")
                return False
            
            register_result = response.json()
            if "user" not in register_result or "token" not in register_result:
                print("  ❌ Invalid registration response format")
                return False
            
            self.test_user_token = register_result["token"]["access_token"]
            print("  ✅ User registration successful")
            
            # Test authentication
            headers = {"Authorization": f"Bearer {self.test_user_token}"}
            response = self.session.get(f"{self.base_url}/api/v1/auth/me", headers=headers)
            
            if response.status_code != 200:
                print(f"  ❌ Authentication failed: {response.status_code}")
                return False
            
            user_data = response.json()
            if user_data["email"] != unique_email:
                print("  ❌ Wrong user data returned")
                return False
            
            print("  ✅ User authentication working")
            
            # Test login
            login_data = {
                "email": unique_email,
                "password": "TestPassword123!"
            }
            
            response = self.session.post(f"{self.base_url}/api/v1/auth/login", json=login_data)
            if response.status_code != 200:
                print(f"  ❌ Login failed: {response.status_code}")
                return False
            
            login_result = response.json()
            if "token" not in login_result:
                print("  ❌ No token in login response")
                return False
            
            print("  ✅ User login working")
            
        except Exception as e:
            print(f"  ❌ Authentication test failed: {e}")
            return False
        
        print("🎉 User registration and authentication test PASSED")
        return True
    
    def test_search_functionality(self):
        """Test search functionality."""
        print("🧪 Testing search functionality...")
        
        if not self.test_user_token:
            print("  ❌ No authentication token available")
            return False
        
        headers = {"Authorization": f"Bearer {self.test_user_token}"}
        
        try:
            # Test basic search
            response = self.session.get(f"{self.base_url}/api/v1/search/?q=dental", headers=headers)
            if response.status_code != 200:
                print(f"  ❌ Basic search failed: {response.status_code}")
                return False
            
            search_result = response.json()
            if "results" not in search_result or search_result["total"] == 0:
                print("  ❌ No search results found")
                return False
            
            print(f"  ✅ Basic search working ({search_result['total']} results)")
            
            # Test search with filters
            response = self.session.get(f"{self.base_url}/api/v1/search/?q=dental&limit=10", headers=headers)
            if response.status_code != 200:
                print(f"  ❌ Filtered search failed: {response.status_code}")
                return False
            
            filtered_result = response.json()
            if len(filtered_result["results"]) > 10:
                print("  ❌ Limit not respected")
                return False
            
            print("  ✅ Search with filters working")
            
            # Test search suggestions
            response = self.session.get(f"{self.base_url}/api/v1/search/suggestions?q=dent", headers=headers)
            if response.status_code != 200:
                print(f"  ❌ Search suggestions failed: {response.status_code}")
                return False
            
            suggestions_result = response.json()
            if "suggestions" not in suggestions_result:
                print("  ❌ No suggestions in response")
                return False
            
            print("  ✅ Search suggestions working")
            
            # Test popular searches
            response = self.session.get(f"{self.base_url}/api/v1/search/popular", headers=headers)
            if response.status_code != 200:
                print(f"  ❌ Popular searches failed: {response.status_code}")
                return False
            
            popular_result = response.json()
            if "popular_searches" not in popular_result:
                print("  ❌ No popular searches in response")
                return False
            
            print("  ✅ Popular searches working")
            
        except Exception as e:
            print(f"  ❌ Search functionality test failed: {e}")
            return False
        
        print("🎉 Search functionality test PASSED")
        return True
    
    def test_product_endpoints(self):
        """Test product-related endpoints."""
        print("🧪 Testing product endpoints...")
        
        if not self.test_user_token:
            print("  ❌ No authentication token available")
            return False
        
        headers = {"Authorization": f"Bearer {self.test_user_token}"}
        
        try:
            # Test categories
            response = self.session.get(f"{self.base_url}/api/v1/products/categories/", headers=headers)
            if response.status_code != 200:
                print(f"  ❌ Get categories failed: {response.status_code}")
                return False
            
            categories_result = response.json()
            if "categories" not in categories_result:
                print("  ❌ No categories in response")
                return False
            
            print(f"  ✅ Categories endpoint working ({len(categories_result['categories'])} categories)")
            
            # Test brands
            response = self.session.get(f"{self.base_url}/api/v1/products/brands/", headers=headers)
            if response.status_code != 200:
                print(f"  ❌ Get brands failed: {response.status_code}")
                return False
            
            brands_result = response.json()
            if "brands" not in brands_result:
                print("  ❌ No brands in response")
                return False
            
            print(f"  ✅ Brands endpoint working ({len(brands_result['brands'])} brands)")
            
            # Test sellers
            response = self.session.get(f"{self.base_url}/api/v1/products/sellers/", headers=headers)
            if response.status_code != 200:
                print(f"  ❌ Get sellers failed: {response.status_code}")
                return False
            
            sellers_result = response.json()
            if "sellers" not in sellers_result:
                print("  ❌ No sellers in response")
                return False
            
            print(f"  ✅ Sellers endpoint working ({len(sellers_result['sellers'])} sellers)")
            
            # Test individual product
            # First get a product ID from search
            response = self.session.get(f"{self.base_url}/api/v1/search/?q=dental&limit=1", headers=headers)
            if response.status_code == 200:
                search_result = response.json()
                if search_result["results"]:
                    product_id = search_result["results"][0]["id"]
                    
                    response = self.session.get(f"{self.base_url}/api/v1/products/{product_id}", headers=headers)
                    if response.status_code != 200:
                        print(f"  ❌ Get product failed: {response.status_code}")
                        return False
                    
                    product_result = response.json()
                    if "id" not in product_result:
                        print("  ❌ Invalid product response")
                        return False
                    
                    print("  ✅ Individual product endpoint working")
            
        except Exception as e:
            print(f"  ❌ Product endpoints test failed: {e}")
            return False
        
        print("🎉 Product endpoints test PASSED")
        return True
    
    def test_shopping_lists(self):
        """Test shopping list functionality."""
        print("🧪 Testing shopping list functionality...")
        
        if not self.test_user_token:
            print("  ❌ No authentication token available")
            return False
        
        headers = {"Authorization": f"Bearer {self.test_user_token}"}
        
        try:
            # Test create shopping list
            list_data = {
                "name": "API Test List",
                "description": "Created during API testing"
            }
            
            response = self.session.post(f"{self.base_url}/api/v1/shopping-lists/", json=list_data, headers=headers)
            if response.status_code != 201:
                print(f"  ❌ Create shopping list failed: {response.status_code}")
                return False
            
            list_result = response.json()
            if "id" not in list_result:
                print("  ❌ No ID in shopping list response")
                return False
            
            shopping_list_id = list_result["id"]
            print("  ✅ Shopping list creation working")
            
            # Test get shopping lists
            response = self.session.get(f"{self.base_url}/api/v1/shopping-lists/", headers=headers)
            if response.status_code != 200:
                print(f"  ❌ Get shopping lists failed: {response.status_code}")
                return False
            
            lists_result = response.json()
            if "data" not in lists_result or "items" not in lists_result["data"]:
                print("  ❌ Invalid shopping lists response format")
                return False
            
            print("  ✅ Get shopping lists working")
            
            # Test get individual shopping list
            response = self.session.get(f"{self.base_url}/api/v1/shopping-lists/{shopping_list_id}", headers=headers)
            if response.status_code != 200:
                print(f"  ❌ Get shopping list failed: {response.status_code}")
                return False
            
            print("  ✅ Get individual shopping list working")
            
        except Exception as e:
            print(f"  ❌ Shopping lists test failed: {e}")
            return False
        
        print("🎉 Shopping lists test PASSED")
        return True
    
    def run_all_tests(self):
        """Run all API tests."""
        print("🚀 ProfiDent Simple API Tests")
        print("=" * 60)
        
        tests = [
            self.test_health_endpoints,
            self.test_user_registration_and_auth,
            self.test_search_functionality,
            self.test_product_endpoints,
            self.test_shopping_lists,
        ]
        
        passed_tests = 0
        total_tests = len(tests)
        
        for test in tests:
            try:
                if test():
                    passed_tests += 1
                else:
                    print(f"❌ Test {test.__name__} FAILED")
            except Exception as e:
                print(f"❌ Test {test.__name__} FAILED with exception: {e}")
        
        print("\n" + "=" * 60)
        print(f"📊 API TEST RESULTS: {passed_tests}/{total_tests} PASSED")
        
        if passed_tests == total_tests:
            print("🎉 ALL API TESTS PASSED!")
            print("✅ API is working correctly")
        else:
            print(f"⚠️ {total_tests - passed_tests} tests failed")
        
        print("=" * 60)
        
        return passed_tests == total_tests


def main():
    """Main function to run API tests."""
    # Check if server is running - try both localhost and 127.0.0.1
    server_urls = ["http://localhost:8000", "http://127.0.0.1:8000"]
    server_url = None

    for url in server_urls:
        try:
            response = requests.get(f"{url}/api/v1/search/health", timeout=5)
            if response.status_code == 200:
                server_url = url
                break
        except Exception:
            continue

    if not server_url:
        print("❌ API server is not running on localhost:8000 or 127.0.0.1:8000")
        print("   Please start the server with: uvicorn app.main:app --reload")
        return False

    print(f"✅ Found API server running on {server_url}")
    
    tester = SimpleAPITester(server_url)
    return tester.run_all_tests()


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

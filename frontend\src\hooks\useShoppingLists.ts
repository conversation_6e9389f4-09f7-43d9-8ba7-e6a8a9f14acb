import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { useShoppingListStore } from "../stores/shoppingListStore";
import apiClient from "../lib/api";
import {
  ShoppingListCreate,
  ShoppingListUpdate,
  ShoppingListItemCreate,
  ShoppingListItemUpdate,
} from "../types";
import { toast } from "../lib/toast";

// Get all shopping lists
export const useShoppingLists = (page = 1, per_page = 20) => {
  const { setLists, setLoading, setError } = useShoppingListStore();

  return useQuery({
    queryKey: ["shopping-lists", page, per_page],
    queryFn: async () => {
      setLoading(true);
      try {
        const response = await apiClient.getShoppingLists(page, per_page);
        setLists(response.data.items);
        return response;
      } catch (error: any) {
        const errorMessage =
          error.response?.data?.detail || "Failed to load shopping lists";
        setError(errorMessage);
        throw error;
      }
    },
    staleTime: 1000 * 60 * 5, // 5 minutes
    retry: 1,
  });
};

// Get single shopping list
export const useShoppingList = (id: number, include_purchased = true) => {
  const { setCurrentList, setLoading, setError } = useShoppingListStore();

  return useQuery({
    queryKey: ["shopping-list", id, include_purchased],
    queryFn: async () => {
      setLoading(true);
      try {
        const response = await apiClient.getShoppingList(id, include_purchased);
        setCurrentList(response);
        return response;
      } catch (error: any) {
        const errorMessage =
          error.response?.data?.detail || "Failed to load shopping list";
        setError(errorMessage);
        throw error;
      }
    },
    enabled: !!id,
    staleTime: 1000 * 60 * 2, // 2 minutes
    retry: 1,
  });
};

// Create shopping list
export const useCreateShoppingList = () => {
  const queryClient = useQueryClient();
  const { addList } = useShoppingListStore();

  return useMutation({
    mutationFn: (data: ShoppingListCreate) =>
      apiClient.createShoppingList(data),
    onSuccess: (newList) => {
      addList(newList);
      queryClient.invalidateQueries({ queryKey: ["shopping-lists"] });
      toast.success("Shopping list created successfully");
    },
    onError: (error: any) => {
      const errorMessage =
        error.response?.data?.detail || "Failed to create shopping list";
      toast.error(errorMessage);
    },
  });
};

// Update shopping list
export const useUpdateShoppingList = () => {
  const queryClient = useQueryClient();
  const { updateList } = useShoppingListStore();

  return useMutation({
    mutationFn: ({ id, data }: { id: number; data: ShoppingListUpdate }) =>
      apiClient.updateShoppingList(id, data),
    onSuccess: (updatedList) => {
      updateList(updatedList.id, updatedList);
      queryClient.invalidateQueries({ queryKey: ["shopping-lists"] });
      queryClient.invalidateQueries({
        queryKey: ["shopping-list", updatedList.id],
      });
      toast.success("Shopping list updated successfully");
    },
    onError: (error: any) => {
      const errorMessage =
        error.response?.data?.detail || "Failed to update shopping list";
      toast.error(errorMessage);
    },
  });
};

// Delete shopping list
export const useDeleteShoppingList = () => {
  const queryClient = useQueryClient();
  const { removeList } = useShoppingListStore();

  return useMutation({
    mutationFn: (id: number) => apiClient.deleteShoppingList(id),
    onSuccess: (_, id) => {
      removeList(id);
      queryClient.invalidateQueries({ queryKey: ["shopping-lists"] });
      toast.success("Shopping list deleted successfully");
    },
    onError: (error: any) => {
      const errorMessage =
        error.response?.data?.detail || "Failed to delete shopping list";
      toast.error(errorMessage);
    },
  });
};

// Add item to shopping list
export const useAddItemToList = () => {
  const queryClient = useQueryClient();
  const { addItemToCurrentList } = useShoppingListStore();

  return useMutation({
    mutationFn: ({
      listId,
      item,
    }: {
      listId: number;
      item: ShoppingListItemCreate;
    }) => apiClient.addItemToList(listId, item),
    onSuccess: (newItem, { listId }) => {
      addItemToCurrentList(newItem);
      queryClient.invalidateQueries({ queryKey: ["shopping-list", listId] });
      queryClient.invalidateQueries({ queryKey: ["shopping-lists"] });
      toast.success("Item added to shopping list");
    },
    onError: (error: any) => {
      const errorMessage =
        error.response?.data?.detail || "Failed to add item to list";
      toast.error(errorMessage);
    },
  });
};

// Quick-add product to default shopping list
export const useQuickAddProduct = () => {
  const queryClient = useQueryClient();
  const { startProductAnimation, endProductAnimation, setLoading, setError } =
    useShoppingListStore();

  return useMutation({
    mutationFn: ({
      productId,
      quantity = 1,
    }: {
      productId: number;
      quantity?: number;
      product?: any;
    }) => apiClient.quickAddProduct(productId, quantity),
    onMutate: ({ productId, product }) => {
      setLoading(true);
      setError(null);
      // Start animation with the provided product object
      if (product) {
        startProductAnimation(product);
      }
    },
    onSuccess: (result, { productId }) => {
      // Invalidate and refetch shopping lists to update counts
      queryClient.invalidateQueries({ queryKey: ["shopping-lists"] });
      queryClient.invalidateQueries({ queryKey: ["shopping-list"] });

      // End animation after a delay to show the effect
      setTimeout(() => {
        endProductAnimation();
      }, 600);
    },
    onError: (error: any, { productId }) => {
      console.error("Error adding product to shopping list:", error);
      const errorMessage =
        error.response?.data?.detail || "Failed to add product";
      setError(errorMessage);
      endProductAnimation();
    },
    onSettled: () => {
      setLoading(false);
    },
  });
};

// Update shopping list item
export const useUpdateListItem = () => {
  const queryClient = useQueryClient();
  const { updateItemInCurrentList } = useShoppingListStore();

  return useMutation({
    mutationFn: ({
      itemId,
      data,
    }: {
      itemId: number;
      data: ShoppingListItemUpdate;
    }) => apiClient.updateListItem(itemId, data),
    onSuccess: (updatedItem) => {
      updateItemInCurrentList(updatedItem.id, updatedItem);
      queryClient.invalidateQueries({ queryKey: ["shopping-list"] });
      toast.success("Item updated successfully");
    },
    onError: (error: any) => {
      const errorMessage =
        error.response?.data?.detail || "Failed to update item";
      toast.error(errorMessage);
    },
  });
};

// Remove item from shopping list
export const useRemoveItemFromList = () => {
  const queryClient = useQueryClient();
  const { removeItemFromCurrentList } = useShoppingListStore();

  return useMutation({
    mutationFn: ({ listId, itemId }: { listId: number; itemId: number }) =>
      apiClient.removeItemFromList(listId, itemId),
    onSuccess: (_, { listId, itemId }) => {
      removeItemFromCurrentList(itemId);
      queryClient.invalidateQueries({ queryKey: ["shopping-list", listId] });
      queryClient.invalidateQueries({ queryKey: ["shopping-lists"] });
      toast.success("Item removed from shopping list");
    },
    onError: (error: any) => {
      const errorMessage =
        error.response?.data?.detail || "Failed to remove item";
      toast.error(errorMessage);
    },
  });
};

// Get shopping list analysis
export const useShoppingListAnalysis = (listId: number) => {
  return useQuery({
    queryKey: ["shopping-list-analysis", listId],
    queryFn: () => apiClient.getShoppingListAnalysis(listId),
    enabled: !!listId,
    staleTime: 1000 * 60 * 5, // 5 minutes
    retry: 1,
  });
};

// Bulk operations
export const useBulkAddToList = () => {
  const queryClient = useQueryClient();
  const { addItemToCurrentList } = useShoppingListStore();

  return useMutation({
    mutationFn: async ({
      listId,
      items,
    }: {
      listId: number;
      items: ShoppingListItemCreate[];
    }) => {
      const results = await Promise.allSettled(
        items.map((item) => apiClient.addItemToList(listId, item))
      );
      return results;
    },
    onSuccess: (results, { listId }) => {
      const successCount = results.filter(
        (result) => result.status === "fulfilled"
      ).length;
      const failCount = results.length - successCount;

      queryClient.invalidateQueries({ queryKey: ["shopping-list", listId] });
      queryClient.invalidateQueries({ queryKey: ["shopping-lists"] });

      if (successCount > 0) {
        toast.success(`${successCount} items added to shopping list`);
      }
      if (failCount > 0) {
        toast.error(`Failed to add ${failCount} items`);
      }
    },
    onError: () => {
      toast.error("Failed to add items to shopping list");
    },
  });
};

// Custom hook for shopping list management
export const useShoppingListManager = (listId?: number) => {
  const { currentList, selectedItems, clearSelection } = useShoppingListStore();
  const createMutation = useCreateShoppingList();
  const updateMutation = useUpdateShoppingList();
  const deleteMutation = useDeleteShoppingList();
  const addItemMutation = useAddItemToList();
  const removeItemMutation = useRemoveItemFromList();

  const isLoading =
    createMutation.isPending ||
    updateMutation.isPending ||
    deleteMutation.isPending ||
    addItemMutation.isPending ||
    removeItemMutation.isPending;

  const removeSelectedItems = async () => {
    if (!currentList || selectedItems.size === 0) return;

    const promises = Array.from(selectedItems).map((itemId) =>
      removeItemMutation.mutateAsync({ listId: currentList.id, itemId })
    );

    try {
      await Promise.all(promises);
      clearSelection();
      toast.success(`${selectedItems.size} items removed`);
    } catch (error) {
      toast.error("Failed to remove some items");
    }
  };

  return {
    currentList,
    selectedItems,
    isLoading,
    createList: createMutation.mutate,
    updateList: updateMutation.mutate,
    deleteList: deleteMutation.mutate,
    addItem: addItemMutation.mutate,
    removeItem: removeItemMutation.mutate,
    removeSelectedItems,
    clearSelection,
  };
};

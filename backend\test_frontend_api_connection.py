#!/usr/bin/env python3
"""
Test Frontend-API Connection
Verifies that the frontend can connect to the API and search works
"""

import requests
import time


def test_frontend_api_connection():
    """Test that frontend can connect to API."""
    print("🧪 Testing Frontend-API Connection...")
    
    api_base_url = "http://localhost:8000"
    
    # Test 1: Health check
    try:
        response = requests.get(f"{api_base_url}/health")
        if response.status_code == 200:
            print("  ✅ API health check: OK")
        else:
            print(f"  ❌ API health check failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"  ❌ API connection failed: {e}")
        return False
    
    # Test 2: Register/Login test user
    try:
        # Try to register
        register_data = {
            "email": "<EMAIL>",
            "password": "password123",
            "full_name": "Frontend Test User"
        }
        
        response = requests.post(f"{api_base_url}/api/v1/auth/register", json=register_data)
        
        if response.status_code == 201:
            token = response.json()["token"]["access_token"]
            print("  ✅ User registration: OK")
        elif response.status_code == 400:
            # User exists, try login
            login_data = {
                "email": "<EMAIL>",
                "password": "password123"
            }
            response = requests.post(f"{api_base_url}/api/v1/auth/login", json=login_data)
            if response.status_code == 200:
                token = response.json()["token"]["access_token"]
                print("  ✅ User login: OK")
            else:
                print(f"  ❌ Login failed: {response.status_code}")
                return False
        else:
            print(f"  ❌ Registration failed: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"  ❌ Authentication failed: {e}")
        return False
    
    # Test 3: Search functionality
    headers = {"Authorization": f"Bearer {token}"}
    
    try:
        # Test search for "composite"
        response = requests.get(
            f"{api_base_url}/api/v1/search/",
            params={"q": "composite", "limit": 5},
            headers=headers
        )
        
        if response.status_code == 200:
            result = response.json()
            print(f"  ✅ Search 'composite': {result['total']} results found")
            
            if result['results']:
                sample = result['results'][0]
                print(f"    📄 Sample result: {sample['name'][:60]}...")
            
        else:
            print(f"  ❌ Search failed: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"  ❌ Search test failed: {e}")
        return False
    
    # Test 4: Search suggestions
    try:
        response = requests.get(
            f"{api_base_url}/api/v1/search/suggestions",
            params={"q": "comp"},
            headers=headers
        )
        
        if response.status_code == 200:
            suggestions = response.json()["suggestions"]
            print(f"  ✅ Search suggestions: {len(suggestions)} suggestions for 'comp'")
        else:
            print(f"  ❌ Suggestions failed: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"  ❌ Suggestions test failed: {e}")
        return False
    
    print("🎉 Frontend-API connection test PASSED")
    print("\n📋 Frontend Testing Instructions:")
    print("1. Open http://localhost:3000 in your browser")
    print("2. Login with: <EMAIL> / password123")
    print("3. Try searching for: 'composite', 'dental', 'implant', 'crown'")
    print("4. Verify search suggestions appear as you type")
    print("5. Check that search results are displayed properly")
    
    return True


if __name__ == "__main__":
    success = test_frontend_api_connection()
    if success:
        print("\n✅ All API tests passed! Frontend should be working.")
    else:
        print("\n❌ API tests failed! Check server status.")

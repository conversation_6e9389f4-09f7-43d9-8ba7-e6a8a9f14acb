"""
Shopping list service for CRUD operations and business logic
"""

import json
from typing import List, Dict, Any, Optional, Tu<PERSON>
from asyncpg import Connection
import redis.asyncio as redis

try:
    from ..models.shopping_list import (
        ShoppingList, ShoppingListItem, 
        ShoppingListRepository, ShoppingListItemRepository
    )
    from ..models.product import ProductRepository
    from ..schemas.shopping_list import (
        ShoppingListCreate, ShoppingListUpdate, ShoppingListResponse,
        ShoppingListItemCreate, ShoppingListItemUpdate, ShoppingListItemResponse,
        ShoppingListDetailResponse, ShoppingListSummary, ShoppingListPriceAnalysis
    )
    from ..config import settings
except ImportError:
    from models.shopping_list import (
        ShoppingList, ShoppingListItem, 
        ShoppingListRepository, ShoppingListItemRepository
    )
    from models.product import ProductRepository
    from schemas.shopping_list import (
        ShoppingListCreate, ShoppingListUpdate, ShoppingListResponse,
        ShoppingListItemCreate, ShoppingListItemUpdate, ShoppingListItemResponse,
        ShoppingListDetailResponse, ShoppingListSummary, ShoppingListPriceAnalysis
    )
    from config import settings


class ShoppingListService:
    """Service for shopping list operations."""
    
    @staticmethod
    async def create_shopping_list(
        conn: Connection,
        user_id: str,
        list_data: ShoppingListCreate
    ) -> ShoppingListResponse:
        """Create a new shopping list."""
        shopping_list = await ShoppingListRepository.create_shopping_list(
            conn, user_id, list_data.name, list_data.description, list_data.is_default
        )

        return ShoppingListResponse(
            id=shopping_list.id,
            user_id=shopping_list.user_id,
            name=shopping_list.name,
            description=shopping_list.description,
            is_default=shopping_list.is_default,
            created_at=shopping_list.created_at,
            updated_at=shopping_list.updated_at
        )
    
    @staticmethod
    async def get_user_shopping_lists(
        conn: Connection,
        redis_client: redis.Redis,
        user_id: str,
        limit: int = 50,
        offset: int = 0
    ) -> Tuple[List[ShoppingListResponse], int]:
        """Get user's shopping lists with caching."""
        cache_key = f"user_lists:{user_id}:{limit}:{offset}"
        
        # Try cache first
        try:
            cached_result = await redis_client.get(cache_key)
            if cached_result:
                cached_data = json.loads(cached_result)
                lists = [ShoppingListResponse(**item) for item in cached_data["lists"]]
                return lists, cached_data["total"]
        except Exception:
            pass
        
        # Get from database
        shopping_lists, total = await ShoppingListRepository.get_user_shopping_lists(
            conn, user_id, limit, offset
        )
        
        # Convert to response format
        list_responses = []
        for shopping_list in shopping_lists:
            response = ShoppingListResponse(
                id=shopping_list.id,
                user_id=shopping_list.user_id,
                name=shopping_list.name,
                description=shopping_list.description,
                is_default=shopping_list.is_default,
                created_at=shopping_list.created_at,
                updated_at=shopping_list.updated_at
            )
            list_responses.append(response)
        
        # Cache the result
        try:
            cache_data = {
                "lists": [item.dict() for item in list_responses],
                "total": total
            }
            await redis_client.setex(cache_key, 300, json.dumps(cache_data, default=str))  # 5 minutes
        except Exception:
            pass
        
        return list_responses, total
    
    @staticmethod
    async def get_shopping_list_detail(
        conn: Connection,
        redis_client: redis.Redis,
        list_id: int,
        user_id: str,
        include_purchased: bool = True
    ) -> Optional[ShoppingListDetailResponse]:
        """Get detailed shopping list with items."""
        cache_key = f"list_detail:{list_id}:{include_purchased}"
        
        # Try cache first
        try:
            cached_result = await redis_client.get(cache_key)
            if cached_result:
                cached_data = json.loads(cached_result)
                return ShoppingListDetailResponse(**cached_data)
        except Exception:
            pass
        
        # Get shopping list
        shopping_list = await ShoppingListRepository.get_shopping_list_by_id(conn, list_id, user_id)
        if not shopping_list:
            return None
        
        # Get items
        items_data = await ShoppingListItemRepository.get_list_items(conn, list_id, include_purchased)
        
        # Get summary
        summary_data = await ShoppingListItemRepository.get_list_summary(conn, list_id)
        
        # Convert to response format
        items = []
        for item_data in items_data:
            item_response = ShoppingListItemResponse(
                id=item_data["id"],
                shopping_list_id=item_data["shopping_list_id"],
                product_id=item_data["product_id"],
                quantity=item_data["quantity"],
                notes=item_data["notes"],
                added_at=item_data["added_at"],
                product=item_data["product"]
            )
            items.append(item_response)
        
        summary = ShoppingListSummary(**summary_data)
        
        detail_response = ShoppingListDetailResponse(
            id=shopping_list.id,
            user_id=shopping_list.user_id,
            name=shopping_list.name,
            description=shopping_list.description,
            created_at=shopping_list.created_at,
            updated_at=shopping_list.updated_at,
            items=items,
            summary=summary
        )
        
        # Cache the result
        try:
            await redis_client.setex(cache_key, 300, json.dumps(detail_response.dict(), default=str))  # 5 minutes
        except Exception:
            pass
        
        return detail_response
    
    @staticmethod
    async def update_shopping_list(
        conn: Connection,
        redis_client: redis.Redis,
        list_id: int,
        user_id: str,
        update_data: ShoppingListUpdate
    ) -> Optional[ShoppingListResponse]:
        """Update shopping list."""
        shopping_list = await ShoppingListRepository.update_shopping_list(
            conn, list_id, user_id,
            update_data.name, update_data.description, update_data.is_default
        )

        if not shopping_list:
            return None

        # Clear cache
        try:
            await redis_client.delete(f"list_detail:{list_id}:*")
            await redis_client.delete(f"user_lists:{user_id}:*")
        except Exception:
            pass

        return ShoppingListResponse(
            id=shopping_list.id,
            user_id=shopping_list.user_id,
            name=shopping_list.name,
            description=shopping_list.description,
            is_default=shopping_list.is_default,
            created_at=shopping_list.created_at,
            updated_at=shopping_list.updated_at
        )
    
    @staticmethod
    async def delete_shopping_list(
        conn: Connection,
        redis_client: redis.Redis,
        list_id: int,
        user_id: str
    ) -> bool:
        """Delete shopping list."""
        success = await ShoppingListRepository.delete_shopping_list(conn, list_id, user_id)
        
        if success:
            # Clear cache
            try:
                await redis_client.delete(f"list_detail:{list_id}:*")
                await redis_client.delete(f"user_lists:{user_id}:*")
            except Exception:
                pass
        
        return success
    
    @staticmethod
    async def add_item_to_list(
        conn: Connection,
        redis_client: redis.Redis,
        list_id: int,
        user_id: str,
        item_data: ShoppingListItemCreate
    ) -> Optional[ShoppingListItemResponse]:
        """Add item to shopping list."""
        # Verify list ownership
        shopping_list = await ShoppingListRepository.get_shopping_list_by_id(conn, list_id, user_id)
        if not shopping_list:
            return None
        
        # Verify product exists
        product = await ProductRepository.get_product_by_id(conn, item_data.product_id)
        if not product:
            return None
        
        # Add item
        item = await ShoppingListItemRepository.add_item_to_list(
            conn, list_id, item_data.product_id, item_data.quantity, item_data.notes
        )
        
        # Clear cache
        try:
            await redis_client.delete(f"list_detail:{list_id}:*")
        except Exception:
            pass
        
        return ShoppingListItemResponse(
            id=item.id,
            shopping_list_id=item.shopping_list_id,
            product_id=item.product_id,
            quantity=item.quantity,
            notes=item.notes,
            added_at=item.added_at
        )
    
    @staticmethod
    async def update_list_item(
        conn: Connection,
        redis_client: redis.Redis,
        item_id: int,
        update_data: ShoppingListItemUpdate
    ) -> Optional[ShoppingListItemResponse]:
        """Update shopping list item."""
        item = await ShoppingListItemRepository.update_list_item(
            conn, item_id, update_data.quantity, update_data.notes
        )
        
        if not item:
            return None
        
        # Clear cache
        try:
            await redis_client.delete(f"list_detail:{item.shopping_list_id}:*")
        except Exception:
            pass
        
        return ShoppingListItemResponse(
            id=item.id,
            shopping_list_id=item.shopping_list_id,
            product_id=item.product_id,
            quantity=item.quantity,
            notes=item.notes,
            added_at=item.added_at
        )
    
    @staticmethod
    async def remove_item_from_list(
        conn: Connection,
        redis_client: redis.Redis,
        item_id: int,
        list_id: int
    ) -> bool:
        """Remove item from shopping list."""
        success = await ShoppingListItemRepository.remove_item_from_list(conn, item_id)
        
        if success:
            # Clear cache
            try:
                await redis_client.delete(f"list_detail:{list_id}:*")
            except Exception:
                pass
        
        return success
    
    @staticmethod
    async def analyze_shopping_list_prices(
        conn: Connection,
        redis_client: redis.Redis,
        list_id: int,
        user_id: str
    ) -> Optional[ShoppingListPriceAnalysis]:
        """Analyze shopping list prices and potential savings."""
        cache_key = f"list_analysis:{list_id}"
        
        # Try cache first
        try:
            cached_result = await redis_client.get(cache_key)
            if cached_result:
                cached_data = json.loads(cached_result)
                return ShoppingListPriceAnalysis(**cached_data)
        except Exception:
            pass
        
        # Verify list ownership
        shopping_list = await ShoppingListRepository.get_shopping_list_by_id(conn, list_id, user_id)
        if not shopping_list:
            return None
        
        # Get items with product details
        items_data = await ShoppingListItemRepository.get_list_items(conn, list_id, include_purchased=False)
        
        total_estimated_cost = 0.0
        items_with_prices = 0
        items_without_prices = 0
        seller_breakdown = {}
        
        for item in items_data:
            product = item["product"]
            quantity = item["quantity"]
            
            if product["price"]:
                try:
                    # Parse price
                    price_str = product["price"].replace("$", "").replace(",", "").strip()
                    if price_str and price_str.replace(".", "").isdigit():
                        price = float(price_str)
                        item_total = price * quantity
                        total_estimated_cost += item_total
                        items_with_prices += 1
                        
                        # Track by seller
                        seller = product["seller"]
                        if seller not in seller_breakdown:
                            seller_breakdown[seller] = {
                                "items": 0,
                                "total_cost": 0.0,
                                "products": []
                            }
                        
                        seller_breakdown[seller]["items"] += 1
                        seller_breakdown[seller]["total_cost"] += item_total
                        seller_breakdown[seller]["products"].append({
                            "name": product["name"],
                            "quantity": quantity,
                            "unit_price": price,
                            "total_price": item_total
                        })
                    else:
                        items_without_prices += 1
                except (ValueError, AttributeError):
                    items_without_prices += 1
            else:
                items_without_prices += 1
        
        analysis = ShoppingListPriceAnalysis(
            total_estimated_cost=round(total_estimated_cost, 2),
            items_with_prices=items_with_prices,
            items_without_prices=items_without_prices,
            price_breakdown_by_seller=seller_breakdown
        )
        
        # Cache the analysis
        try:
            await redis_client.setex(cache_key, 1800, json.dumps(analysis.dict(), default=str))  # 30 minutes
        except Exception:
            pass

        return analysis

    @staticmethod
    async def quick_add_product_to_default_list(
        conn: Connection,
        redis_client: redis.Redis,
        user_id: str,
        product_id: int,
        quantity: int = 1
    ) -> Optional[ShoppingListItemResponse]:
        """
        Quick-add a product to the user's default shopping list.
        Creates a default list if none exists.
        """
        # Get or create default shopping list
        default_list = await ShoppingListService._get_or_create_default_list(conn, user_id)

        # Verify product exists
        product = await ProductRepository.get_product_by_id(conn, product_id)
        if not product:
            return None

        # Add item to the default list
        item_data = ShoppingListItemCreate(
            product_id=product_id,
            quantity=quantity,
            notes=None
        )

        item = await ShoppingListService.add_item_to_list(
            conn, redis_client, default_list.id, user_id, item_data
        )

        return item

    @staticmethod
    async def _get_or_create_default_list(
        conn: Connection,
        user_id: str
    ) -> ShoppingListResponse:
        """
        Get the user's default shopping list or create one if it doesn't exist.
        """
        # Try to get existing default list
        default_list = await ShoppingListRepository.get_default_shopping_list(conn, user_id)

        if default_list:
            return ShoppingListResponse(
                id=default_list.id,
                user_id=default_list.user_id,
                name=default_list.name,
                description=default_list.description,
                is_default=default_list.is_default,
                created_at=default_list.created_at,
                updated_at=default_list.updated_at
            )

        # If no default list exists, check if user has any lists and make the first one default
        existing_lists, _ = await ShoppingListRepository.get_user_shopping_lists(
            conn, user_id, limit=1, offset=0
        )

        if existing_lists:
            # Make the first list the default
            updated_list = await ShoppingListRepository.update_shopping_list(
                conn, existing_lists[0].id, user_id, is_default=True
            )

            return ShoppingListResponse(
                id=updated_list.id,
                user_id=updated_list.user_id,
                name=updated_list.name,
                description=updated_list.description,
                is_default=updated_list.is_default,
                created_at=updated_list.created_at,
                updated_at=updated_list.updated_at
            )

        # Create a new default shopping list
        default_list = await ShoppingListRepository.create_shopping_list(
            conn, user_id, "My Shopping List", "Default shopping list for quick additions", is_default=True
        )

        return ShoppingListResponse(
            id=default_list.id,
            user_id=default_list.user_id,
            name=default_list.name,
            description=default_list.description,
            is_default=default_list.is_default,
            created_at=default_list.created_at,
            updated_at=default_list.updated_at
        )

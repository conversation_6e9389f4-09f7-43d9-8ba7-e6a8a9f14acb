#!/usr/bin/env python3
"""
Simple test script for FastAPI application structure
Tests basic functionality without complex lifespan management
"""

import sys
from pathlib import Path

# Add the app directory to Python path
sys.path.insert(0, str(Path(__file__).parent / "app"))

def test_app_import():
    """Test that the app can be imported."""
    print("🔍 Testing app import...")
    
    try:
        from app.main import app
        from app.config import settings
        
        print(f"✅ App imported successfully: {app.title}")
        print(f"✅ Settings loaded: {settings.PROJECT_NAME}")
        return True, app
        
    except Exception as e:
        print(f"❌ App import failed: {e}")
        return False, None


def test_basic_routes():
    """Test basic routes without database."""
    print("\n🔍 Testing basic routes...")
    
    try:
        from fastapi.testclient import TestClient
        from app.main import app
        
        # Create test client without lifespan events
        with TestClient(app) as client:
            # Test root endpoint
            response = client.get("/")
            if response.status_code == 200:
                data = response.json()
                print(f"✅ Root endpoint: {data['message']}")
            else:
                print(f"❌ Root endpoint failed: {response.status_code} - {response.text}")
                return False
            
            # Test API info endpoint
            response = client.get("/api/v1/info")
            if response.status_code == 200:
                data = response.json()
                print(f"✅ API info endpoint: {data['name']}")
            else:
                print(f"❌ API info endpoint failed: {response.status_code} - {response.text}")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ Basic routes test failed: {e}")
        return False


def test_auth_routes():
    """Test authentication routes structure."""
    print("\n🔍 Testing authentication routes...")
    
    try:
        from fastapi.testclient import TestClient
        from app.main import app
        
        with TestClient(app) as client:
            # Test auth health endpoint (should work without database)
            response = client.get("/api/v1/auth/health")
            if response.status_code == 200:
                data = response.json()
                print(f"✅ Auth health endpoint: {data['message']}")
            else:
                print(f"❌ Auth health endpoint failed: {response.status_code}")
                return False
            
            # Test auth register endpoint structure (should return validation error)
            response = client.post("/api/v1/auth/register", json={})
            if response.status_code == 422:  # Validation error expected
                print("✅ Auth register endpoint structure working (validation error expected)")
            else:
                print(f"⚠️ Auth register endpoint: {response.status_code} (expected 422)")
        
        return True
        
    except Exception as e:
        print(f"❌ Auth routes test failed: {e}")
        return False


def test_openapi_generation():
    """Test OpenAPI specification generation."""
    print("\n🔍 Testing OpenAPI generation...")
    
    try:
        from fastapi.testclient import TestClient
        from app.main import app
        
        with TestClient(app) as client:
            # Test OpenAPI JSON generation
            response = client.get("/api/v1/openapi.json")
            if response.status_code == 200:
                openapi_spec = response.json()
                print(f"✅ OpenAPI spec generated: {openapi_spec['info']['title']}")
                
                # Check paths
                paths = openapi_spec.get("paths", {})
                print(f"✅ API endpoints documented: {len(paths)} endpoints")
                
                # Check auth endpoints
                auth_paths = [path for path in paths.keys() if "/auth/" in path]
                print(f"✅ Auth endpoints documented: {len(auth_paths)} endpoints")
                
            else:
                print(f"❌ OpenAPI generation failed: {response.status_code}")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ OpenAPI generation test failed: {e}")
        return False


def test_middleware_setup():
    """Test middleware configuration."""
    print("\n🔍 Testing middleware setup...")
    
    try:
        from app.main import app
        
        # Check middleware stack
        middleware_types = [type(middleware).__name__ for middleware in app.user_middleware]
        print(f"✅ Middleware configured: {middleware_types}")
        
        # Check for CORS middleware
        if any("CORS" in name for name in middleware_types):
            print("✅ CORS middleware configured")
        else:
            print("⚠️ CORS middleware not found")
        
        return True
        
    except Exception as e:
        print(f"❌ Middleware setup test failed: {e}")
        return False


def test_dependencies():
    """Test dependencies module."""
    print("\n🔍 Testing dependencies module...")

    try:
        from app.dependencies import (
            create_response_model, create_error_response, create_paginated_response
        )

        # Test response helpers
        success_response = create_response_model({"test": "data"}, "Test message")
        print(f"✅ Success response: {success_response['success']}")

        error_response = create_error_response("Test error")
        print(f"✅ Error response: {error_response['success']}")

        # Test paginated response
        paginated_response = create_paginated_response(
            items=[{"id": 1}, {"id": 2}],
            total=10,
            page=1,
            per_page=2
        )
        print(f"✅ Paginated response: {paginated_response['data']['pagination']['total']} total items")

        return True

    except Exception as e:
        print(f"❌ Dependencies test failed: {e}")
        return False


def main():
    """Run all FastAPI structure tests."""
    print("🚀 Starting ProfiDent FastAPI Structure Tests\n")
    print("=" * 60)
    
    tests = [
        ("App Import", test_app_import),
        ("Basic Routes", test_basic_routes),
        ("Auth Routes", test_auth_routes),
        ("OpenAPI Generation", test_openapi_generation),
        ("Middleware Setup", test_middleware_setup),
        ("Dependencies", test_dependencies),
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            if test_name == "App Import":
                result, app = test_func()
            else:
                result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} failed with exception: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 TEST SUMMARY")
    print("=" * 60)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{status} - {test_name}")
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All FastAPI structure tests passed!")
        print("✅ FastAPI application structure is working correctly")
        return True
    else:
        print("❌ Some tests failed. Check FastAPI application structure.")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

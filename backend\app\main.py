"""
ProfiDent FastAPI Application
Main application entry point with routing and middleware configuration
"""

import logging
from contextlib import asynccontextmanager
from fastapi import <PERSON><PERSON><PERSON>, Request, status
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware
from .middleware.rate_limiting import RateLimitMiddleware, initialize_rate_limiter
from fastapi.responses import JSONResponse
from fastapi.exceptions import RequestValidationError
from starlette.exceptions import HTTPException as StarletteHTTPException
import time

try:
    from .config import settings
    from .database import startup_database, shutdown_database
    from .routers import auth, search, products, shopping_lists
except ImportError:
    from config import settings
    from database import startup_database, shutdown_database
    from routers import auth, search, products, shopping_lists

# Configure logging
logging.basicConfig(
    level=getattr(logging, settings.LOG_LEVEL),
    format=settings.LOG_FORMAT
)
logger = logging.getLogger(__name__)


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan manager."""
    # Startup
    logger.info("Starting ProfiDent API...")
    try:
        await startup_database()
        logger.info("Database connections initialized")

        # Initialize rate limiter with Redis client
        from .database import get_redis_client
        redis_client = await get_redis_client()
        initialize_rate_limiter(redis_client)
        logger.info("Rate limiting initialized")

        # Initialize account lockout manager
        from .utils.account_lockout import initialize_lockout_manager
        initialize_lockout_manager(redis_client)
        logger.info("Account lockout manager initialized")

        # Initialize audit logger
        from .utils.audit_logger import initialize_audit_logger
        initialize_audit_logger(redis_client)
        logger.info("Audit logger initialized")

    except Exception as e:
        logger.error(f"Failed to start application: {e}")
        # Don't raise in lifespan, just log the error
        logger.warning("Continuing without database connection")

    yield

    # Shutdown
    logger.info("Shutting down ProfiDent API...")
    try:
        await shutdown_database()
        logger.info("Database connections closed")
    except Exception as e:
        logger.error(f"Error during shutdown: {e}")


# Create FastAPI application
app = FastAPI(
    title=settings.PROJECT_NAME,
    version=settings.PROJECT_VERSION,
    description=settings.DESCRIPTION,
    openapi_url=f"{settings.API_V1_STR}/openapi.json",
    docs_url=f"{settings.API_V1_STR}/docs",
    redoc_url=f"{settings.API_V1_STR}/redoc",
    lifespan=lifespan
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.BACKEND_CORS_ORIGINS,
    allow_credentials=True,
    allow_methods=["GET", "POST", "PUT", "DELETE", "OPTIONS"],
    allow_headers=["*"],
)

# Add trusted host middleware for security (disabled in debug/testing)
if not settings.DEBUG and not settings.TESTING:
    app.add_middleware(
        TrustedHostMiddleware,
        allowed_hosts=["localhost", "127.0.0.1", "*.profident.com"]
    )

# Add rate limiting middleware using proper BaseHTTPMiddleware
app.add_middleware(RateLimitMiddleware)

# Add CSRF protection middleware
from .middleware.csrf_middleware import default_csrf_middleware
app.add_middleware(default_csrf_middleware)


# Request timing middleware
# @app.middleware("http")
# async def add_process_time_header(request: Request, call_next):
#     """Add processing time header to responses."""
#     start_time = time.time()
#     response = await call_next(request)
#     process_time = time.time() - start_time
#     response.headers["X-Process-Time"] = str(process_time)
#     return response


# Request logging middleware
# @app.middleware("http")
# async def log_requests(request: Request, call_next):
#     """Log all requests."""
#     start_time = time.time()
#
#     # Log request
#     logger.info(f"Request: {request.method} {request.url}")
#
#     response = await call_next(request)
#
#     # Log response
#     process_time = time.time() - start_time
#     logger.info(
#         f"Response: {response.status_code} - {process_time:.3f}s - "
#         f"{request.method} {request.url}"
#     )
#
#     return response


# Exception handlers
@app.exception_handler(StarletteHTTPException)
async def http_exception_handler(request: Request, exc: StarletteHTTPException):
    """Handle HTTP exceptions."""
    logger.error(f"HTTP {exc.status_code}: {exc.detail} - {request.method} {request.url}")
    
    return JSONResponse(
        status_code=exc.status_code,
        content={
            "success": False,
            "message": exc.detail,
            "error_code": f"HTTP_{exc.status_code}",
            "path": str(request.url)
        }
    )


@app.exception_handler(RequestValidationError)
async def validation_exception_handler(request: Request, exc: RequestValidationError):
    """Handle request validation errors."""
    logger.error(f"Validation error: {exc.errors()} - {request.method} {request.url}")
    
    return JSONResponse(
        status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
        content={
            "success": False,
            "message": "Validation error",
            "errors": exc.errors(),
            "error_code": "VALIDATION_ERROR",
            "path": str(request.url)
        }
    )


@app.exception_handler(Exception)
async def general_exception_handler(request: Request, exc: Exception):
    """Handle general exceptions."""
    logger.error(f"Unhandled exception: {exc} - {request.method} {request.url}", exc_info=True)
    
    return JSONResponse(
        status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
        content={
            "success": False,
            "message": "Internal server error",
            "error_code": "INTERNAL_ERROR",
            "path": str(request.url)
        }
    )


# Include routers - Order matters! More specific routes first
app.include_router(auth.router, prefix=settings.API_V1_STR)
app.include_router(search.router, prefix=settings.API_V1_STR)
app.include_router(shopping_lists.router, prefix=settings.API_V1_STR)

# RBAC Management routers
from .routers import roles, permissions, user_roles, admin
app.include_router(roles.router, prefix=settings.API_V1_STR)
app.include_router(permissions.router, prefix=settings.API_V1_STR)
app.include_router(user_roles.router, prefix=settings.API_V1_STR)
app.include_router(admin.router, prefix=settings.API_V1_STR)

app.include_router(products.router, prefix=settings.API_V1_STR)  # Products last due to catch-all /{product_id}


# Root endpoint
@app.get("/")
async def root():
    """Root endpoint with API information."""
    return {
        "message": "Welcome to ProfiDent API",
        "version": settings.PROJECT_VERSION,
        "description": settings.DESCRIPTION,
        "docs_url": f"{settings.API_V1_STR}/docs",
        "status": "healthy"
    }


# Health check endpoint
@app.get("/health")
async def health_check():
    """Health check endpoint."""
    try:
        from .database import db_manager

        # Check if database manager is initialized
        if not db_manager._is_initialized:
            return {
                "status": "starting",
                "version": settings.PROJECT_VERSION,
                "services": {
                    "postgres": {"status": "not_initialized", "error": None},
                    "redis": {"status": "not_initialized", "error": None}
                },
                "timestamp": time.time()
            }

        # Check database health
        health_status = await db_manager.health_check()

        # Check overall health
        all_healthy = all(
            service["status"] == "healthy"
            for service in health_status.values()
        )

        return {
            "status": "healthy" if all_healthy else "unhealthy",
            "version": settings.PROJECT_VERSION,
            "services": health_status,
            "timestamp": time.time()
        }

    except Exception as e:
        logger.error(f"Health check failed: {e}")
        return JSONResponse(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            content={
                "status": "unhealthy",
                "error": str(e),
                "timestamp": time.time()
            }
        )


# API information endpoint
@app.get(f"{settings.API_V1_STR}/info")
async def api_info():
    """API information endpoint."""
    return {
        "name": settings.PROJECT_NAME,
        "version": settings.PROJECT_VERSION,
        "description": settings.DESCRIPTION,
        "api_version": "v1",
        "endpoints": {
            "authentication": f"{settings.API_V1_STR}/auth",
            "products": f"{settings.API_V1_STR}/products",
            "shopping_lists": f"{settings.API_V1_STR}/shopping-lists",
            "health": "/health",
            "docs": f"{settings.API_V1_STR}/docs"
        },
        "features": [
            "JWT Authentication",
            "Product Search",
            "Price Comparison",
            "Shopping Lists",
            "Full-Text Search",
            "Vector Similarity Search (Phase 3)"
        ]
    }


if __name__ == "__main__":
    import uvicorn
    
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8000,
        reload=settings.DEBUG,
        log_level=settings.LOG_LEVEL.lower()
    )

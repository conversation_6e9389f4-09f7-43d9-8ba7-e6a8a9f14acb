#!/usr/bin/env python3
"""
CSRF protection middleware for FastAPI.

Provides automatic CSRF token validation for sensitive endpoints.
"""

import time
from typing import List, Set
from fastapi import Request, Response, HTTPException, status
from starlette.middleware.base import BaseHTTPMiddleware
from ..utils.csrf import csrf_protection


class CSRFMiddleware(BaseHTTPMiddleware):
    """
    CSRF protection middleware.
    
    Automatically validates CSRF tokens for configured endpoints and methods.
    """
    
    def __init__(
        self,
        app,
        protected_paths: List[str] = None,
        protected_methods: Set[str] = None,
        exempt_paths: List[str] = None
    ):
        """
        Initialize CSRF middleware.
        
        Args:
            app: FastAPI application
            protected_paths: List of path patterns that require CSRF protection
            protected_methods: Set of HTTP methods that require CSRF protection
            exempt_paths: List of path patterns exempt from CSRF protection
        """
        super().__init__(app)
        
        # Default protected paths (authentication endpoints)
        self.protected_paths = protected_paths or [
            "/api/v1/auth/register",
            "/api/v1/auth/login",
            "/api/v1/auth/logout",
            "/api/v1/auth/change-password",
            "/api/v1/auth/reset-password"
        ]
        
        # Default protected methods
        self.protected_methods = protected_methods or {"POST", "PUT", "PATCH", "DELETE"}
        
        # Default exempt paths
        self.exempt_paths = exempt_paths or [
            "/api/v1/auth/csrf-token",
            "/api/v1/search",  # Search endpoints are read-only
            "/docs",
            "/redoc",
            "/openapi.json"
        ]
    
    def _is_path_protected(self, path: str) -> bool:
        """Check if a path requires CSRF protection."""
        # Check if path is explicitly exempted
        for exempt_path in self.exempt_paths:
            if path.startswith(exempt_path):
                return False
        
        # Check if path is in protected paths
        for protected_path in self.protected_paths:
            if path.startswith(protected_path):
                return True
        
        return False
    
    def _is_method_protected(self, method: str) -> bool:
        """Check if an HTTP method requires CSRF protection."""
        return method.upper() in self.protected_methods
    
    def _get_csrf_token_from_request(self, request: Request) -> str:
        """Extract CSRF token from request headers."""
        # Check X-CSRF-Token header (preferred)
        csrf_token = request.headers.get('X-CSRF-Token')
        if csrf_token:
            return csrf_token
        
        # Check X-CSRFToken header (alternative)
        csrf_token = request.headers.get('X-CSRFToken')
        if csrf_token:
            return csrf_token
        
        return None
    
    async def dispatch(self, request: Request, call_next):
        """Process request and validate CSRF token if required."""
        start_time = time.time()
        
        # Check if this request needs CSRF protection
        needs_csrf_protection = (
            self._is_method_protected(request.method) and
            self._is_path_protected(request.url.path)
        )
        
        if needs_csrf_protection:
            # Get CSRF token from request
            csrf_token = self._get_csrf_token_from_request(request)
            
            if not csrf_token:
                return Response(
                    content='{"error": "CSRF token missing", "message": "CSRF token is required for this operation", "error_code": "CSRF_TOKEN_MISSING"}',
                    status_code=status.HTTP_403_FORBIDDEN,
                    media_type="application/json"
                )
            
            # Validate CSRF token
            if not csrf_protection.validate_csrf_token(csrf_token):
                return Response(
                    content='{"error": "CSRF token invalid", "message": "Invalid or expired CSRF token", "error_code": "CSRF_TOKEN_INVALID"}',
                    status_code=status.HTTP_403_FORBIDDEN,
                    media_type="application/json"
                )
        
        # Process the request
        response = await call_next(request)
        
        # Add CSRF protection headers to response
        process_time = time.time() - start_time
        response.headers["X-CSRF-Protected"] = "true" if needs_csrf_protection else "false"
        response.headers["X-Process-Time"] = str(process_time)
        
        return response


def create_csrf_middleware(
    protected_paths: List[str] = None,
    protected_methods: Set[str] = None,
    exempt_paths: List[str] = None
) -> CSRFMiddleware:
    """
    Create CSRF middleware with custom configuration.
    
    Args:
        protected_paths: List of path patterns that require CSRF protection
        protected_methods: Set of HTTP methods that require CSRF protection
        exempt_paths: List of path patterns exempt from CSRF protection
        
    Returns:
        Configured CSRF middleware
    """
    def middleware_factory(app):
        return CSRFMiddleware(
            app=app,
            protected_paths=protected_paths,
            protected_methods=protected_methods,
            exempt_paths=exempt_paths
        )
    
    return middleware_factory


# Default CSRF middleware configuration
default_csrf_middleware = create_csrf_middleware()

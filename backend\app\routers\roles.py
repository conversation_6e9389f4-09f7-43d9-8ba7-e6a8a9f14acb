"""
Role Management API Router
CRUD operations for roles with proper authorization
"""

from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, status, Query
from asyncpg import Connection

try:
    from ..dependencies import get_db_connection, get_current_active_user
    from ..models.user import User
    from ..models.role import Role, RoleRepository
    from ..models.permission import PermissionRepository
    from ..schemas.rbac import (
        RoleCreate, RoleUpdate, RoleResponse, RoleWithPermissions, 
        RoleListResponse, RoleSummary, RBACErrorResponse
    )
except ImportError:
    from dependencies import get_db_connection, get_current_active_user
    from models.user import User
    from models.role import Role, RoleRepository
    from models.permission import PermissionRepository
    from schemas.rbac import (
        RoleCreate, RoleUpdate, RoleResponse, RoleWithPermissions, 
        RoleListResponse, RoleSummary, RBACErrorResponse
    )

# Create router
router = APIRouter(prefix="/roles", tags=["role-management"])


async def require_admin_permission(current_user: User = Depends(get_current_active_user)) -> User:
    """Dependency to require admin permissions for role management."""
    if not current_user.has_permission("roles.manage") and not current_user.is_superuser:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Insufficient permissions. Role management requires 'roles.manage' permission."
        )
    return current_user


@router.get("/", response_model=RoleListResponse)
async def list_roles(
    page: int = Query(1, ge=1, description="Page number"),
    page_size: int = Query(20, ge=1, le=100, description="Items per page"),
    include_inactive: bool = Query(False, description="Include inactive roles"),
    search: Optional[str] = Query(None, description="Search in role name or description"),
    conn: Connection = Depends(get_db_connection),
    current_user: User = Depends(get_current_active_user)
):
    """List all roles with pagination and filtering."""
    try:
        # Check permissions
        if not current_user.has_permission("roles.read") and not current_user.is_superuser:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Insufficient permissions to view roles."
            )
        
        # Get roles with pagination
        roles = await RoleRepository.get_roles_paginated(
            conn=conn,
            page=page,
            page_size=page_size,
            include_inactive=include_inactive,
            search=search
        )
        
        # Get total count for pagination
        total = await RoleRepository.get_roles_count(
            conn=conn,
            include_inactive=include_inactive,
            search=search
        )
        
        total_pages = (total + page_size - 1) // page_size
        
        return RoleListResponse(
            roles=[RoleResponse(**role.to_dict()) for role in roles],
            total=total,
            page=page,
            page_size=page_size,
            total_pages=total_pages
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to list roles: {str(e)}"
        )


@router.get("/summary", response_model=RoleSummary)
async def get_roles_summary(
    conn: Connection = Depends(get_db_connection),
    current_user: User = Depends(require_admin_permission)
):
    """Get role summary statistics."""
    try:
        summary = await RoleRepository.get_roles_summary(conn)
        return RoleSummary(**summary)
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get roles summary: {str(e)}"
        )


@router.get("/{role_id}", response_model=RoleWithPermissions)
async def get_role(
    role_id: str,
    conn: Connection = Depends(get_db_connection),
    current_user: User = Depends(get_current_active_user)
):
    """Get a specific role with its permissions."""
    try:
        # Check permissions
        if not current_user.has_permission("roles.read") and not current_user.is_superuser:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Insufficient permissions to view roles."
            )
        
        # Get role
        role = await RoleRepository.get_role_by_id(conn, role_id)
        if not role:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Role not found"
            )
        
        # Get role permissions
        permissions = await RoleRepository.get_role_permissions(conn, role_id)
        
        role_dict = role.to_dict()
        role_dict["permissions"] = permissions
        role_dict["permission_count"] = len(permissions)
        
        return RoleWithPermissions(**role_dict)
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get role: {str(e)}"
        )


@router.post("/", response_model=RoleResponse, status_code=status.HTTP_201_CREATED)
async def create_role(
    role_data: RoleCreate,
    conn: Connection = Depends(get_db_connection),
    current_user: User = Depends(require_admin_permission)
):
    """Create a new role."""
    try:
        # Check if role name already exists
        existing_role = await RoleRepository.get_role_by_name(conn, role_data.name)
        if existing_role:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Role with name '{role_data.name}' already exists"
            )
        
        # Create role
        role = await RoleRepository.create_role(
            conn=conn,
            name=role_data.name,
            display_name=role_data.display_name,
            description=role_data.description,
            metadata=role_data.metadata
        )
        
        return RoleResponse(**role.to_dict())
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to create role: {str(e)}"
        )


@router.put("/{role_id}", response_model=RoleResponse)
async def update_role(
    role_id: str,
    role_data: RoleUpdate,
    conn: Connection = Depends(get_db_connection),
    current_user: User = Depends(require_admin_permission)
):
    """Update an existing role."""
    try:
        # Check if role exists
        existing_role = await RoleRepository.get_role_by_id(conn, role_id)
        if not existing_role:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Role not found"
            )
        
        # Prevent modification of system roles
        if existing_role.is_system_role:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Cannot modify system roles"
            )
        
        # Update role
        updated_role = await RoleRepository.update_role(
            conn=conn,
            role_id=role_id,
            display_name=role_data.display_name,
            description=role_data.description,
            metadata=role_data.metadata,
            is_active=role_data.is_active
        )
        
        if not updated_role:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Role not found or update failed"
            )
        
        return RoleResponse(**updated_role.to_dict())
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to update role: {str(e)}"
        )


@router.delete("/{role_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_role(
    role_id: str,
    conn: Connection = Depends(get_db_connection),
    current_user: User = Depends(require_admin_permission)
):
    """Delete a role (soft delete)."""
    try:
        # Check if role exists
        existing_role = await RoleRepository.get_role_by_id(conn, role_id)
        if not existing_role:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Role not found"
            )
        
        # Attempt to delete role (will fail for system roles)
        success = await RoleRepository.delete_role(conn, role_id)
        if not success:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Cannot delete system roles or role deletion failed"
            )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to delete role: {str(e)}"
        )

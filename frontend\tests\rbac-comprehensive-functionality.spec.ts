import { test, expect } from "@playwright/test";

/**
 * Comprehensive RBAC Functionality Tests
 * 
 * Tests detailed functionality of role-based features:
 * - Admin dashboard functionality
 * - User management operations
 * - Role management operations
 * - Permission-based UI interactions
 * - Search and shopping list integration with RBAC
 */

const SUPERADMIN_CREDENTIALS = {
  email: "<EMAIL>",
  password: "SuperYzn123!",
};

const BASE_URL = "http://localhost:3000";

test.describe.configure({ mode: "serial" });

test.describe("Comprehensive RBAC Functionality", () => {
  test.beforeEach(async ({ page }) => {
    await page.goto(BASE_URL);
    await page.evaluate(() => {
      localStorage.clear();
      sessionStorage.clear();
    });
    await page.waitForLoadState("networkidle");

    // Login as superadmin
    await page.goto(`${BASE_URL}/login`);
    await page.waitForLoadState("networkidle");

    await page
      .getByRole("textbox", { name: "Email address" })
      .fill(SUPERADMIN_CREDENTIALS.email);
    await page
      .getByRole("textbox", { name: "Password" })
      .fill(SUPERADMIN_CREDENTIALS.password);

    await Promise.all([
      page.waitForURL(`${BASE_URL}/`, { timeout: 15000 }),
      page.getByRole("button", { name: "Sign in" }).click(),
    ]);

    await page.waitForLoadState("networkidle");
  });

  test.describe("Admin Dashboard Functionality", () => {
    test("should display comprehensive admin dashboard", async ({ page }) => {
      await page.goto(`${BASE_URL}/admin`);
      
      // Check main dashboard heading
      await expect(
        page.getByRole("heading", { name: "Admin Dashboard" })
      ).toBeVisible();

      // Check dashboard stats/cards if they exist
      await page.waitForTimeout(2000);
      
      // Take screenshot for verification
      await page.screenshot({ path: "admin-dashboard.png" });
    });

    test("should navigate to admin sections from dashboard", async ({ page }) => {
      await page.goto(`${BASE_URL}/admin`);

      // Test navigation to user management
      await page.getByRole("link", { name: "User Management" }).click();
      await expect(page).toHaveURL(`${BASE_URL}/admin/users`);

      // Navigate back to dashboard
      await page.getByRole("link", { name: "Admin Dashboard" }).click();
      await expect(page).toHaveURL(`${BASE_URL}/admin`);

      // Test navigation to role management
      await page.getByRole("link", { name: "Role Management" }).click();
      await expect(page).toHaveURL(`${BASE_URL}/admin/roles`);
    });
  });

  test.describe("User Management Functionality", () => {
    test("should display user management interface", async ({ page }) => {
      await page.goto(`${BASE_URL}/admin/users`);
      await page.waitForLoadState("networkidle");

      // Should load without errors
      await expect(page).toHaveURL(`${BASE_URL}/admin/users`);
      
      // Take screenshot for verification
      await page.screenshot({ path: "user-management.png" });
    });

    test("should handle user management operations", async ({ page }) => {
      await page.goto(`${BASE_URL}/admin/users`);
      await page.waitForLoadState("networkidle");
      await page.waitForTimeout(3000);

      // Check if user list loads (may depend on backend implementation)
      // This test verifies the page loads without errors
      await expect(page).toHaveURL(`${BASE_URL}/admin/users`);
    });
  });

  test.describe("Role Management Functionality", () => {
    test("should display role management interface", async ({ page }) => {
      await page.goto(`${BASE_URL}/admin/roles`);
      await page.waitForLoadState("networkidle");

      // Should load without errors
      await expect(page).toHaveURL(`${BASE_URL}/admin/roles`);
      
      // Take screenshot for verification
      await page.screenshot({ path: "role-management.png" });
    });

    test("should handle role management operations", async ({ page }) => {
      await page.goto(`${BASE_URL}/admin/roles`);
      await page.waitForLoadState("networkidle");
      await page.waitForTimeout(3000);

      // Check if role list loads (may depend on backend implementation)
      await expect(page).toHaveURL(`${BASE_URL}/admin/roles`);
    });
  });

  test.describe("Search Integration with RBAC", () => {
    test("should maintain search functionality with admin permissions", async ({ page }) => {
      await page.goto(`${BASE_URL}/search`);

      // Verify search interface is available
      await expect(
        page.getByRole("textbox", { name: "Search dental products..." })
      ).toBeVisible();

      // Test search functionality
      await page
        .getByRole("textbox", { name: "Search dental products..." })
        .fill("dental");

      await page.waitForTimeout(2000);

      // Should show suggestions or results
      // Take screenshot for verification
      await page.screenshot({ path: "search-with-admin-permissions.png" });
    });

    test("should show admin navigation while on search page", async ({ page }) => {
      await page.goto(`${BASE_URL}/search`);

      // Admin navigation should be visible
      await expect(
        page.getByRole("link", { name: "Admin Dashboard" })
      ).toBeVisible();
      await expect(
        page.getByRole("link", { name: "User Management" })
      ).toBeVisible();
      await expect(
        page.getByRole("link", { name: "Role Management" })
      ).toBeVisible();
    });

    test("should handle product details with admin permissions", async ({ page }) => {
      await page.goto(`${BASE_URL}/search`);

      // Search for products
      await page
        .getByRole("textbox", { name: "Search dental products..." })
        .fill("bur");

      await page.waitForTimeout(2000);

      // Try to click on a product suggestion if available
      const suggestions = page.locator('[role="button"]').filter({ hasText: /bur/i });
      const suggestionCount = await suggestions.count();
      
      if (suggestionCount > 0) {
        await suggestions.first().click();
        
        // Should show product details
        await expect(page.getByRole("heading", { name: "Product Details" })).toBeVisible();
        
        // Take screenshot
        await page.screenshot({ path: "product-details-admin.png" });
      }
    });
  });

  test.describe("Shopping Lists Integration with RBAC", () => {
    test("should maintain shopping list functionality with admin permissions", async ({ page }) => {
      await page.goto(`${BASE_URL}/shopping-lists`);

      // Verify shopping lists interface
      await expect(
        page.getByRole("heading", { name: "Shopping Lists" })
      ).toBeVisible();

      // Should show New List button (create permission)
      await expect(
        page.getByRole("button", { name: "New List" })
      ).toBeVisible();

      // Should show authorization badge
      await expect(page.getByText("Authorized")).toBeVisible();

      // Take screenshot
      await page.screenshot({ path: "shopping-lists-admin.png" });
    });

    test("should show admin navigation while on shopping lists page", async ({ page }) => {
      await page.goto(`${BASE_URL}/shopping-lists`);

      // Admin navigation should be visible
      await expect(
        page.getByRole("link", { name: "Admin Dashboard" })
      ).toBeVisible();
      await expect(
        page.getByRole("link", { name: "User Management" })
      ).toBeVisible();
      await expect(
        page.getByRole("link", { name: "Role Management" })
      ).toBeVisible();
    });
  });

  test.describe("Profile Integration with RBAC", () => {
    test("should display comprehensive admin profile information", async ({ page }) => {
      await page.goto(`${BASE_URL}/profile`);
      await page.waitForLoadState("networkidle");

      // Check profile heading
      await expect(
        page.getByRole("heading", { name: "Super YZN Admin" })
      ).toBeVisible();

      // Click on Roles & Permissions tab
      await page.getByRole("button", { name: "Roles & Permissions" }).click();
      await page.waitForTimeout(1000);

      // Verify comprehensive role information
      await expect(
        page.getByRole("heading", { name: "Your Roles" })
      ).toBeVisible();
      await expect(page.getByText("Super Administrator")).toBeVisible();

      // Check all permission categories
      await expect(
        page.getByRole("heading", { name: "Search & Products" })
      ).toBeVisible();
      await expect(
        page.getByRole("heading", { name: "Shopping Lists" })
      ).toBeVisible();
      await expect(
        page.getByRole("heading", { name: "Administrative Access" })
      ).toBeVisible();

      // Take screenshot
      await page.screenshot({ path: "admin-profile-permissions.png" });
    });

    test("should show admin navigation while on profile page", async ({ page }) => {
      await page.goto(`${BASE_URL}/profile`);

      // Admin navigation should be visible
      await expect(
        page.getByRole("link", { name: "Admin Dashboard" })
      ).toBeVisible();
      await expect(
        page.getByRole("link", { name: "User Management" })
      ).toBeVisible();
      await expect(
        page.getByRole("link", { name: "Role Management" })
      ).toBeVisible();
    });
  });

  test.describe("Cross-Feature Integration", () => {
    test("should maintain consistent admin UI across all pages", async ({ page }) => {
      const pages = [
        { url: "/search", heading: "Search Products" },
        { url: "/shopping-lists", heading: "Shopping Lists" },
        { url: "/profile", heading: "Super YZN Admin" },
        { url: "/admin", heading: "Admin Dashboard" },
        { url: "/admin/users", heading: null }, // May not have specific heading
        { url: "/admin/roles", heading: null }, // May not have specific heading
      ];

      for (const pageInfo of pages) {
        await page.goto(`${BASE_URL}${pageInfo.url}`);
        await page.waitForLoadState("networkidle");

        // Check admin navigation is always visible
        await expect(
          page.getByRole("link", { name: "Admin Dashboard" })
        ).toBeVisible();

        // Check user dropdown shows admin role
        await page.locator("header button").nth(2).click();
        await expect(page.getByText("Super Administrator")).toBeVisible();
        
        // Close dropdown
        await page.locator("header button").nth(2).click();

        if (pageInfo.heading) {
          await expect(
            page.getByRole("heading", { name: pageInfo.heading })
          ).toBeVisible();
        }
      }
    });

    test("should handle logout from any admin page", async ({ page }) => {
      // Test logout from admin dashboard
      await page.goto(`${BASE_URL}/admin`);
      await page.locator("header button").nth(2).click();
      await page.getByRole("button", { name: "Sign out" }).click();
      await page.waitForTimeout(1000);

      // Should redirect to home and lose admin access
      await page.goto(`${BASE_URL}/admin`);
      await expect(page).toHaveURL(`${BASE_URL}/login`);
    });
  });
});

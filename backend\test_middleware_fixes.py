#!/usr/bin/env python3
"""
Test middleware fixes for JSON parsing and concurrent performance.
"""

import asyncio
import httpx
import time
import statistics


async def test_concurrent_search_performance():
    """Test concurrent search performance after middleware fixes."""
    print("🚀 Testing Concurrent Search Performance After Middleware Fixes")
    print("=" * 70)
    
    base_url = "http://localhost:8000"
    search_queries = ["dental", "implant", "crown", "bridge", "filling"]
    
    async def single_search(query):
        """Perform a single search request."""
        async with httpx.AsyncClient(timeout=30.0) as client:
            start_time = time.time()
            response = await client.get(f"{base_url}/api/v1/search/suggestions?q={query}&limit=5")
            end_time = time.time()
            
            if response.status_code == 200:
                data = response.json()
                suggestions = data.get('suggestions', [])
                return True, (end_time - start_time) * 1000, len(suggestions)
            else:
                return False, (end_time - start_time) * 1000, 0
    
    # Test 1: Individual Search Performance
    print("1. Testing Individual Search Performance...")
    individual_times = []
    
    for query in search_queries:
        success, response_time, count = await single_search(query)
        if success:
            individual_times.append(response_time)
            print(f"   ✅ Search '{query}': {response_time:.2f}ms ({count} results)")
        else:
            print(f"   ❌ Search '{query}' failed")
    
    if individual_times:
        avg_individual = statistics.mean(individual_times)
        print(f"   📊 Average individual search time: {avg_individual:.2f}ms")
    else:
        print("   ❌ No successful individual searches")
        return False
    
    # Test 2: Concurrent Search Performance
    print("\n2. Testing Concurrent Search Performance...")
    
    async def concurrent_search_batch():
        """Run concurrent searches."""
        tasks = [single_search(query) for query in search_queries]
        start_time = time.time()
        results = await asyncio.gather(*tasks)
        end_time = time.time()
        
        successful_results = [r for r in results if r[0]]
        response_times = [r[1] for r in successful_results]
        total_time = (end_time - start_time) * 1000
        
        return successful_results, response_times, total_time
    
    # Run multiple concurrent batches
    concurrent_times = []
    batch_times = []
    
    for batch in range(3):
        print(f"   Running concurrent batch {batch + 1}...")
        successful_results, response_times, total_batch_time = await concurrent_search_batch()
        
        if response_times:
            avg_batch_time = statistics.mean(response_times)
            concurrent_times.extend(response_times)
            batch_times.append(total_batch_time)
            
            print(f"   ✅ Batch {batch + 1}: {len(successful_results)}/5 successful")
            print(f"      Average response time: {avg_batch_time:.2f}ms")
            print(f"      Total batch time: {total_batch_time:.2f}ms")
        else:
            print(f"   ❌ Batch {batch + 1}: No successful requests")
    
    if concurrent_times:
        avg_concurrent = statistics.mean(concurrent_times)
        avg_batch_total = statistics.mean(batch_times)
        
        print(f"\n📊 Performance Summary:")
        print(f"   Individual search average: {avg_individual:.2f}ms")
        print(f"   Concurrent search average: {avg_concurrent:.2f}ms")
        print(f"   Average batch total time: {avg_batch_total:.2f}ms")
        
        # Performance evaluation
        performance_ratio = avg_concurrent / avg_individual
        print(f"   Performance ratio (concurrent/individual): {performance_ratio:.2f}x")
        
        if performance_ratio <= 2.0:
            print("   ✅ EXCELLENT: Concurrent performance is excellent")
            return True
        elif performance_ratio <= 5.0:
            print("   ✅ GOOD: Concurrent performance is good")
            return True
        elif performance_ratio <= 10.0:
            print("   ⚠️  ACCEPTABLE: Concurrent performance is acceptable")
            return True
        else:
            print("   ❌ POOR: Concurrent performance needs improvement")
            return False
    else:
        print("   ❌ No successful concurrent searches")
        return False


async def test_json_parsing_with_csrf():
    """Test JSON parsing with CSRF protection enabled."""
    print("\n🔒 Testing JSON Parsing with CSRF Protection")
    print("=" * 70)
    
    base_url = "http://localhost:8000"
    
    try:
        async with httpx.AsyncClient(timeout=30.0) as client:
            
            # Test 1: Get CSRF Token
            print("1. Getting CSRF Token...")
            response = await client.get(f"{base_url}/api/v1/auth/csrf-token")
            
            if response.status_code == 200:
                data = response.json()
                csrf_token = data.get('data', {}).get('csrf_token')
                if csrf_token:
                    print(f"   ✅ CSRF token obtained: {csrf_token[:20]}...")
                else:
                    print(f"   ❌ CSRF token not found in response")
                    return False
            else:
                print(f"   ❌ Failed to get CSRF token: {response.status_code}")
                return False
            
            # Test 2: Test JSON Parsing with CSRF
            print("\n2. Testing JSON Request with CSRF Token...")
            test_data = {
                "email": f"test_{int(time.time())}@example.com",
                "password": "Test123!@#",
                "name": "Test User"
            }
            
            headers = {
                "Content-Type": "application/json",
                "X-CSRF-Token": csrf_token
            }
            
            start_time = time.time()
            response = await client.post(
                f"{base_url}/api/v1/auth/register",
                json=test_data,
                headers=headers
            )
            end_time = time.time()
            
            response_time = (end_time - start_time) * 1000
            
            if response.status_code in [201, 400, 409]:  # Success, validation error, or user exists
                print(f"   ✅ JSON request processed: {response_time:.2f}ms")
                print(f"   Status: {response.status_code}")
                return True
            else:
                print(f"   ❌ JSON request failed: {response.status_code}")
                print(f"   Response: {response.text}")
                return False
                
    except Exception as e:
        print(f"   ❌ JSON parsing test failed: {e}")
        return False


async def main():
    """Run all middleware fix tests."""
    print("🔧 Middleware Fixes Verification")
    print("=" * 70)
    
    # Test concurrent search performance
    concurrent_success = await test_concurrent_search_performance()
    
    # Test JSON parsing with CSRF
    json_success = await test_json_parsing_with_csrf()
    
    print("\n🎉 Middleware Fixes Test Summary")
    print("=" * 70)
    
    if concurrent_success:
        print("✅ Concurrent search performance: IMPROVED")
    else:
        print("❌ Concurrent search performance: NEEDS WORK")
    
    if json_success:
        print("✅ JSON parsing with CSRF: WORKING")
    else:
        print("❌ JSON parsing with CSRF: FAILING")
    
    if concurrent_success and json_success:
        print("\n🎉 All middleware fixes are working correctly!")
        return True
    else:
        print("\n❌ Some middleware issues remain")
        return False


if __name__ == "__main__":
    success = asyncio.run(main())
    exit(0 if success else 1)

#!/usr/bin/env python3
"""
Simple test to verify the application is working
"""

import asyncio
from playwright.async_api import async_playwright

async def simple_test():
    print("🚀 Starting simple application test...")
    
    playwright = await async_playwright().start()
    browser = await playwright.chromium.launch(headless=False)
    page = await browser.new_page()
    
    try:
        # Navigate to homepage
        print("📱 Navigating to homepage...")
        await page.goto("http://localhost:3000")
        await page.wait_for_load_state("networkidle", timeout=10000)
        
        # Take screenshot
        await page.screenshot(path="homepage_screenshot.png")
        print("📸 Screenshot saved as homepage_screenshot.png")
        
        # Check title
        title = await page.title()
        print(f"📄 Page title: {title}")
        
        # Look for key elements
        search_input = await page.locator("input[placeholder*='Search'], input[type='search']").count()
        manufacturers_text = await page.locator("text=Manufacturers").count()
        categories_text = await page.locator("text=Categories").count()
        
        print(f"🔍 Search inputs found: {search_input}")
        print(f"🏭 Manufacturers text found: {manufacturers_text}")
        print(f"📂 Categories text found: {categories_text}")
        
        # Test API endpoints
        print("\n🌐 Testing API endpoints...")
        
        # Test manufacturers endpoint
        response = await page.request.get("http://localhost:8000/api/v1/products/manufacturers/?limit=3")
        print(f"🏭 Manufacturers API: {response.status}")
        if response.status == 200:
            data = await response.json()
            if "manufacturers" in data:
                print(f"   Found {len(data['manufacturers'])} manufacturers")
                for mfr in data['manufacturers']:
                    print(f"   - {mfr['name']}: {mfr['count']} products")
        
        # Test categories endpoint
        response = await page.request.get("http://localhost:8000/api/v1/products/categories/?limit=3")
        print(f"📂 Categories API: {response.status}")
        if response.status == 200:
            data = await response.json()
            if "categories" in data:
                print(f"   Found {len(data['categories'])} categories")
                for cat in data['categories']:
                    print(f"   - {cat['name']}: {cat['count']} products")
        
        # Test filtered suggestions
        response = await page.request.get("http://localhost:8000/api/v1/search/suggestions?q=comp&manufactured_by=hu-friedy")
        print(f"💡 Filtered suggestions API: {response.status}")
        if response.status == 200:
            data = await response.json()
            if "suggestions" in data:
                print(f"   Found {len(data['suggestions'])} filtered suggestions")
                for sugg in data['suggestions'][:3]:
                    print(f"   - {sugg}")
        
        print("\n✅ Basic tests completed!")
        print("🌐 Application URLs:")
        print("   Frontend: http://localhost:3000")
        print("   Backend API: http://localhost:8000")
        print("   API Docs: http://localhost:8000/docs")
        
        print("\n📋 Manual Testing Instructions:")
        print("1. The browser window is open for you to test manually")
        print("2. Try the following:")
        print("   - Look for 'Manufacturers' dropdown (not 'Brands')")
        print("   - Select a manufacturer and category")
        print("   - Type in search box and check suggestions")
        print("   - Perform a search and verify results")
        print("3. Press Enter here when done to close browser")
        
        # Keep browser open for manual testing
        input("\nPress Enter to close browser...")
        
    except Exception as e:
        print(f"❌ Error during test: {e}")
    finally:
        await browser.close()
        await playwright.stop()

if __name__ == "__main__":
    asyncio.run(simple_test())

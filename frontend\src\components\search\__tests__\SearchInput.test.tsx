import { describe, it, expect, vi, beforeEach } from 'vitest'
import { screen, fireEvent, waitFor } from '@testing-library/react'
import { render } from '../../../test-utils'
import SearchInput from '../SearchInput'
import { useSearchStore } from '../../../stores/searchStore'

// Mock the search store
vi.mock('../../../stores/searchStore')
vi.mock('../../../hooks/useSearch')

const mockUseSearchStore = vi.mocked(useSearchStore)

describe('SearchInput', () => {
  const mockSetQuery = vi.fn()
  const mockAddRecentSearch = vi.fn()

  beforeEach(() => {
    vi.clearAllMocks()
    
    mockUseSearchStore.mockReturnValue({
      query: '',
      setQuery: mockSetQuery,
      recentSearches: ['dental equipment', 'implants'],
      addRecentSearch: mockAddRecentSearch,
      suggestions: ['dental tools', 'dental supplies'],
      popularSearches: ['orthodontics', 'endodontics'],
    } as any)
  })

  it('renders search input with placeholder', () => {
    render(<SearchInput />)
    
    const input = screen.getByPlaceholderText('Search dental products...')
    expect(input).toBeInTheDocument()
  })

  it('updates input value when typing', () => {
    render(<SearchInput />)
    
    const input = screen.getByPlaceholderText('Search dental products...')
    fireEvent.change(input, { target: { value: 'dental' } })
    
    expect(input).toHaveValue('dental')
  })

  it('shows suggestions when input is focused', async () => {
    render(<SearchInput />)
    
    const input = screen.getByPlaceholderText('Search dental products...')
    fireEvent.focus(input)
    
    await waitFor(() => {
      expect(screen.getByText('Recent')).toBeInTheDocument()
      expect(screen.getByText('dental equipment')).toBeInTheDocument()
      expect(screen.getByText('implants')).toBeInTheDocument()
    })
  })

  it('handles form submission', () => {
    render(<SearchInput />)
    
    const input = screen.getByPlaceholderText('Search dental products...')
    fireEvent.change(input, { target: { value: 'dental tools' } })
    fireEvent.submit(input.closest('form')!)
    
    expect(mockSetQuery).toHaveBeenCalledWith('dental tools')
    expect(mockAddRecentSearch).toHaveBeenCalledWith('dental tools')
  })

  it('clears input when clear button is clicked', () => {
    render(<SearchInput />)
    
    const input = screen.getByPlaceholderText('Search dental products...')
    fireEvent.change(input, { target: { value: 'dental' } })
    
    const clearButton = screen.getByRole('button')
    fireEvent.click(clearButton)
    
    expect(input).toHaveValue('')
  })

  it('handles keyboard navigation in suggestions', async () => {
    render(<SearchInput />)
    
    const input = screen.getByPlaceholderText('Search dental products...')
    fireEvent.focus(input)
    
    await waitFor(() => {
      expect(screen.getByText('dental equipment')).toBeInTheDocument()
    })
    
    // Test arrow down navigation
    fireEvent.keyDown(input, { key: 'ArrowDown' })
    fireEvent.keyDown(input, { key: 'Enter' })
    
    expect(mockSetQuery).toHaveBeenCalled()
  })

  it('closes suggestions on escape key', async () => {
    render(<SearchInput />)
    
    const input = screen.getByPlaceholderText('Search dental products...')
    fireEvent.focus(input)
    
    await waitFor(() => {
      expect(screen.getByText('Recent')).toBeInTheDocument()
    })
    
    fireEvent.keyDown(input, { key: 'Escape' })
    
    await waitFor(() => {
      expect(screen.queryByText('Recent')).not.toBeInTheDocument()
    })
  })
})

#!/usr/bin/env python3
"""
Test search functionality performance and compatibility with security enhancements.
"""

import asyncio
import time
import statistics
import httpx


async def test_search_performance_with_security():
    """Test search functionality performance with security enhancements."""
    print("🔍 Search Performance Testing with Security Enhancements")
    print("=" * 70)
    
    base_url = "http://localhost:8000"

    try:
        
        # Test 1: Search Suggestions Performance
        print("1. Testing Search Suggestions Performance...")
        
        suggestion_queries = [
            "dental",
            "implant",
            "crown",
            "bridge",
            "filling",
            "root",
            "extraction",
            "cleaning",
            "whitening",
            "orthodontic"
        ]
        
        suggestion_times = []
        async with httpx.AsyncClient(timeout=30.0) as client:
            for query in suggestion_queries:
                start_time = time.time()
                response = await client.get(f"{base_url}/api/v1/search/suggestions?q={query}&limit=10")
                end_time = time.time()

                response_time = (end_time - start_time) * 1000  # Convert to milliseconds
                suggestion_times.append(response_time)

                if response.status_code == 200:
                    data = response.json()
                    suggestions = data.get('suggestions', [])
                    print(f"   Query '{query}': {response_time:.2f}ms ({len(suggestions)} suggestions)")
                else:
                    print(f"   Query '{query}': {response_time:.2f}ms (ERROR: {response.status_code})")
        
        avg_suggestion_time = statistics.mean(suggestion_times)
        max_suggestion_time = max(suggestion_times)
        
        print(f"   Average suggestion time: {avg_suggestion_time:.2f}ms")
        print(f"   Maximum suggestion time: {max_suggestion_time:.2f}ms")
        
        if avg_suggestion_time < 100:  # Target: under 100ms average
            print("   ✅ Search suggestions performance: EXCELLENT")
            suggestion_performance = "EXCELLENT"
        elif avg_suggestion_time < 200:
            print("   ✅ Search suggestions performance: GOOD")
            suggestion_performance = "GOOD"
        elif avg_suggestion_time < 500:
            print("   ⚠️  Search suggestions performance: ACCEPTABLE")
            suggestion_performance = "ACCEPTABLE"
        else:
            print("   ❌ Search suggestions performance: POOR")
            suggestion_performance = "POOR"
        
        # Test 2: Main Search Performance
        print("\n2. Testing Main Search Performance...")
        
        search_queries = [
            ("dental implant", 20),
            ("crown restoration", 15),
            ("root canal", 25),
            ("teeth cleaning", 30),
            ("orthodontic braces", 10),
            ("tooth extraction", 20),
            ("dental bridge", 15),
            ("cavity filling", 25),
            ("teeth whitening", 20),
            ("periodontal treatment", 10)
        ]
        
        search_times = []
        async with httpx.AsyncClient(timeout=30.0) as client:
            for query, expected_min_results in search_queries:
                start_time = time.time()
                response = await client.get(f"{base_url}/api/v1/search/?q={query}&limit=50")
                end_time = time.time()

                response_time = (end_time - start_time) * 1000  # Convert to milliseconds
                search_times.append(response_time)

                if response.status_code == 200:
                    data = response.json()
                    results = data.get('results', [])
                    print(f"   Query '{query}': {response_time:.2f}ms ({len(results)} results)")

                    if len(results) < expected_min_results:
                        print(f"      ⚠️  Expected at least {expected_min_results} results, got {len(results)}")
                else:
                    print(f"   Query '{query}': {response_time:.2f}ms (ERROR: {response.status_code})")
        
        avg_search_time = statistics.mean(search_times)
        max_search_time = max(search_times)
        
        print(f"   Average search time: {avg_search_time:.2f}ms")
        print(f"   Maximum search time: {max_search_time:.2f}ms")
        
        if avg_search_time < 200:  # Target: under 200ms average
            print("   ✅ Main search performance: EXCELLENT")
            search_performance = "EXCELLENT"
        elif avg_search_time < 500:
            print("   ✅ Main search performance: GOOD")
            search_performance = "GOOD"
        elif avg_search_time < 1000:
            print("   ⚠️  Main search performance: ACCEPTABLE")
            search_performance = "ACCEPTABLE"
        else:
            print("   ❌ Main search performance: POOR")
            search_performance = "POOR"
        
        # Test 3: Search Functionality Integrity
        print("\n3. Testing Search Functionality Integrity...")
        
        integrity_tests = [
            ("dental", "Should return dental-related products"),
            ("implant", "Should return implant products"),
            ("crown", "Should return crown products"),
            ("bridge", "Should return bridge products"),
            ("filling", "Should return filling materials"),
        ]
        
        integrity_passed = 0
        async with httpx.AsyncClient(timeout=30.0) as client:
            for query, description in integrity_tests:
                # Test suggestions
                sugg_response = await client.get(f"{base_url}/api/v1/search/suggestions?q={query}&limit=5")
                suggestions = []
                if sugg_response.status_code == 200:
                    sugg_data = sugg_response.json()
                    suggestions = sugg_data.get('suggestions', [])

                # Test search results
                search_response = await client.get(f"{base_url}/api/v1/search/?q={query}&limit=10")
                results = []
                if search_response.status_code == 200:
                    search_data = search_response.json()
                    results = search_data.get('results', [])

                if len(suggestions) > 0 and len(results) > 0:
                    integrity_passed += 1
                    print(f"   ✅ {description}: {len(suggestions)} suggestions, {len(results)} results")
                else:
                    print(f"   ❌ {description}: {len(suggestions)} suggestions, {len(results)} results")
        
        integrity_percentage = (integrity_passed / len(integrity_tests)) * 100
        print(f"   Search integrity: {integrity_percentage:.1f}% ({integrity_passed}/{len(integrity_tests)} tests passed)")
        
        # Test 4: Search with Filters Performance
        print("\n4. Testing Search with Filters Performance...")
        
        filter_tests = [
            ("dental", {"category": "Implants"}),
            ("crown", {"brand": "Straumann"}),
            ("filling", {"seller": "Dental Supply Co"}),
            ("implant", {"category": "Implants", "brand": "Nobel Biocare"}),
        ]
        
        filter_times = []
        async with httpx.AsyncClient(timeout=30.0) as client:
            for query, filters in filter_tests:
                # Build filter parameters
                params = {"q": query, "limit": 20}
                for key, value in filters.items():
                    params[key] = value

                start_time = time.time()
                response = await client.get(f"{base_url}/api/v1/search/", params=params)
                end_time = time.time()

                response_time = (end_time - start_time) * 1000
                filter_times.append(response_time)

                if response.status_code == 200:
                    data = response.json()
                    results = data.get('results', [])
                    print(f"   Query '{query}' with filters: {response_time:.2f}ms ({len(results)} results)")
                else:
                    print(f"   Query '{query}' with filters: {response_time:.2f}ms (ERROR: {response.status_code})")
        
        avg_filter_time = statistics.mean(filter_times) if filter_times else 0
        print(f"   Average filtered search time: {avg_filter_time:.2f}ms")
        
        if avg_filter_time < 300:  # Target: under 300ms average for filtered searches
            print("   ✅ Filtered search performance: EXCELLENT")
            filter_performance = "EXCELLENT"
        elif avg_filter_time < 600:
            print("   ✅ Filtered search performance: GOOD")
            filter_performance = "GOOD"
        elif avg_filter_time < 1000:
            print("   ⚠️  Filtered search performance: ACCEPTABLE")
            filter_performance = "ACCEPTABLE"
        else:
            print("   ❌ Filtered search performance: POOR")
            filter_performance = "POOR"
        
        # Test 5: Concurrent Search Performance
        print("\n5. Testing Concurrent Search Performance...")
        
        async def concurrent_search(query, search_id):
            async with httpx.AsyncClient(timeout=30.0) as client:
                start_time = time.time()
                response = await client.get(f"{base_url}/api/v1/search/?q={query}&limit=10")
                end_time = time.time()

                results_count = 0
                if response.status_code == 200:
                    data = response.json()
                    results = data.get('results', [])
                    results_count = len(results)

                return (search_id, (end_time - start_time) * 1000, results_count)
        
        # Run 10 concurrent searches
        concurrent_tasks = []
        for i in range(10):
            query = suggestion_queries[i % len(suggestion_queries)]
            task = concurrent_search(query, i)
            concurrent_tasks.append(task)
        
        concurrent_results = await asyncio.gather(*concurrent_tasks)
        concurrent_times = [result[1] for result in concurrent_results]
        
        avg_concurrent_time = statistics.mean(concurrent_times)
        max_concurrent_time = max(concurrent_times)
        
        print(f"   Average concurrent search time: {avg_concurrent_time:.2f}ms")
        print(f"   Maximum concurrent search time: {max_concurrent_time:.2f}ms")
        
        if avg_concurrent_time < 300:
            print("   ✅ Concurrent search performance: EXCELLENT")
            concurrent_performance = "EXCELLENT"
        elif avg_concurrent_time < 600:
            print("   ✅ Concurrent search performance: GOOD")
            concurrent_performance = "GOOD"
        elif avg_concurrent_time < 1000:
            print("   ⚠️  Concurrent search performance: ACCEPTABLE")
            concurrent_performance = "ACCEPTABLE"
        else:
            print("   ❌ Concurrent search performance: POOR")
            concurrent_performance = "POOR"
        
        # Overall Assessment
        print("\n🎉 Search Performance Testing Completed!")
        print("=" * 70)
        
        performance_scores = {
            "Search Suggestions": suggestion_performance,
            "Main Search": search_performance,
            "Search Integrity": "EXCELLENT" if integrity_percentage >= 90 else "GOOD" if integrity_percentage >= 80 else "ACCEPTABLE" if integrity_percentage >= 70 else "POOR",
            "Filtered Search": filter_performance,
            "Concurrent Search": concurrent_performance
        }
        
        for test_name, performance in performance_scores.items():
            status_icon = "✅" if performance in ["EXCELLENT", "GOOD"] else "⚠️" if performance == "ACCEPTABLE" else "❌"
            print(f"{status_icon} {test_name}: {performance}")
        
        excellent_count = sum(1 for p in performance_scores.values() if p == "EXCELLENT")
        good_count = sum(1 for p in performance_scores.values() if p == "GOOD")
        acceptable_count = sum(1 for p in performance_scores.values() if p == "ACCEPTABLE")
        poor_count = sum(1 for p in performance_scores.values() if p == "POOR")
        
        print(f"\nPerformance Summary:")
        print(f"✅ Excellent: {excellent_count}")
        print(f"✅ Good: {good_count}")
        print(f"⚠️  Acceptable: {acceptable_count}")
        print(f"❌ Poor: {poor_count}")
        
        overall_score = (excellent_count * 4 + good_count * 3 + acceptable_count * 2 + poor_count * 1) / (len(performance_scores) * 4) * 100
        print(f"\nOverall Performance Score: {overall_score:.1f}%")
        
        if overall_score >= 85:
            print("🚀 Search functionality is performing excellently with security enhancements!")
            return True
        elif overall_score >= 70:
            print("✅ Search functionality is performing well with security enhancements!")
            return True
        elif overall_score >= 55:
            print("⚠️  Search functionality performance is acceptable but could be improved!")
            return True
        else:
            print("❌ Search functionality performance is degraded by security enhancements!")
            return False
        
        # Test completed successfully
        
    except Exception as e:
        print(f"❌ Search performance test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


async def main():
    """Run search performance tests."""
    success = await test_search_performance_with_security()
    
    if success:
        print("\n✅ Search functionality works excellently with security enhancements!")
    else:
        print("\n❌ Search functionality performance issues detected!")
        exit(1)


if __name__ == "__main__":
    asyncio.run(main())

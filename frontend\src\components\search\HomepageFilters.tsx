import { useState } from "react";
import { useNavigate } from "react-router-dom";
import { Filter, Search } from "lucide-react";
import FilterDropdown from "./FilterDropdown";
import {
  useCategories,
  useManufacturers,
  useSellers,
} from "../../hooks/useSearch";
import { useSearchStore } from "../../stores/searchStore";

const HomepageFilters = () => {
  const navigate = useNavigate();
  const { setFilters, setQuery } = useSearchStore();

  // Local filter state
  const [selectedCategories, setSelectedCategories] = useState<string[]>([]);
  const [selectedManufacturers, setSelectedManufacturers] = useState<string[]>(
    []
  );
  const [selectedSellers, setSelectedSellers] = useState<string[]>([]);

  // Fetch filter data
  const { data: categoriesData, isLoading: categoriesLoading } =
    useCategories();
  const { data: manufacturersData, isLoading: manufacturersLoading } =
    useManufacturers();
  const { data: sellersData, isLoading: sellersLoading } = useSellers();

  const categories = categoriesData?.categories || [];
  const manufacturers = manufacturersData?.manufacturers || [];
  const sellers = sellersData?.sellers || [];

  const handleSearch = () => {
    // Set filters in the store
    const filters: any = {};
    if (selectedCategories.length > 0) {
      filters.category = selectedCategories[0]; // API currently supports single category
    }
    if (selectedManufacturers.length > 0) {
      filters.manufactured_by = selectedManufacturers[0]; // API currently supports single manufacturer
    }
    if (selectedSellers.length > 0) {
      filters.seller = selectedSellers[0]; // API currently supports single seller
    }

    setFilters(filters);

    // Navigate to search page
    const params = new URLSearchParams();
    if (selectedCategories.length > 0) {
      params.set("category", selectedCategories[0]);
    }
    if (selectedManufacturers.length > 0) {
      params.set("manufactured_by", selectedManufacturers[0]);
    }
    if (selectedSellers.length > 0) {
      params.set("seller", selectedSellers[0]);
    }

    navigate(`/search?${params.toString()}`);
  };

  const handleClearAll = () => {
    setSelectedCategories([]);
    setSelectedManufacturers([]);
    setSelectedSellers([]);
  };

  const hasFilters =
    selectedCategories.length > 0 ||
    selectedManufacturers.length > 0 ||
    selectedSellers.length > 0;
  const totalSelected =
    selectedCategories.length +
    selectedManufacturers.length +
    selectedSellers.length;

  return (
    <div className="bg-white border border-gray-200 rounded-xl shadow-sm p-6">
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-2">
          <Filter className="h-5 w-5 text-primary-600" />
          <h3 className="text-lg font-semibold text-gray-900">
            Advanced Filters
          </h3>
          {totalSelected > 0 && (
            <span className="bg-primary-100 text-primary-700 text-xs font-medium px-2 py-1 rounded-full">
              {totalSelected} selected
            </span>
          )}
        </div>
        {hasFilters && (
          <button
            onClick={handleClearAll}
            className="text-sm text-gray-500 hover:text-gray-700 font-medium"
          >
            Clear all
          </button>
        )}
      </div>

      {/* Filter Dropdowns */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
        <FilterDropdown
          label="Categories"
          placeholder="Select categories"
          options={categories}
          selectedValues={selectedCategories}
          onSelectionChange={setSelectedCategories}
          loading={categoriesLoading}
          multiSelect={false} // API currently supports single selection
        />

        <FilterDropdown
          label="Manufacturers"
          placeholder="Select manufacturers"
          options={manufacturers}
          selectedValues={selectedManufacturers}
          onSelectionChange={setSelectedManufacturers}
          loading={manufacturersLoading}
          multiSelect={false} // API currently supports single selection
        />

        <FilterDropdown
          label="Sellers"
          placeholder="Select sellers"
          options={sellers}
          selectedValues={selectedSellers}
          onSelectionChange={setSelectedSellers}
          loading={sellersLoading}
          multiSelect={false} // API currently supports single selection
        />
      </div>

      {/* Search Button */}
      <div className="flex justify-center">
        <button
          onClick={handleSearch}
          className="inline-flex items-center px-8 py-3 border border-transparent text-base font-medium rounded-lg text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors"
        >
          <Search className="h-5 w-5 mr-2" />
          {hasFilters ? "Search with Filters" : "Browse All Products"}
        </button>
      </div>

      {/* Filter Summary */}
      {hasFilters && (
        <div className="mt-4 p-3 bg-gray-50 rounded-lg">
          <div className="text-sm text-gray-600">
            <span className="font-medium">Active filters:</span>
            <div className="mt-1 flex flex-wrap gap-2">
              {selectedCategories.map((category) => (
                <span
                  key={category}
                  className="inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-blue-100 text-blue-800"
                >
                  Category: {category}
                </span>
              ))}
              {selectedBrands.map((brand) => (
                <span
                  key={brand}
                  className="inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-green-100 text-green-800"
                >
                  Brand: {brand}
                </span>
              ))}
              {selectedSellers.map((seller) => (
                <span
                  key={seller}
                  className="inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-purple-100 text-purple-800"
                >
                  Seller: {seller}
                </span>
              ))}
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default HomepageFilters;

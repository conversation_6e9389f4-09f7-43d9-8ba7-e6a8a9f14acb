#!/usr/bin/env python3
"""
Test script to verify existing functionality remains intact after RBAC implementation
"""

import asyncio
import asyncpg
import sys
from pathlib import Path

# Add the app directory to Python path
sys.path.append(str(Path(__file__).parent))

from app.config import settings

async def test_existing_functionality():
    """Test that existing functionality still works after RBAC implementation."""
    
    print("🔍 Testing Existing Functionality After RBAC Implementation")
    print("=" * 60)
    
    try:
        # Connect to database
        db_url = settings.DATABASE_URL.replace("postgresql+asyncpg://", "postgresql://")
        conn = await asyncpg.connect(db_url)
        print("✅ Database connection successful")
        
        # Test 1: Verify existing tables are intact
        print("\n📋 Test 1: Verifying existing tables...")
        existing_tables = await conn.fetch("""
            SELECT table_name 
            FROM information_schema.tables 
            WHERE table_schema = 'public' 
            AND table_name IN ('users', 'products', 'shopping_lists', 'shopping_list_items', 'search_cache')
            ORDER BY table_name
        """)
        
        expected_tables = {'users', 'products', 'shopping_lists', 'shopping_list_items', 'search_cache'}
        found_tables = {row['table_name'] for row in existing_tables}
        
        if expected_tables.issubset(found_tables):
            print("✅ All existing tables are intact")
            for table in expected_tables:
                print(f"  ✅ {table}")
        else:
            missing = expected_tables - found_tables
            print(f"❌ Missing tables: {missing}")
            return False
        
        # Test 2: Verify users table structure
        print("\n👥 Test 2: Verifying users table structure...")
        user_columns = await conn.fetch("""
            SELECT column_name, data_type 
            FROM information_schema.columns 
            WHERE table_name = 'users' 
            ORDER BY column_name
        """)
        
        expected_user_columns = {
            'id', 'email', 'hashed_password', 'full_name', 
            'is_active', 'is_superuser', 'created_at', 'updated_at'
        }
        found_user_columns = {row['column_name'] for row in user_columns}
        
        if expected_user_columns.issubset(found_user_columns):
            print("✅ Users table structure is intact")
        else:
            missing = expected_user_columns - found_user_columns
            print(f"❌ Missing user columns: {missing}")
            return False
        
        # Test 3: Verify existing user data
        print("\n🔐 Test 3: Verifying existing user authentication...")
        test_users = await conn.fetch("""
            SELECT id, email, hashed_password, is_active, is_superuser 
            FROM users 
            WHERE email LIKE '%test%' 
            LIMIT 3
        """)
        
        if test_users:
            print(f"✅ Found {len(test_users)} test users with intact data")
            for user in test_users:
                print(f"  - {user['email']} (active: {user['is_active']}, superuser: {user['is_superuser']})")
        else:
            print("⚠️  No test users found, but this might be expected")
        
        # Test 4: Verify products table and search functionality
        print("\n🔍 Test 4: Verifying products table and search...")
        product_count = await conn.fetchval("SELECT COUNT(*) FROM products")
        print(f"✅ Products table has {product_count} products")
        
        if product_count > 0:
            # Test search functionality
            search_results = await conn.fetch("""
                SELECT id, name, mfr 
                FROM products 
                WHERE name ILIKE '%bur%' OR mfr ILIKE '%bur%'
                LIMIT 5
            """)
            
            if search_results:
                print(f"✅ Search functionality works - found {len(search_results)} results for 'bur'")
                for product in search_results[:3]:
                    print(f"  - {product['name']} (MFR: {product['mfr']})")
            else:
                print("⚠️  No search results for 'bur', but search query executed successfully")
        
        # Test 5: Verify shopping lists functionality
        print("\n🛒 Test 5: Verifying shopping lists functionality...")
        shopping_lists = await conn.fetch("""
            SELECT sl.id, sl.name, sl.user_id, u.email
            FROM shopping_lists sl
            JOIN users u ON sl.user_id = u.id
            LIMIT 5
        """)
        
        if shopping_lists:
            print(f"✅ Shopping lists functionality intact - found {len(shopping_lists)} lists")
            for sl in shopping_lists[:3]:
                print(f"  - '{sl['name']}' owned by {sl['email']}")
        else:
            print("⚠️  No shopping lists found, but table structure is intact")
        
        # Test 6: Verify shopping list items
        print("\n📝 Test 6: Verifying shopping list items...")
        list_items = await conn.fetch("""
            SELECT sli.id, sli.shopping_list_id, sli.product_id, p.name
            FROM shopping_list_items sli
            JOIN products p ON sli.product_id = p.id
            LIMIT 5
        """)
        
        if list_items:
            print(f"✅ Shopping list items intact - found {len(list_items)} items")
            for item in list_items[:3]:
                print(f"  - Product: {item['name']}")
        else:
            print("⚠️  No shopping list items found, but table structure is intact")
        
        # Test 7: Test User model backward compatibility
        print("\n🧪 Test 7: Testing User model backward compatibility...")
        from app.models.user import UserRepository
        
        if test_users:
            test_user_id = test_users[0]['id']
            
            # Test original methods still work
            user = await UserRepository.get_user_by_id(conn, test_user_id)
            if user:
                print(f"✅ Original User.get_user_by_id() works for {user.email}")
                print(f"   User dict: {user.to_dict()}")
                
                # Test new RBAC methods
                user_with_roles = await UserRepository.get_user_with_roles_and_permissions(conn, test_user_id)
                if user_with_roles:
                    print(f"✅ New RBAC methods work - roles: {user_with_roles.roles}")
                    print(f"   Has products.read: {user_with_roles.has_permission('products.read')}")
                else:
                    print("❌ New RBAC methods failed")
                    return False
            else:
                print("❌ Original User methods failed")
                return False
        
        # Test 8: Verify database indexes and performance
        print("\n⚡ Test 8: Verifying database indexes...")
        indexes = await conn.fetch("""
            SELECT indexname, tablename 
            FROM pg_indexes 
            WHERE schemaname = 'public' 
            AND tablename IN ('products', 'users', 'shopping_lists', 'roles', 'permissions', 'user_roles')
            ORDER BY tablename, indexname
        """)
        
        print(f"✅ Found {len(indexes)} indexes across key tables")
        
        # Group by table
        table_indexes = {}
        for idx in indexes:
            table = idx['tablename']
            if table not in table_indexes:
                table_indexes[table] = []
            table_indexes[table].append(idx['indexname'])
        
        for table, idx_list in table_indexes.items():
            print(f"  {table}: {len(idx_list)} indexes")
        
        print("\n🎉 All Existing Functionality Tests Passed!")
        print("=" * 60)
        print("✅ RBAC implementation preserves all existing functionality")
        print("✅ Search functionality remains intact")
        print("✅ Shopping list functionality remains intact") 
        print("✅ User authentication remains intact")
        print("✅ Database performance indexes are preserved")
        
        return True
        
    except Exception as e:
        print(f"\n❌ Existing Functionality Test Failed: {e}")
        import traceback
        traceback.print_exc()
        return False
        
    finally:
        if 'conn' in locals():
            await conn.close()
            print("\n📡 Database connection closed")

if __name__ == "__main__":
    success = asyncio.run(test_existing_functionality())
    sys.exit(0 if success else 1)

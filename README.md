# ProfiDent - Dental Product Price Comparison Platform

A comprehensive web application enabling dental professionals to compare product pricing across 10 major dental suppliers using intelligent search and matching capabilities.

## 🦷 Project Overview

ProfiDent leverages a consolidated dataset of **339,078 products** from 10 dental suppliers to provide:
- **Instant Product Search**: Fast full-text search across all suppliers
- **Price Comparison**: Compare identical products across different sellers
- **Shopping Lists**: Save and manage product lists for easy ordering
- **Advanced Search**: Semantic similarity search for product discovery (Phase 3)

### Supported Suppliers
- Benco Dental
- Darby Dental
- DDS Lab
- Frontier Dental
- Henry <PERSON>
- Midwest Dental
- Net32
- Optimus Dental
- Safco Dental
- TDSC

## 🏗️ Architecture

### Backend
- **FastAPI** (Python) - High-performance async API
- **PostgreSQL** - Database with full-text search capabilities
- **Redis** - Caching layer for optimal performance
- **JWT Authentication** - Secure user management

### Frontend
- **React** with **TypeScript** - Modern, type-safe UI
- **Tailwind CSS** - Utility-first styling
- **Zustand** - Lightweight state management
- **React Query** - Server state management

### Search Technology
- **Phase 1**: PostgreSQL full-text search (instant results)
- **Phase 3**: Vector similarity search with sentence-transformers

## 🚀 Quick Start

### Prerequisites
- Python 3.9+
- Node.js 18+
- PostgreSQL 14+
- Redis (optional, for caching)

### Development Setup

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd profident
   ```

2. **Backend Setup**
   ```bash
   cd backend
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   pip install -r requirements.txt
   ```

3. **Database Setup**
   ```bash
   # Create PostgreSQL database
   createdb profident
   
   # Run migrations
   alembic upgrade head
   ```

4. **Frontend Setup**
   ```bash
   cd frontend
   npm install
   npm run dev
   ```

5. **Data Import**
   ```bash
   # Import product data (339K products)
   python scripts/data_migration.py
   ```

## 📁 Project Structure

```
profident/
├── backend/           # FastAPI application
├── frontend/          # React application
├── scripts/           # Data migration and utilities
├── example/           # Original prototype (reference)
├── docs/              # Documentation
└── PROJECT_CONTEXT.md # Comprehensive development guide
```

## 🔍 Search Features

### Current (Phase 1)
- **Instant Search**: Real-time product search with autocomplete
- **Full-Text Search**: PostgreSQL-powered text search
- **Product Matching**: Find same products across all suppliers
- **Category Filtering**: Filter by product categories and brands

### Planned (Phase 3)
- **Semantic Search**: AI-powered similarity search
- **Related Products**: Discover similar items
- **Search Analytics**: Track and improve search performance

## 🛒 Core Features

- ✅ **Product Search & Discovery**
- ✅ **Multi-Supplier Price Comparison**
- ✅ **Shopping List Management**
- ✅ **User Authentication & Profiles**
- ✅ **Responsive Design**
- 🔄 **Advanced Vector Search** (Phase 3)
- 🔄 **Price History Tracking** (Future)
- 🔄 **Mobile Application** (Future)

## 📊 Database

### Product Data Structure
```json
{
  "mfr": "6010049",                    // Manufacturer code (primary key)
  "name": "Product Name",              // Display name
  "url": "https://seller.com/...",     // Product URL
  "maincat": "Acrylics",              // Main category
  "brand": "DMG America",             // Brand name
  "manufactured_by": "dmg-america",   // Normalized manufacturer
  "category": "Prosthodontics...",    // Detailed category
  "seller": "benco",                  // Seller identifier
  "price": "1 @ 240.69ea.",          // Price information
  "search_text": "searchable text"   // Optimized search field
}
```

## 🧪 Testing

```bash
# Backend tests
cd backend
pytest

# Frontend tests
cd frontend
npm test
```

## 🚢 Deployment

### Development
```bash
docker-compose -f docker-compose.dev.yml up
```

### Production
```bash
docker-compose up -d
```

## 📈 Performance

- **Search Response Time**: < 100ms target
- **Database Size**: 339,078 products
- **Concurrent Users**: Designed for 1000+ simultaneous users
- **Uptime Target**: 99.9%

## 🤝 Contributing

1. Create a feature branch from `develop`
2. Make your changes
3. Add tests for new functionality
4. Submit a pull request

### Branch Structure
- `master` - Production-ready code
- `develop` - Integration branch for features
- `feature/*` - Individual feature development

## 📝 Documentation

- **[PROJECT_CONTEXT.md](PROJECT_CONTEXT.md)** - Comprehensive development guide
- **[API Documentation](docs/API.md)** - API endpoint reference
- **[Deployment Guide](docs/DEPLOYMENT.md)** - Production deployment instructions

## 🔒 Security

- JWT-based authentication
- SQL injection prevention
- XSS protection
- HTTPS enforcement
- Rate limiting

## 📄 License

This project is proprietary software for dental practice management.

## 🆘 Support

For development questions, refer to the [PROJECT_CONTEXT.md](PROJECT_CONTEXT.md) file which contains detailed implementation guidelines and technical specifications.

---

**ProfiDent** - Simplifying dental product procurement through intelligent price comparison.

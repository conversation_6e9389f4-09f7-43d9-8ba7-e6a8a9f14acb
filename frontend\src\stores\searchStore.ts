import { create } from "zustand";
import {
  Product,
  SearchFilters,
  SearchRequest,
  ProductSuggestion,
} from "../types";

interface SearchState {
  query: string;
  results: Product[];
  total: number;
  isLoading: boolean;
  error: string | null;
  filters: SearchFilters;
  searchTime: number;
  suggestions: ProductSuggestion[];
  popularSearches: string[];
  recentSearches: string[];
  pagination: {
    page: number;
    limit: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}

interface SearchActions {
  setQuery: (query: string) => void;
  setResults: (results: Product[], total: number, searchTime: number) => void;
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  setFilters: (filters: Partial<SearchFilters>) => void;
  clearFilters: () => void;
  setSuggestions: (suggestions: ProductSuggestion[]) => void;
  setPopularSearches: (searches: string[]) => void;
  addRecentSearch: (query: string) => void;
  clearRecentSearches: () => void;
  setPagination: (pagination: Partial<SearchState["pagination"]>) => void;
  reset: () => void;
}

type SearchStore = SearchState & SearchActions;

const initialState: SearchState = {
  query: "",
  results: [],
  total: 0,
  isLoading: false,
  error: null,
  filters: {},
  searchTime: 0,
  suggestions: [],
  popularSearches: [],
  recentSearches: JSON.parse(localStorage.getItem("recent-searches") || "[]"),
  pagination: {
    page: 1,
    limit: 20,
    hasNext: false,
    hasPrev: false,
  },
};

export const useSearchStore = create<SearchStore>((set, get) => ({
  ...initialState,

  setQuery: (query: string) => {
    set({ query });
  },

  setResults: (results: Product[], total: number, searchTime: number) => {
    set({
      results,
      total,
      searchTime,
      isLoading: false,
      error: null,
    });
  },

  setLoading: (isLoading: boolean) => {
    set({ isLoading });
  },

  setError: (error: string | null) => {
    set({ error, isLoading: false });
  },

  setFilters: (newFilters: Partial<SearchFilters>) => {
    set((state) => ({
      filters: { ...state.filters, ...newFilters },
    }));
  },

  clearFilters: () => {
    set({ filters: {} });
  },

  setSuggestions: (suggestions: ProductSuggestion[]) => {
    set({ suggestions });
  },

  setPopularSearches: (popularSearches: string[]) => {
    set({ popularSearches });
  },

  addRecentSearch: (query: string) => {
    if (!query.trim()) return;

    const currentRecent = get().recentSearches;
    const newRecent = [
      query,
      ...currentRecent.filter((q) => q !== query),
    ].slice(0, 10); // Keep only last 10 searches

    set({ recentSearches: newRecent });
    localStorage.setItem("recent-searches", JSON.stringify(newRecent));
  },

  clearRecentSearches: () => {
    set({ recentSearches: [] });
    localStorage.removeItem("recent-searches");
  },

  setPagination: (newPagination: Partial<SearchState["pagination"]>) => {
    set((state) => ({
      pagination: { ...state.pagination, ...newPagination },
    }));
  },

  reset: () => {
    set({
      ...initialState,
      recentSearches: get().recentSearches, // Keep recent searches
    });
  },
}));

// Helper function to build search request from store state
export const buildSearchRequest = (store: SearchState): SearchRequest => {
  const { query, filters, pagination } = store;

  return {
    q: query,
    limit: pagination.limit,
    offset: (pagination.page - 1) * pagination.limit,
    search_type: filters.search_type || "fulltext",
    category: filters.category,
    brand: filters.brand,
    seller: filters.seller,
    manufactured_by: filters.manufactured_by,
  };
};

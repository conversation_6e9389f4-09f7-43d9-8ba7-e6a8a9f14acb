"""
User database models and operations
"""

import uuid
from datetime import datetime
from typing import Optional, List, Set
from asyncpg import Connection

try:
    from ..utils.security import get_password_hash, verify_password, verify_and_upgrade_password
except ImportError:
    from utils.security import get_password_hash, verify_password, verify_and_upgrade_password


class User:
    """User model for database operations."""

    def __init__(
        self,
        id: str = None,
        email: str = None,
        hashed_password: str = None,
        full_name: str = None,
        is_active: bool = True,
        is_superuser: bool = False,
        created_at: datetime = None,
        updated_at: datetime = None,
        roles: List[str] = None,  # Role names for this user
        permissions: Set[str] = None  # Permission names for this user
    ):
        self.id = id or str(uuid.uuid4())
        self.email = email
        self.hashed_password = hashed_password
        self.full_name = full_name
        self.is_active = is_active
        self.is_superuser = is_superuser
        self.created_at = created_at
        self.updated_at = updated_at
        self.roles = roles or []
        self.permissions = permissions or set()
    
    @classmethod
    def from_record(cls, record) -> "User":
        """Create User instance from database record."""
        if not record:
            return None
        
        return cls(
            id=str(record["id"]),
            email=record["email"],
            hashed_password=record["hashed_password"],
            full_name=record["full_name"],
            is_active=record["is_active"],
            is_superuser=record["is_superuser"],
            created_at=record["created_at"],
            updated_at=record["updated_at"]
        )
    
    def to_dict(self) -> dict:
        """Convert user to dictionary (excluding password)."""
        return {
            "id": self.id,
            "email": self.email,
            "full_name": self.full_name,
            "is_active": self.is_active,
            "is_superuser": self.is_superuser,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None,
            "roles": self.roles,
            "permissions": list(self.permissions)
        }

    # RBAC Role and Permission Methods
    def has_role(self, role_name: str) -> bool:
        """Check if user has a specific role."""
        return role_name in self.roles

    def has_any_role(self, role_names: List[str]) -> bool:
        """Check if user has any of the specified roles."""
        return any(role in self.roles for role in role_names)

    def has_all_roles(self, role_names: List[str]) -> bool:
        """Check if user has all of the specified roles."""
        return all(role in self.roles for role in role_names)

    def has_permission(self, permission_name: str) -> bool:
        """Check if user has a specific permission."""
        # Superuser has all permissions (backward compatibility)
        if self.is_superuser:
            return True
        return permission_name in self.permissions

    def has_any_permission(self, permission_names: List[str]) -> bool:
        """Check if user has any of the specified permissions."""
        # Superuser has all permissions (backward compatibility)
        if self.is_superuser:
            return True
        return any(perm in self.permissions for perm in permission_names)

    def has_all_permissions(self, permission_names: List[str]) -> bool:
        """Check if user has all of the specified permissions."""
        # Superuser has all permissions (backward compatibility)
        if self.is_superuser:
            return True
        return all(perm in self.permissions for perm in permission_names)

    def is_admin(self) -> bool:
        """Check if user has admin privileges (superuser or superadmin role)."""
        return self.is_superuser or self.has_role('superadmin')

    def can_manage_users(self) -> bool:
        """Check if user can manage other users."""
        return self.has_permission('users.manage') or self.is_admin()

    def can_assign_roles(self) -> bool:
        """Check if user can assign roles to other users."""
        return self.has_permission('users.manage_roles') or self.is_admin()


class UserRepository:
    """User database operations."""
    
    @staticmethod
    async def create_user(
        conn: Connection,
        email: str,
        password: str,
        full_name: str = None
    ) -> User:
        """Create a new user."""
        user_id = str(uuid.uuid4())
        hashed_password = get_password_hash(password)
        
        query = """
            INSERT INTO users (id, email, hashed_password, full_name)
            VALUES ($1, $2, $3, $4)
            RETURNING *
        """
        
        record = await conn.fetchrow(query, user_id, email, hashed_password, full_name)
        return User.from_record(record)
    
    @staticmethod
    async def get_user_by_id(conn: Connection, user_id: str) -> Optional[User]:
        """Get user by ID."""
        query = "SELECT * FROM users WHERE id = $1"
        record = await conn.fetchrow(query, user_id)
        return User.from_record(record)
    
    @staticmethod
    async def get_user_by_email(conn: Connection, email: str) -> Optional[User]:
        """Get user by email."""
        query = "SELECT * FROM users WHERE email = $1"
        record = await conn.fetchrow(query, email)
        return User.from_record(record)
    
    @staticmethod
    async def update_user(
        conn: Connection,
        user_id: str,
        update_data = None,
        email: str = None,
        full_name: str = None,
        is_active: bool = None
    ) -> Optional[User]:
        """Update user information."""
        # Handle both direct parameters and update_data object
        if update_data:
            email = getattr(update_data, 'email', None) if email is None else email
            full_name = getattr(update_data, 'full_name', None) if full_name is None else full_name
            is_active = getattr(update_data, 'is_active', None) if is_active is None else is_active

        updates = []
        values = []
        param_count = 1

        if email is not None:
            updates.append(f"email = ${param_count}")
            values.append(email)
            param_count += 1

        if full_name is not None:
            updates.append(f"full_name = ${param_count}")
            values.append(full_name)
            param_count += 1

        if is_active is not None:
            updates.append(f"is_active = ${param_count}")
            values.append(is_active)
            param_count += 1

        if not updates:
            return await UserRepository.get_user_by_id(conn, user_id)

        updates.append(f"updated_at = NOW()")
        values.append(user_id)

        query = f"""
            UPDATE users
            SET {', '.join(updates)}
            WHERE id = ${param_count}
            RETURNING *
        """

        record = await conn.fetchrow(query, *values)
        return User.from_record(record)
    
    @staticmethod
    async def update_password(
        conn: Connection,
        user_id: str,
        new_password: str
    ) -> bool:
        """Update user password."""
        hashed_password = get_password_hash(new_password)

        query = """
            UPDATE users
            SET hashed_password = $1, updated_at = NOW()
            WHERE id = $2
        """

        result = await conn.execute(query, hashed_password, user_id)
        return "UPDATE 1" in result

    @staticmethod
    async def update_password_hash(
        conn: Connection,
        user_id: str,
        hashed_password: str
    ) -> bool:
        """Update user password with pre-hashed password (for hash upgrades)."""
        query = """
            UPDATE users
            SET hashed_password = $1, updated_at = NOW()
            WHERE id = $2
        """

        result = await conn.execute(query, hashed_password, user_id)
        return "UPDATE 1" in result
    
    @staticmethod
    async def authenticate_user(
        conn: Connection,
        email: str,
        password: str
    ) -> Optional[User]:
        """
        Authenticate user with email and password.
        Automatically upgrades password hash if needed.
        """
        user = await UserRepository.get_user_by_email(conn, email)

        if not user:
            return None

        if not user.is_active:
            return None

        # Verify password and check if hash needs upgrade
        is_valid, new_hash = verify_and_upgrade_password(password, user.hashed_password)

        if not is_valid:
            return None

        # If password hash needs upgrade, update it
        if new_hash:
            try:
                await UserRepository.update_password_hash(conn, user.id, new_hash)
                print(f"Password hash upgraded for user {user.email}")
            except Exception as e:
                # Log error but don't fail authentication
                print(f"Warning: Failed to upgrade password hash for user {user.email}: {e}")

        return user
    
    @staticmethod
    async def delete_user(conn: Connection, user_id: str) -> bool:
        """Delete user (soft delete by setting is_active to False)."""
        query = """
            UPDATE users 
            SET is_active = FALSE, updated_at = NOW()
            WHERE id = $1
        """
        
        result = await conn.execute(query, user_id)
        return "UPDATE 1" in result
    
    @staticmethod
    async def list_users(
        conn: Connection,
        limit: int = 50,
        offset: int = 0,
        active_only: bool = True
    ) -> List[User]:
        """List users with pagination."""
        where_clause = "WHERE is_active = TRUE" if active_only else ""
        
        query = f"""
            SELECT * FROM users 
            {where_clause}
            ORDER BY created_at DESC
            LIMIT $1 OFFSET $2
        """
        
        records = await conn.fetch(query, limit, offset)
        return [User.from_record(record) for record in records]
    
    @staticmethod
    async def count_users(conn: Connection, active_only: bool = True) -> int:
        """Count total users."""
        where_clause = "WHERE is_active = TRUE" if active_only else ""
        query = f"SELECT COUNT(*) FROM users {where_clause}"
        return await conn.fetchval(query)
    
    @staticmethod
    async def email_exists(conn: Connection, email: str) -> bool:
        """Check if email already exists."""
        query = "SELECT EXISTS(SELECT 1 FROM users WHERE email = $1)"
        return await conn.fetchval(query, email)

    @staticmethod
    async def list_users_with_filters(
        conn: Connection,
        skip: int = 0,
        limit: int = 100,
        search: Optional[str] = None,
        is_active: Optional[bool] = None,
        is_superuser: Optional[bool] = None
    ) -> List[User]:
        """List users with advanced filtering and pagination."""
        conditions = []
        values = []
        param_count = 1

        if search:
            conditions.append(f"(email ILIKE ${param_count} OR full_name ILIKE ${param_count})")
            values.append(f"%{search}%")
            param_count += 1

        if is_active is not None:
            conditions.append(f"is_active = ${param_count}")
            values.append(is_active)
            param_count += 1

        if is_superuser is not None:
            conditions.append(f"is_superuser = ${param_count}")
            values.append(is_superuser)
            param_count += 1

        where_clause = "WHERE " + " AND ".join(conditions) if conditions else ""

        query = f"""
            SELECT * FROM users
            {where_clause}
            ORDER BY created_at DESC
            LIMIT ${param_count} OFFSET ${param_count + 1}
        """

        values.extend([limit, skip])
        records = await conn.fetch(query, *values)
        return [User.from_record(record) for record in records]

    @staticmethod
    async def activate_user(conn: Connection, user_id: str) -> bool:
        """Activate a user account."""
        query = """
            UPDATE users
            SET is_active = TRUE, updated_at = NOW()
            WHERE id = $1
        """

        result = await conn.execute(query, user_id)
        return "UPDATE 1" in result

    @staticmethod
    async def make_superuser(conn: Connection, user_id: str) -> bool:
        """Grant superuser privileges to a user."""
        query = """
            UPDATE users
            SET is_superuser = TRUE, updated_at = NOW()
            WHERE id = $1
        """

        result = await conn.execute(query, user_id)
        return "UPDATE 1" in result

    @staticmethod
    async def remove_superuser(conn: Connection, user_id: str) -> bool:
        """Remove superuser privileges from a user."""
        query = """
            UPDATE users
            SET is_superuser = FALSE, updated_at = NOW()
            WHERE id = $1
        """

        result = await conn.execute(query, user_id)
        return "UPDATE 1" in result

    @staticmethod
    async def get_user_statistics(conn: Connection) -> dict:
        """Get user statistics for admin dashboard."""
        stats_query = """
            SELECT
                COUNT(*) as total_users,
                COUNT(*) FILTER (WHERE is_active = TRUE) as active_users,
                COUNT(*) FILTER (WHERE is_active = FALSE) as inactive_users,
                COUNT(*) FILTER (WHERE is_superuser = TRUE) as superuser_count,
                COUNT(*) FILTER (WHERE created_at >= NOW() - INTERVAL '30 days') as new_users_last_30_days,
                COUNT(*) FILTER (WHERE created_at >= NOW() - INTERVAL '7 days') as new_users_last_7_days
            FROM users
        """

        result = await conn.fetchrow(stats_query)
        return dict(result) if result else {}

    # RBAC Methods for User Role and Permission Management
    @staticmethod
    async def get_user_with_roles_and_permissions(conn: Connection, user_id: str) -> Optional[User]:
        """Get user with their roles and permissions loaded."""
        # Get basic user info
        user = await UserRepository.get_user_by_id(conn, user_id)
        if not user:
            return None

        # Load user roles
        roles_query = """
            SELECT r.name
            FROM roles r
            JOIN user_roles ur ON r.id = ur.role_id
            WHERE ur.user_id = $1 AND ur.is_active = TRUE AND r.is_active = TRUE
        """
        role_records = await conn.fetch(roles_query, user_id)
        user.roles = [record['name'] for record in role_records]

        # Load user permissions (through roles)
        permissions_query = """
            SELECT DISTINCT p.name
            FROM permissions p
            JOIN role_permissions rp ON p.id = rp.permission_id
            JOIN roles r ON rp.role_id = r.id
            JOIN user_roles ur ON r.id = ur.role_id
            WHERE ur.user_id = $1
            AND ur.is_active = TRUE
            AND r.is_active = TRUE
            AND rp.is_active = TRUE
        """
        permission_records = await conn.fetch(permissions_query, user_id)
        user.permissions = {record['name'] for record in permission_records}

        return user

    @staticmethod
    async def get_user_roles(conn: Connection, user_id: str) -> List[str]:
        """Get list of role names for a user."""
        query = """
            SELECT r.name
            FROM roles r
            JOIN user_roles ur ON r.id = ur.role_id
            WHERE ur.user_id = $1 AND ur.is_active = TRUE AND r.is_active = TRUE
        """
        records = await conn.fetch(query, user_id)
        return [record['name'] for record in records]

    @staticmethod
    async def get_user_permissions(conn: Connection, user_id: str) -> Set[str]:
        """Get set of permission names for a user."""
        query = """
            SELECT DISTINCT p.name
            FROM permissions p
            JOIN role_permissions rp ON p.id = rp.permission_id
            JOIN roles r ON rp.role_id = r.id
            JOIN user_roles ur ON r.id = ur.role_id
            WHERE ur.user_id = $1
            AND ur.is_active = TRUE
            AND r.is_active = TRUE
            AND rp.is_active = TRUE
        """
        records = await conn.fetch(query, user_id)
        return {record['name'] for record in records}

    @staticmethod
    async def assign_role_to_user(
        conn: Connection,
        user_id: str,
        role_id: str,  # Changed to accept role_id directly
        assigned_by: str = None
    ) -> "UserRoleAssignment":
        """Assign a role to a user by role ID."""
        # Insert user role assignment
        insert_query = """
            INSERT INTO user_roles (user_id, role_id, assigned_by)
            VALUES ($1, $2, $3)
            ON CONFLICT (user_id, role_id) DO UPDATE SET
                is_active = TRUE,
                assigned_by = EXCLUDED.assigned_by,
                assigned_at = NOW(),
                updated_at = NOW()
            RETURNING *
        """

        try:
            record = await conn.fetchrow(insert_query, user_id, role_id, assigned_by)
            return UserRoleAssignment.from_record(record)
        except Exception as e:
            raise Exception(f"Failed to assign role to user: {str(e)}")

    @staticmethod
    async def assign_role_to_user_by_name(
        conn: Connection,
        user_id: str,
        role_name: str,
        assigned_by: str = None
    ) -> bool:
        """Assign a role to a user by role name (legacy method)."""
        # Get role ID by name
        role_query = "SELECT id FROM roles WHERE name = $1 AND is_active = TRUE"
        role_id = await conn.fetchval(role_query, role_name)

        if not role_id:
            return False

        # Insert user role assignment
        insert_query = """
            INSERT INTO user_roles (user_id, role_id, assigned_by)
            VALUES ($1, $2, $3)
            ON CONFLICT (user_id, role_id) DO UPDATE SET
                is_active = TRUE,
                assigned_by = EXCLUDED.assigned_by,
                assigned_at = NOW(),
                updated_at = NOW()
        """

        try:
            await conn.execute(insert_query, user_id, role_id, assigned_by)
            return True
        except Exception:
            return False

    @staticmethod
    async def revoke_role_from_user(
        conn: Connection,
        user_id: str,
        role_id: str
    ) -> bool:
        """Revoke a role from a user by role ID."""
        query = """
            UPDATE user_roles
            SET is_active = FALSE, updated_at = NOW()
            WHERE user_id = $1 AND role_id = $2 AND is_active = TRUE
        """

        try:
            result = await conn.execute(query, user_id, role_id)
            return "UPDATE 1" in result
        except Exception:
            return False

    @staticmethod
    async def remove_role_from_user(conn: Connection, user_id: str, role_name: str) -> bool:
        """Remove a role from a user by role name (legacy method)."""
        query = """
            UPDATE user_roles
            SET is_active = FALSE, updated_at = NOW()
            WHERE user_id = $1
            AND role_id = (SELECT id FROM roles WHERE name = $2)
        """

        try:
            result = await conn.execute(query, user_id, role_name)
            return "UPDATE 1" in result
        except Exception:
            return False

    @staticmethod
    async def get_users_by_role(
        conn: Connection,
        role_id: str,
        skip: int = 0,
        limit: int = 20
    ) -> List["UserRoleAssignment"]:
        """Get all users assigned to a specific role."""
        query = """
            SELECT ur.*, u.email, u.full_name
            FROM user_roles ur
            JOIN users u ON ur.user_id = u.id
            WHERE ur.role_id = $1 AND ur.is_active = TRUE
            ORDER BY ur.assigned_at DESC
            OFFSET $2 LIMIT $3
        """

        records = await conn.fetch(query, role_id, skip, limit)
        return [UserRoleAssignment.from_record(record) for record in records]

    @staticmethod
    async def count_users_by_role(conn: Connection, role_id: str) -> int:
        """Count users assigned to a specific role."""
        query = """
            SELECT COUNT(*) FROM user_roles
            WHERE role_id = $1 AND is_active = TRUE
        """
        return await conn.fetchval(query, role_id)

    @staticmethod
    async def list_user_role_assignments(
        conn: Connection,
        skip: int = 0,
        limit: int = 20,
        user_id: str = None,
        role_id: str = None,
        is_active: bool = None
    ) -> List["UserRoleAssignment"]:
        """List user role assignments with optional filtering."""
        conditions = []
        params = []
        param_count = 0

        if user_id:
            param_count += 1
            conditions.append(f"ur.user_id = ${param_count}")
            params.append(user_id)

        if role_id:
            param_count += 1
            conditions.append(f"ur.role_id = ${param_count}")
            params.append(role_id)

        if is_active is not None:
            param_count += 1
            conditions.append(f"ur.is_active = ${param_count}")
            params.append(is_active)

        where_clause = "WHERE " + " AND ".join(conditions) if conditions else ""

        param_count += 1
        offset_param = f"${param_count}"
        params.append(skip)

        param_count += 1
        limit_param = f"${param_count}"
        params.append(limit)

        query = f"""
            SELECT ur.*, u.email, u.full_name, r.name as role_name
            FROM user_roles ur
            JOIN users u ON ur.user_id = u.id
            JOIN roles r ON ur.role_id = r.id
            {where_clause}
            ORDER BY ur.assigned_at DESC
            OFFSET {offset_param} LIMIT {limit_param}
        """

        records = await conn.fetch(query, *params)
        return [UserRoleAssignment.from_record(record) for record in records]

    @staticmethod
    async def count_user_role_assignments(
        conn: Connection,
        user_id: str = None,
        role_id: str = None,
        is_active: bool = None
    ) -> int:
        """Count user role assignments with optional filtering."""
        conditions = []
        params = []
        param_count = 0

        if user_id:
            param_count += 1
            conditions.append(f"user_id = ${param_count}")
            params.append(user_id)

        if role_id:
            param_count += 1
            conditions.append(f"role_id = ${param_count}")
            params.append(role_id)

        if is_active is not None:
            param_count += 1
            conditions.append(f"is_active = ${param_count}")
            params.append(is_active)

        where_clause = "WHERE " + " AND ".join(conditions) if conditions else ""

        query = f"SELECT COUNT(*) FROM user_roles {where_clause}"
        return await conn.fetchval(query, *params)

    @staticmethod
    async def update_user_role_assignment(
        conn: Connection,
        assignment_id: str,
        update_data: dict
    ) -> "UserRoleAssignment":
        """Update a user role assignment."""
        if not update_data:
            return None

        set_clauses = []
        params = []
        param_count = 0

        for field, value in update_data.items():
            if field in ['is_active', 'notes']:
                param_count += 1
                set_clauses.append(f"{field} = ${param_count}")
                params.append(value)

        if not set_clauses:
            return None

        # Always update the updated_at timestamp
        param_count += 1
        set_clauses.append(f"updated_at = ${param_count}")
        params.append(datetime.now())

        # Add assignment_id parameter
        param_count += 1
        params.append(assignment_id)

        query = f"""
            UPDATE user_roles
            SET {', '.join(set_clauses)}
            WHERE id = ${param_count}
            RETURNING *
        """

        record = await conn.fetchrow(query, *params)
        return UserRoleAssignment.from_record(record) if record else None


class UserRoleAssignment:
    """User-Role assignment model."""

    def __init__(
        self,
        id: str,
        user_id: str,
        role_id: str,
        assigned_by: str = None,
        assigned_at: datetime = None,
        is_active: bool = True,
        notes: str = None,
        created_at: datetime = None,
        updated_at: datetime = None
    ):
        self.id = id
        self.user_id = user_id
        self.role_id = role_id
        self.assigned_by = assigned_by
        self.assigned_at = assigned_at
        self.is_active = is_active
        self.notes = notes
        self.created_at = created_at
        self.updated_at = updated_at

    @classmethod
    def from_record(cls, record) -> "UserRoleAssignment":
        """Create UserRoleAssignment instance from database record."""
        if not record:
            return None

        return cls(
            id=str(record["id"]),
            user_id=str(record["user_id"]),
            role_id=str(record["role_id"]),
            assigned_by=str(record["assigned_by"]) if record["assigned_by"] else None,
            assigned_at=record["assigned_at"],
            is_active=record["is_active"],
            notes=record.get("notes"),
            created_at=record.get("created_at"),
            updated_at=record.get("updated_at")
        )

    def to_dict(self) -> dict:
        """Convert assignment to dictionary."""
        return {
            "id": self.id,
            "user_id": self.user_id,
            "role_id": self.role_id,
            "assigned_by": self.assigned_by,
            "assigned_at": self.assigned_at.isoformat() if self.assigned_at else None,
            "is_active": self.is_active,
            "notes": self.notes,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None
        }

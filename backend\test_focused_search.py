#!/usr/bin/env python3
"""
Test focused search functionality with MFR and name fields only.
Validates search performance and accuracy after the focused search_text update.
"""

import asyncio
import asyncpg
import os
import sys
import time
from typing import List, Dict, Any

# Add the app directory to the path to import config
sys.path.append(os.path.join(os.path.dirname(__file__), 'app'))

try:
    from config import settings, get_database_url_sync
except ImportError:
    settings = None

class FocusedSearchTester:
    """Tests focused search functionality on MFR and name fields."""
    
    def __init__(self):
        if settings:
            self.db_url = get_database_url_sync()
        else:
            self.db_url = "postgresql://profident_user:profident_dev_password@localhost:5433/profident_dev"
        
        print(f"🔗 Using database URL: {self.db_url[:50]}...")
    
    async def get_connection(self):
        """Get database connection."""
        return await asyncpg.connect(self.db_url)
    
    async def test_search_performance(self):
        """Test search performance with focused search_text."""
        print("🚀 Testing focused search performance...")
        
        conn = await self.get_connection()
        try:
            # Test queries focusing on MFR and name
            test_queries = [
                "110402",  # MFR search
                "dental",  # Name search
                "implant", # Name search
                "crown",   # Name search
                "bridge"   # Name search
            ]
            
            total_time = 0
            total_results = 0
            
            for query in test_queries:
                start_time = time.time()
                
                # Test full-text search on focused search_text
                results = await conn.fetch("""
                    SELECT id, mfr, name, seller, search_text
                    FROM products 
                    WHERE to_tsvector('english', search_text) @@ to_tsquery('english', $1)
                    ORDER BY ts_rank(to_tsvector('english', search_text), to_tsquery('english', $1)) DESC
                    LIMIT 20
                """, query)
                
                query_time = (time.time() - start_time) * 1000
                total_time += query_time
                total_results += len(results)
                
                print(f"  🔍 '{query}': {len(results)} results in {query_time:.1f}ms")
                
                # Show sample results
                if results:
                    print(f"    📋 Sample results:")
                    for i, result in enumerate(results[:3]):
                        print(f"      {i+1}. MFR: {result['mfr']}, Name: {result['name'][:50]}...")
                        print(f"         Search text: {result['search_text'][:80]}...")
                    print()
            
            avg_time = total_time / len(test_queries)
            print(f"  📊 Average search time: {avg_time:.1f}ms")
            print(f"  📊 Total results found: {total_results}")
            
            # Performance check
            if avg_time < 500:
                print(f"  ✅ Performance target met: {avg_time:.1f}ms < 500ms")
            else:
                print(f"  ⚠️ Performance target missed: {avg_time:.1f}ms >= 500ms")
        
        finally:
            await conn.close()
    
    async def test_mfr_specific_search(self):
        """Test MFR-specific search functionality."""
        print("🔧 Testing MFR-specific search...")
        
        conn = await self.get_connection()
        try:
            # Get some sample MFR values
            sample_mfrs = await conn.fetch("""
                SELECT DISTINCT mfr FROM products 
                WHERE mfr IS NOT NULL AND mfr != ''
                ORDER BY mfr
                LIMIT 5
            """)
            
            print(f"📋 Testing with {len(sample_mfrs)} sample MFR codes:")
            
            for mfr_record in sample_mfrs:
                mfr = mfr_record['mfr']
                print(f"  🧪 Testing MFR: {mfr}")
                
                start_time = time.time()
                
                # Search for this specific MFR
                results = await conn.fetch("""
                    SELECT id, mfr, name, seller
                    FROM products 
                    WHERE to_tsvector('english', search_text) @@ to_tsquery('english', $1)
                    ORDER BY ts_rank(to_tsvector('english', search_text), to_tsquery('english', $1)) DESC
                    LIMIT 10
                """, mfr)
                
                query_time = (time.time() - start_time) * 1000
                
                print(f"    ✅ Found {len(results)} results in {query_time:.1f}ms")
                
                # Verify results contain the MFR
                matching_results = [r for r in results if r['mfr'] == mfr]
                print(f"    🎯 {len(matching_results)} results match exact MFR")
                
                if matching_results:
                    print(f"    📋 Sample matching result:")
                    result = matching_results[0]
                    print(f"      - MFR: {result['mfr']}")
                    print(f"      - Name: {result['name']}")
                    print(f"      - Seller: {result['seller']}")
                print()
        
        finally:
            await conn.close()
    
    async def verify_search_text_format(self):
        """Verify that search_text contains only MFR and name."""
        print("🔍 Verifying focused search_text format...")
        
        conn = await self.get_connection()
        try:
            # Get sample products to verify search_text format
            sample_products = await conn.fetch("""
                SELECT id, mfr, name, search_text, brand, category, seller
                FROM products
                WHERE mfr IS NOT NULL AND mfr != '' AND name IS NOT NULL
                LIMIT 10
            """)
            
            print(f"📋 Checking {len(sample_products)} sample products:")
            
            correct_format_count = 0
            
            for product in sample_products:
                expected_search_text = ' | '.join(filter(None, [
                    product['mfr'] or '',
                    product['name'] or ''
                ]))
                
                actual_search_text = product['search_text'] or ''
                
                is_correct = actual_search_text == expected_search_text
                if is_correct:
                    correct_format_count += 1
                
                print(f"  Product ID: {product['id']}")
                print(f"    MFR: {product['mfr']}")
                print(f"    Name: {product['name'][:50]}...")
                print(f"    Expected: {expected_search_text[:80]}...")
                print(f"    Actual:   {actual_search_text[:80]}...")
                print(f"    Format correct: {'✅' if is_correct else '❌'}")
                
                # Check that other fields are NOT in search_text
                other_fields = [product['brand'], product['category'], product['seller']]
                other_fields_in_search = any(
                    field and field.lower() in actual_search_text.lower() 
                    for field in other_fields 
                    if field and field not in [product['mfr'], product['name']]
                )
                
                if other_fields_in_search:
                    print(f"    ⚠️ Warning: Other fields detected in search_text")
                else:
                    print(f"    ✅ Search_text contains only MFR and name")
                print()
            
            print(f"📊 Summary: {correct_format_count}/{len(sample_products)} products have correct search_text format")
        
        finally:
            await conn.close()

async def main():
    """Main function to test focused search functionality."""
    tester = FocusedSearchTester()
    
    try:
        print("🎯 Testing Focused Search Functionality (MFR + Name Only)")
        print("=" * 60)
        
        await tester.verify_search_text_format()
        await tester.test_search_performance()
        await tester.test_mfr_specific_search()
        
        print("\n🎉 Focused search testing completed!")
        
    except Exception as e:
        print(f"❌ Error testing focused search: {e}")
        raise

if __name__ == "__main__":
    asyncio.run(main())

import React, { useState, useRef, useEffect } from "react";
import { ShoppingCart, Plus, Minus, X } from "lucide-react";
import { useShoppingLists } from "../../hooks/useShoppingLists";
import { cn } from "../../lib/utils";

interface ShoppingListIconProps {
  className?: string;
  onProductAdded?: (productId: number) => void;
}

export const ShoppingListIcon: React.FC<ShoppingListIconProps> = ({
  className,
  onProductAdded,
}) => {
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const [animatingProductId, setAnimatingProductId] = useState<number | null>(
    null
  );
  const dropdownRef = useRef<HTMLDivElement>(null);
  const iconRef = useRef<HTMLButtonElement>(null);

  const { data: shoppingListsData, isLoading } = useShoppingLists();
  const shoppingLists = shoppingListsData?.data?.items || [];

  // Get the default shopping list (first one or marked as default)
  const defaultList =
    shoppingLists.find((list) => list.is_default) || shoppingLists[0];

  // Calculate total items across all lists
  const totalItems = shoppingLists.reduce((total, list) => {
    return total + (list.summary?.total_items || 0);
  }, 0);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as Node) &&
        iconRef.current &&
        !iconRef.current.contains(event.target as Node)
      ) {
        setIsDropdownOpen(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, []);

  // Handle product addition animation
  const handleProductAdded = (productId: number) => {
    setAnimatingProductId(productId);

    // Trigger bounce animation
    if (iconRef.current) {
      iconRef.current.classList.add("animate-bounce");
      setTimeout(() => {
        iconRef.current?.classList.remove("animate-bounce");
        setAnimatingProductId(null);
      }, 600);
    }

    // Call parent callback
    onProductAdded?.(productId);
  };

  // Expose the animation trigger for external use
  useEffect(() => {
    const handleProductAddedEvent = (event: CustomEvent) => {
      handleProductAdded(event.detail.productId);
    };

    window.addEventListener(
      "shopping-list-product-added",
      handleProductAddedEvent as EventListener
    );
    return () => {
      window.removeEventListener(
        "shopping-list-product-added",
        handleProductAddedEvent as EventListener
      );
    };
  }, []);

  const toggleDropdown = () => {
    setIsDropdownOpen(!isDropdownOpen);
  };

  return (
    <div className="relative">
      {/* Shopping List Icon */}
      <button
        ref={iconRef}
        onClick={toggleDropdown}
        className={cn(
          "relative p-2 text-gray-600 hover:text-primary-600 hover:bg-primary-50 rounded-lg transition-all duration-200",
          isDropdownOpen && "text-primary-600 bg-primary-50",
          animatingProductId && "animate-pulse",
          className
        )}
        title="Shopping List"
      >
        <ShoppingCart className="h-6 w-6" />

        {/* Item Count Badge */}
        {totalItems > 0 && (
          <span className="absolute -top-1 -right-1 bg-primary-600 text-white text-xs font-bold rounded-full h-5 w-5 flex items-center justify-center min-w-[20px]">
            {totalItems > 99 ? "99+" : totalItems}
          </span>
        )}

        {/* Animation indicator */}
        {animatingProductId && (
          <div className="absolute inset-0 bg-green-500 rounded-lg opacity-20 animate-ping" />
        )}
      </button>

      {/* Dropdown */}
      {isDropdownOpen && (
        <div
          ref={dropdownRef}
          className="absolute right-0 top-full mt-2 w-80 bg-white rounded-lg shadow-lg border border-gray-200 z-50 max-h-96 overflow-hidden"
        >
          {/* Header */}
          <div className="px-4 py-3 border-b border-gray-100 bg-gray-50">
            <div className="flex items-center justify-between">
              <h3 className="font-semibold text-gray-900">Shopping Lists</h3>
              <button
                onClick={() => setIsDropdownOpen(false)}
                className="text-gray-400 hover:text-gray-600 p-1 rounded"
              >
                <X className="h-4 w-4" />
              </button>
            </div>
            {defaultList && (
              <p className="text-sm text-gray-600 mt-1">
                Default: {defaultList.name}
              </p>
            )}
          </div>

          {/* Content */}
          <div className="max-h-80 overflow-y-auto">
            {isLoading ? (
              <div className="p-4 text-center text-gray-500">
                <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary-600 mx-auto mb-2"></div>
                Loading shopping lists...
              </div>
            ) : shoppingLists.length === 0 ? (
              <div className="p-4 text-center text-gray-500">
                <ShoppingCart className="h-8 w-8 mx-auto mb-2 text-gray-300" />
                <p className="text-sm">No shopping lists yet</p>
                <p className="text-xs text-gray-400 mt-1">
                  Add products from search to create your first list
                </p>
              </div>
            ) : (
              <div className="divide-y divide-gray-100">
                {shoppingLists.map((list) => (
                  <div key={list.id} className="p-4">
                    <div className="flex items-center justify-between mb-2">
                      <div className="flex items-center">
                        <h4 className="font-medium text-gray-900">
                          {list.name}
                        </h4>
                        {list.is_default && (
                          <span className="ml-2 px-2 py-1 bg-primary-100 text-primary-700 text-xs rounded-full">
                            Default
                          </span>
                        )}
                      </div>
                      <span className="text-sm text-gray-500">
                        {list.summary?.total_items || 0} items
                      </span>
                    </div>

                    {list.description && (
                      <p className="text-sm text-gray-600 mb-2">
                        {list.description}
                      </p>
                    )}

                    {/* Summary Info */}
                    {list.summary && list.summary.total_items > 0 && (
                      <div className="text-xs text-gray-500 mt-2">
                        {list.summary.total_quantity} total quantity
                      </div>
                    )}

                    {/* Action Buttons */}
                    <div className="flex items-center justify-between mt-3 pt-2 border-t border-gray-100">
                      <button
                        onClick={() => {
                          // Navigate to shopping list detail page
                          window.location.href = `/shopping-lists/${list.id}`;
                        }}
                        className="text-sm text-primary-600 hover:text-primary-700 font-medium"
                      >
                        View Details
                      </button>

                      <div className="flex items-center space-x-2">
                        <span className="text-xs text-gray-400">
                          Updated{" "}
                          {new Date(list.updated_at).toLocaleDateString()}
                        </span>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>

          {/* Footer */}
          <div className="px-4 py-3 border-t border-gray-100 bg-gray-50">
            <button
              onClick={() => {
                // Navigate to shopping lists management page
                window.location.href = "/shopping-lists";
                setIsDropdownOpen(false);
              }}
              className="w-full text-sm text-primary-600 hover:text-primary-700 font-medium text-center"
            >
              Manage All Lists
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

#!/usr/bin/env python3
"""
Data Validation & Cleanup Script
Validates imported product data integrity and cleans up any inconsistencies
"""

import asyncio
import sys
import time
from pathlib import Path
from typing import Dict, List, Any, Tuple

# Add the app directory to Python path
sys.path.insert(0, str(Path(__file__).parent / "app"))

from app.database import DatabaseManager


class DataValidator:
    """Handles data validation and cleanup operations."""
    
    def __init__(self):
        self.db = None
        self.validation_results = {}
        self.cleanup_results = {}
        
    async def initialize(self):
        """Initialize database connection."""
        self.db = DatabaseManager()
        await self.db.initialize()
        print("✅ Database connection initialized")
    
    async def close(self):
        """Close database connection."""
        if self.db:
            await self.db.close()
            print("✅ Database connection closed")
    
    async def validate_data_integrity(self):
        """Validate overall data integrity."""
        print("🔍 Validating data integrity...")
        
        async with self.db.get_connection() as conn:
            # Check total record count
            total_products = await conn.fetchval("SELECT COUNT(*) FROM products")
            print(f"  📊 Total products: {total_products:,}")
            
            # Check for duplicate records
            duplicates = await conn.fetch("""
                SELECT name, mfr, seller, COUNT(*) as count
                FROM products 
                WHERE mfr IS NOT NULL
                GROUP BY name, mfr, seller
                HAVING COUNT(*) > 1
                ORDER BY count DESC
                LIMIT 10
            """)
            
            duplicate_count = len(duplicates)
            print(f"  🔍 Potential duplicates (same name+MFR+seller): {duplicate_count}")
            if duplicates:
                print("    Top duplicates:")
                for dup in duplicates[:5]:
                    print(f"      - {dup['name'][:50]}... | {dup['mfr']} | {dup['seller']} ({dup['count']} copies)")
            
            # Check for missing critical fields
            missing_name = await conn.fetchval("SELECT COUNT(*) FROM products WHERE name IS NULL OR name = ''")
            missing_url = await conn.fetchval("SELECT COUNT(*) FROM products WHERE url IS NULL OR url = ''")
            missing_seller = await conn.fetchval("SELECT COUNT(*) FROM products WHERE seller IS NULL OR seller = ''")
            null_mfr = await conn.fetchval("SELECT COUNT(*) FROM products WHERE mfr IS NULL")
            
            print(f"  ⚠️ Missing name: {missing_name}")
            print(f"  ⚠️ Missing URL: {missing_url}")
            print(f"  ⚠️ Missing seller: {missing_seller}")
            print(f"  ℹ️ NULL MFR codes: {null_mfr:,} (expected)")
            
            # Check data quality metrics
            avg_name_length = await conn.fetchval("SELECT AVG(LENGTH(name)) FROM products WHERE name IS NOT NULL")
            max_name_length = await conn.fetchval("SELECT MAX(LENGTH(name)) FROM products")
            
            print(f"  📏 Average name length: {avg_name_length:.1f} characters")
            print(f"  📏 Maximum name length: {max_name_length} characters")
            
            # Store validation results
            self.validation_results = {
                'total_products': total_products,
                'duplicates': duplicate_count,
                'missing_name': missing_name,
                'missing_url': missing_url,
                'missing_seller': missing_seller,
                'null_mfr': null_mfr,
                'avg_name_length': avg_name_length,
                'max_name_length': max_name_length
            }
    
    async def validate_search_text_quality(self):
        """Validate search text field quality."""
        print("\n🔍 Validating search text quality...")
        
        async with self.db.get_connection() as conn:
            # Check search_text field completeness
            missing_search_text = await conn.fetchval("""
                SELECT COUNT(*) FROM products 
                WHERE search_text IS NULL OR search_text = ''
            """)
            
            # Check average search text length
            avg_search_length = await conn.fetchval("""
                SELECT AVG(LENGTH(search_text)) FROM products 
                WHERE search_text IS NOT NULL
            """)
            
            # Check for search text that's too short (might indicate poor quality)
            short_search_text = await conn.fetchval("""
                SELECT COUNT(*) FROM products 
                WHERE LENGTH(search_text) < 20
            """)
            
            print(f"  📝 Missing search text: {missing_search_text}")
            print(f"  📏 Average search text length: {avg_search_length:.1f} characters")
            print(f"  ⚠️ Very short search text (<20 chars): {short_search_text}")
            
            # Sample some search text for manual inspection
            samples = await conn.fetch("""
                SELECT name, search_text 
                FROM products 
                WHERE search_text IS NOT NULL 
                ORDER BY RANDOM() 
                LIMIT 3
            """)
            
            print("  📋 Sample search text:")
            for sample in samples:
                search_preview = sample['search_text'][:100] + "..." if len(sample['search_text']) > 100 else sample['search_text']
                print(f"    - {sample['name'][:30]}...: {search_preview}")
    
    async def validate_categories_and_brands(self):
        """Validate categories and brands data."""
        print("\n🔍 Validating categories and brands...")
        
        async with self.db.get_connection() as conn:
            # Category statistics
            total_categories = await conn.fetchval("""
                SELECT COUNT(DISTINCT maincat) FROM products WHERE maincat IS NOT NULL
            """)
            
            null_categories = await conn.fetchval("""
                SELECT COUNT(*) FROM products WHERE maincat IS NULL
            """)
            
            # Brand statistics
            total_brands = await conn.fetchval("""
                SELECT COUNT(DISTINCT brand) FROM products WHERE brand IS NOT NULL
            """)
            
            null_brands = await conn.fetchval("""
                SELECT COUNT(*) FROM products WHERE brand IS NULL
            """)
            
            # Seller statistics
            total_sellers = await conn.fetchval("""
                SELECT COUNT(DISTINCT seller) FROM products WHERE seller IS NOT NULL
            """)
            
            print(f"  🏷️ Unique categories: {total_categories}")
            print(f"  🏷️ Products without category: {null_categories:,}")
            print(f"  🏢 Unique brands: {total_brands:,}")
            print(f"  🏢 Products without brand: {null_brands:,}")
            print(f"  🏪 Unique sellers: {total_sellers}")
            
            # Top categories by product count
            top_categories = await conn.fetch("""
                SELECT maincat, COUNT(*) as count
                FROM products 
                WHERE maincat IS NOT NULL
                GROUP BY maincat
                ORDER BY count DESC
                LIMIT 5
            """)
            
            print("  📊 Top categories:")
            for cat in top_categories:
                print(f"    - {cat['maincat']}: {cat['count']:,} products")
    
    async def validate_price_data(self):
        """Validate price data quality."""
        print("\n🔍 Validating price data...")
        
        async with self.db.get_connection() as conn:
            # Price field statistics
            total_with_price = await conn.fetchval("""
                SELECT COUNT(*) FROM products WHERE price IS NOT NULL AND price != ''
            """)
            
            null_prices = await conn.fetchval("""
                SELECT COUNT(*) FROM products WHERE price IS NULL OR price = ''
            """)
            
            # Try to identify numeric prices
            numeric_prices = await conn.fetchval("""
                SELECT COUNT(*) FROM products 
                WHERE price ~ '^[0-9]+\.?[0-9]*$'
            """)
            
            print(f"  💰 Products with price data: {total_with_price:,}")
            print(f"  💰 Products without price: {null_prices:,}")
            print(f"  💰 Numeric price format: {numeric_prices:,}")
            
            # Sample price formats
            price_samples = await conn.fetch("""
                SELECT price
                FROM products
                WHERE price IS NOT NULL AND price != ''
                ORDER BY RANDOM()
                LIMIT 10
            """)
            
            print("  📋 Sample price formats:")
            for sample in price_samples:
                print(f"    - '{sample['price']}'")
    
    async def cleanup_data_inconsistencies(self):
        """Clean up data inconsistencies."""
        print("\n🧹 Cleaning up data inconsistencies...")
        
        async with self.db.get_connection() as conn:
            cleanup_count = 0
            
            # Clean up empty string fields (convert to NULL)
            empty_mfr = await conn.execute("""
                UPDATE products 
                SET mfr = NULL 
                WHERE mfr = '' OR mfr = ' '
            """)
            cleanup_count += int(empty_mfr.split()[-1]) if 'UPDATE' in empty_mfr else 0
            
            empty_brand = await conn.execute("""
                UPDATE products 
                SET brand = NULL 
                WHERE brand = '' OR brand = ' '
            """)
            cleanup_count += int(empty_brand.split()[-1]) if 'UPDATE' in empty_brand else 0
            
            empty_category = await conn.execute("""
                UPDATE products 
                SET maincat = NULL 
                WHERE maincat = '' OR maincat = ' '
            """)
            cleanup_count += int(empty_category.split()[-1]) if 'UPDATE' in empty_category else 0
            
            # Trim whitespace from text fields
            trimmed = await conn.execute("""
                UPDATE products 
                SET 
                    name = TRIM(name),
                    mfr = TRIM(mfr),
                    brand = TRIM(brand),
                    seller = TRIM(seller),
                    maincat = TRIM(maincat),
                    category = TRIM(category),
                    manufactured_by = TRIM(manufactured_by)
                WHERE 
                    name != TRIM(name) OR
                    mfr != TRIM(mfr) OR
                    brand != TRIM(brand) OR
                    seller != TRIM(seller) OR
                    maincat != TRIM(maincat) OR
                    category != TRIM(category) OR
                    manufactured_by != TRIM(manufactured_by)
            """)
            cleanup_count += int(trimmed.split()[-1]) if 'UPDATE' in trimmed else 0
            
            # Fix search_text for products that might have empty search_text
            fixed_search_text = await conn.execute("""
                UPDATE products 
                SET search_text = COALESCE(name, '') || ' | ' || 
                                 COALESCE(brand, '') || ' | ' || 
                                 COALESCE(maincat, '') || ' | ' || 
                                 COALESCE(seller, '')
                WHERE search_text IS NULL OR search_text = '' OR LENGTH(search_text) < 10
            """)
            cleanup_count += int(fixed_search_text.split()[-1]) if 'UPDATE' in fixed_search_text else 0
            
            print(f"  ✅ Total records cleaned up: {cleanup_count}")
            
            self.cleanup_results = {
                'total_cleaned': cleanup_count,
                'empty_fields_fixed': cleanup_count,
                'whitespace_trimmed': True,
                'search_text_regenerated': True
            }
    
    async def validate_indexes_and_performance(self):
        """Validate database indexes and performance."""
        print("\n🔍 Validating indexes and performance...")
        
        async with self.db.get_connection() as conn:
            # Check index usage statistics
            index_stats = await conn.fetch("""
                SELECT
                    indexrelname as indexname,
                    idx_scan,
                    idx_tup_read,
                    idx_tup_fetch
                FROM pg_stat_user_indexes
                WHERE relname = 'products'
                ORDER BY idx_scan DESC
            """)
            
            print("  📊 Index usage statistics:")
            for stat in index_stats:
                print(f"    - {stat['indexname']}: {stat['idx_scan']} scans, {stat['idx_tup_read']} tuples read")
            
            # Test search performance
            start_time = time.time()
            search_result = await conn.fetchval("""
                SELECT COUNT(*) FROM products 
                WHERE to_tsvector('english', search_text) @@ to_tsquery('english', 'dental')
            """)
            search_time = (time.time() - start_time) * 1000
            
            print(f"  ⚡ Search performance test: {search_result:,} results in {search_time:.2f}ms")
            
            # Check table size and index sizes
            table_size = await conn.fetchval("""
                SELECT pg_size_pretty(pg_total_relation_size('products'))
            """)
            
            print(f"  💾 Products table size: {table_size}")
    
    async def generate_validation_report(self):
        """Generate comprehensive validation report."""
        print("\n📊 Generating validation report...")
        
        report = {
            'timestamp': time.strftime('%Y-%m-%d %H:%M:%S'),
            'validation_results': self.validation_results,
            'cleanup_results': self.cleanup_results,
            'recommendations': []
        }
        
        # Generate recommendations based on validation results
        if self.validation_results.get('missing_name', 0) > 0:
            report['recommendations'].append("Consider removing products with missing names")
        
        if self.validation_results.get('duplicates', 0) > 10:
            report['recommendations'].append("Review and potentially merge duplicate products")
        
        if self.validation_results.get('null_mfr', 0) > 0:
            report['recommendations'].append(f"Note: {self.validation_results['null_mfr']:,} products have NULL MFR codes (expected)")
        
        # Save report to file
        import json
        with open('data_validation_report.json', 'w') as f:
            json.dump(report, f, indent=2, default=str)
        
        print("  ✅ Validation report saved to data_validation_report.json")
        
        return report


async def main():
    """Run comprehensive data validation and cleanup."""
    print("🚀 ProfiDent Data Validation & Cleanup")
    print("=" * 50)
    
    validator = DataValidator()
    
    try:
        await validator.initialize()
        
        # Run validation steps
        await validator.validate_data_integrity()
        await validator.validate_search_text_quality()
        await validator.validate_categories_and_brands()
        await validator.validate_price_data()
        await validator.cleanup_data_inconsistencies()
        await validator.validate_indexes_and_performance()
        
        # Generate final report
        report = await validator.generate_validation_report()
        
        print("\n" + "=" * 50)
        print("📊 VALIDATION SUMMARY")
        print("=" * 50)
        print(f"Total products validated: {report['validation_results']['total_products']:,}")
        print(f"Data inconsistencies cleaned: {report['cleanup_results']['total_cleaned']}")
        print(f"Recommendations: {len(report['recommendations'])}")
        
        for rec in report['recommendations']:
            print(f"  - {rec}")
        
        print("\n✅ Data validation and cleanup completed successfully!")
        print("🎯 Database is ready for production use")
        
        return True
        
    except Exception as e:
        print(f"❌ Validation failed: {e}")
        return False
        
    finally:
        await validator.close()


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)

#!/usr/bin/env python3
"""
Test script for fast full-text search API
Verifies search functionality and performance
"""

import asyncio
import sys
import time
from pathlib import Path

# Add the app directory to Python path
sys.path.insert(0, str(Path(__file__).parent / "app"))

from app.database import DatabaseManager
from app.services.search_service import SearchService
from app.schemas.product import ProductSearchRequest
from app.models.product import ProductRepository


async def test_search_service_import():
    """Test search service imports."""
    print("🔍 Testing search service imports...")
    
    try:
        from app.services.search_service import SearchService
        from app.models.product import ProductRepository
        from app.schemas.product import ProductSearchRequest, ProductSearchResponse
        
        print("✅ Search service imports successful")
        return True
        
    except Exception as e:
        print(f"❌ Search service import failed: {e}")
        return False


async def test_database_search_functions():
    """Test database search functions."""
    print("\n🔍 Testing database search functions...")
    
    try:
        db = DatabaseManager()
        await db.initialize()
        
        async with db.get_connection() as conn:
            # Test if we have any products
            product_count = await conn.fetchval("SELECT COUNT(*) FROM products")
            print(f"✅ Database connected, products available: {product_count}")
            
            if product_count == 0:
                print("⚠️ No products in database - search tests will be limited")
                return True
            
            # Test full-text search function
            try:
                products, total = await ProductRepository.search_products_fulltext(
                    conn, "test", limit=5, offset=0
                )
                print(f"✅ Full-text search function working: {total} results for 'test'")
            except Exception as e:
                print(f"❌ Full-text search function failed: {e}")
                return False
            
            # Test similarity search function
            try:
                products, total = await ProductRepository.search_products_similarity(
                    conn, "test", limit=5, offset=0
                )
                print(f"✅ Similarity search function working: {total} results for 'test'")
            except Exception as e:
                print(f"❌ Similarity search function failed: {e}")
                return False
            
            # Test suggestions function
            try:
                suggestions = await ProductRepository.get_search_suggestions(conn, "te", limit=5)
                print(f"✅ Suggestions function working: {len(suggestions)} suggestions for 'te'")
            except Exception as e:
                print(f"❌ Suggestions function failed: {e}")
                return False
        
        await db.close()
        return True
        
    except Exception as e:
        print(f"❌ Database search functions test failed: {e}")
        return False


async def test_search_service_methods():
    """Test search service methods."""
    print("\n🔍 Testing search service methods...")
    
    try:
        db = DatabaseManager()
        await db.initialize()
        
        # Mock Redis client for testing
        class MockRedis:
            async def get(self, key):
                return None
            async def setex(self, key, ttl, value):
                return True
            async def keys(self, pattern):
                return []
            async def delete(self, *keys):
                return 0
            async def ping(self):
                return True
        
        redis_client = MockRedis()
        
        async with db.get_connection() as conn:
            # Test search service
            search_request = ProductSearchRequest(
                q="test",
                limit=5,
                offset=0,
                search_type="fulltext"
            )
            
            start_time = time.time()
            result = await SearchService.search_products(conn, redis_client, search_request)
            search_time = (time.time() - start_time) * 1000
            
            print(f"✅ Search service working: {result.total} results in {search_time:.2f}ms")
            print(f"✅ Search response format: query='{result.query}', type='{result.search_type}'")
            
            # Test suggestions service
            suggestions_result = await SearchService.get_search_suggestions(
                conn, redis_client, "te", 5
            )
            print(f"✅ Suggestions service working: {len(suggestions_result.suggestions)} suggestions")
            
            # Test popular searches service
            popular_result = await SearchService.get_popular_searches(
                conn, redis_client, 5
            )
            print(f"✅ Popular searches service working: {len(popular_result.popular_searches)} popular terms")
            
            # Test search stats
            stats = await SearchService.get_search_stats(conn, redis_client)
            print(f"✅ Search stats working: {stats.get('total_products', 0)} total products")
        
        await db.close()
        return True
        
    except Exception as e:
        print(f"❌ Search service methods test failed: {e}")
        return False


async def test_search_api_endpoints():
    """Test search API endpoints."""
    print("\n🔍 Testing search API endpoints...")
    
    try:
        from fastapi.testclient import TestClient
        from app.main import app
        
        with TestClient(app) as client:
            # Test search endpoint structure
            response = client.get("/api/v1/search/?q=test")
            if response.status_code in [200, 401]:  # 401 is expected without auth
                print("✅ Search endpoint structure working")
            else:
                print(f"❌ Search endpoint failed: {response.status_code}")
                return False
            
            # Test suggestions endpoint
            response = client.get("/api/v1/search/suggestions?q=te")
            if response.status_code in [200, 401]:
                print("✅ Suggestions endpoint structure working")
            else:
                print(f"❌ Suggestions endpoint failed: {response.status_code}")
                return False
            
            # Test popular searches endpoint
            response = client.get("/api/v1/search/popular")
            if response.status_code in [200, 401]:
                print("✅ Popular searches endpoint structure working")
            else:
                print(f"❌ Popular searches endpoint failed: {response.status_code}")
                return False
            
            # Test search health endpoint
            response = client.get("/api/v1/search/health")
            if response.status_code in [200, 503]:  # 503 if database not ready
                print("✅ Search health endpoint working")
            else:
                print(f"❌ Search health endpoint failed: {response.status_code}")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ Search API endpoints test failed: {e}")
        return False


async def test_search_performance():
    """Test search performance."""
    print("\n🔍 Testing search performance...")
    
    try:
        db = DatabaseManager()
        await db.initialize()
        
        class MockRedis:
            async def get(self, key):
                return None
            async def setex(self, key, ttl, value):
                return True
            async def keys(self, pattern):
                return []
            async def ping(self):
                return True
        
        redis_client = MockRedis()
        
        async with db.get_connection() as conn:
            test_queries = ["test", "dental", "implant", "crown", "filling"]
            
            for query in test_queries[:3]:  # Test first 3 queries
                # Test fulltext search performance
                start_time = time.time()
                search_request = ProductSearchRequest(
                    q=query,
                    limit=10,
                    offset=0,
                    search_type="fulltext"
                )
                result = await SearchService.search_products(conn, redis_client, search_request)
                fulltext_time = (time.time() - start_time) * 1000
                
                # Test similarity search performance
                start_time = time.time()
                search_request.search_type = "similarity"
                result = await SearchService.search_products(conn, redis_client, search_request)
                similarity_time = (time.time() - start_time) * 1000
                
                print(f"✅ Query '{query}': fulltext={fulltext_time:.1f}ms, similarity={similarity_time:.1f}ms")
        
        await db.close()
        return True
        
    except Exception as e:
        print(f"❌ Search performance test failed: {e}")
        return False


async def cleanup():
    """Clean up resources."""
    print("\n🧹 Cleaning up...")
    
    try:
        # No specific cleanup needed for search tests
        print("✅ Cleanup completed")
    except Exception as e:
        print(f"⚠️ Cleanup warning: {e}")


async def main():
    """Run all search API tests."""
    print("🚀 Starting ProfiDent Fast Full-Text Search API Tests\n")
    print("Testing PostgreSQL full-text search with instant performance")
    print("=" * 60)
    
    tests = [
        ("Search Service Import", test_search_service_import),
        ("Database Search Functions", test_database_search_functions),
        ("Search Service Methods", test_search_service_methods),
        ("Search API Endpoints", test_search_api_endpoints),
        ("Search Performance", test_search_performance),
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = await test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} failed with exception: {e}")
            results.append((test_name, False))
    
    # Cleanup
    await cleanup()
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 TEST SUMMARY")
    print("=" * 60)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{status} - {test_name}")
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All search API tests passed!")
        print("✅ Fast full-text search API is working correctly")
        print("⚡ Ready for instant search performance with PostgreSQL")
        return True
    else:
        print("❌ Some tests failed. Check search API configuration.")
        return False


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)

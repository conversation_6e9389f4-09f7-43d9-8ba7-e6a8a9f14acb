#!/usr/bin/env python3
"""
Test Permission Management API Endpoints
"""

import asyncio
import asyncpg
import sys
from pathlib import Path
import httpx

# Add the app directory to Python path
sys.path.append(str(Path(__file__).parent))

from app.config import settings
from app.utils.security import create_token_response

async def test_permission_management_api():
    """Test permission management API endpoints."""
    
    print("🔐 Testing Permission Management API Endpoints")
    print("=" * 50)
    
    try:
        # Connect to database
        db_url = settings.DATABASE_URL.replace("postgresql+asyncpg://", "postgresql://")
        conn = await asyncpg.connect(db_url)
        print("✅ Database connection successful")
        
        # Test 1: Setup authentication
        print("\n👤 Test 1: Setting up authentication...")
        
        # Get a test user (use any existing user and make them superuser for testing)
        user_record = await conn.fetchrow("""
            SELECT u.*, array_agg(DISTINCT r.name) as roles, u.is_superuser
            FROM users u
            LEFT JOIN user_roles ur ON u.id = ur.user_id AND ur.is_active = TRUE
            LEFT JOIN roles r ON ur.role_id = r.id AND r.is_active = TRUE
            GROUP BY u.id, u.email, u.hashed_password, u.full_name, u.is_active, u.is_superuser, u.created_at, u.updated_at
            LIMIT 1
        """)
        
        if not user_record:
            print("❌ No users found")
            return False

        print(f"✅ Test user: {user_record['email']}")

        # Temporarily make user superuser for testing if not already
        if not user_record['is_superuser']:
            await conn.execute("UPDATE users SET is_superuser = TRUE WHERE id = $1", user_record['id'])
            print("✅ Temporarily granted superuser privileges for testing")

        # Create authentication token
        token_data = create_token_response(
            user_id=user_record['id'],
            email=user_record['email'],
            roles=user_record['roles'] or [],
            permissions=[],  # Will be loaded by the system
            is_superuser=True  # Use superuser for testing
        )
        
        auth_token = token_data['access_token']
        print("✅ Authentication token created")
        print(f"✅ User is_superuser: True (for testing)")
        
        # Test 2: Test permission listing
        print("\n📋 Test 2: Testing permission listing...")
        
        async with httpx.AsyncClient() as client:
            response = await client.get(
                "http://localhost:8000/api/v1/permissions/",
                headers={"Authorization": f"Bearer {auth_token}"}
            )
            
            if response.status_code == 200:
                data = response.json()
                print("✅ Permission listing successful")
                print(f"✅ Total permissions: {data['total']}")
                print(f"✅ Permissions returned: {len(data['permissions'])}")
                if data['permissions']:
                    print(f"✅ First permission: {data['permissions'][0]['name']}")
            else:
                print(f"❌ Permission listing failed: {response.status_code}")
                print(f"Response: {response.text}")
                return False
        
        # Test 3: Test permission details
        print("\n🔍 Test 3: Testing permission details...")
        
        # Get first permission ID
        first_permission_id = data['permissions'][0]['id']
        
        async with httpx.AsyncClient() as client:
            response = await client.get(
                f"http://localhost:8000/api/v1/permissions/{first_permission_id}",
                headers={"Authorization": f"Bearer {auth_token}"}
            )
            
            if response.status_code == 200:
                perm_data = response.json()
                print("✅ Permission details retrieved successfully")
                print(f"✅ Permission: {perm_data['name']}")
                print(f"✅ Resource: {perm_data['resource']}")
                print(f"✅ Action: {perm_data['action']}")
            else:
                print(f"❌ Permission details failed: {response.status_code}")
                print(f"Response: {response.text}")
        
        # Test 4: Test permission summary
        print("\n📊 Test 4: Testing permission summary...")
        
        async with httpx.AsyncClient() as client:
            response = await client.get(
                "http://localhost:8000/api/v1/permissions/summary",
                headers={"Authorization": f"Bearer {auth_token}"}
            )
            
            if response.status_code == 200:
                summary_data = response.json()
                print("✅ Permission summary retrieved successfully")
                print(f"✅ Total permissions: {summary_data['total_permissions']}")
                print(f"✅ System permissions: {summary_data['system_permissions']}")
                print(f"✅ Custom permissions: {summary_data['custom_permissions']}")
                print(f"✅ Total resources: {summary_data['total_resources']}")
                print(f"✅ Total actions: {summary_data['total_actions']}")
            else:
                print(f"❌ Permission summary failed: {response.status_code}")
                print(f"Response: {response.text}")
        
        # Test 5: Test permission creation
        print("\n➕ Test 5: Testing permission creation...")
        
        import time
        timestamp = int(time.time())
        test_permission = {
            "name": f"test.custom.{timestamp}",
            "resource": "system",  # Use system resource
            "action": "search",   # Use search action which should be allowed
            "description": "Test custom permission for API testing"
        }
        
        async with httpx.AsyncClient() as client:
            response = await client.post(
                "http://localhost:8000/api/v1/permissions/",
                headers={"Authorization": f"Bearer {auth_token}"},
                json=test_permission
            )
            
            if response.status_code == 201:
                created_perm = response.json()
                print("✅ Permission creation successful")
                print(f"✅ Created permission: {created_perm['name']}")
                test_permission_id = created_perm['id']
            else:
                print(f"❌ Permission creation failed: {response.status_code}")
                print(f"Response: {response.text}")
                test_permission_id = None
        
        # Test 6: Test role-permission assignment
        print("\n🔗 Test 6: Testing role-permission assignment...")
        
        if test_permission_id:
            # Get superadmin role ID
            superadmin_role = await conn.fetchrow("SELECT id FROM roles WHERE name = 'superadmin'")
            
            if superadmin_role:
                assignment_data = {
                    "role_id": str(superadmin_role['id']),
                    "permission_id": test_permission_id
                }
                
                async with httpx.AsyncClient() as client:
                    response = await client.post(
                        "http://localhost:8000/api/v1/permissions/assign-to-role",
                        headers={"Authorization": f"Bearer {auth_token}"},
                        json=assignment_data
                    )
                    
                    if response.status_code == 201:
                        assignment = response.json()
                        print("✅ Permission assignment successful")
                        print(f"✅ Assigned to role: {assignment['role_id']}")
                    else:
                        print(f"❌ Permission assignment failed: {response.status_code}")
                        print(f"Response: {response.text}")
        
        # Test 7: Test permission filtering
        print("\n🔍 Test 7: Testing permission filtering...")
        
        async with httpx.AsyncClient() as client:
            # Test resource filter
            response = await client.get(
                "http://localhost:8000/api/v1/permissions/?resource=roles",
                headers={"Authorization": f"Bearer {auth_token}"}
            )
            
            if response.status_code == 200:
                filtered_data = response.json()
                print(f"✅ Resource filter works: {len(filtered_data['permissions'])} role permissions")
            else:
                print(f"❌ Resource filter failed: {response.status_code}")
        
        # Test 8: Verify database state
        print("\n🗄️  Test 8: Verifying database state...")
        
        total_perms = await conn.fetchval("SELECT COUNT(*) FROM permissions")
        system_perms = await conn.fetchval("SELECT COUNT(*) FROM permissions WHERE is_system_permission = TRUE")
        custom_perms = await conn.fetchval("SELECT COUNT(*) FROM permissions WHERE is_system_permission = FALSE")
        
        print(f"✅ Total permissions in database: {total_perms}")
        print(f"✅ System permissions: {system_perms}")
        print(f"✅ Custom permissions: {custom_perms}")
        
        # Cleanup: Delete test permission if created
        if test_permission_id:
            print("\n🧹 Cleaning up test permission...")
            async with httpx.AsyncClient() as client:
                response = await client.delete(
                    f"http://localhost:8000/api/v1/permissions/{test_permission_id}",
                    headers={"Authorization": f"Bearer {auth_token}"}
                )
                
                if response.status_code == 204:
                    print("✅ Test permission cleaned up successfully")
                else:
                    print(f"⚠️  Test permission cleanup failed: {response.status_code}")
        
        print("\n🎉 Permission Management API Test Completed!")
        print("=" * 50)
        print("✅ Permission listing endpoint works")
        print("✅ Permission details endpoint works")
        print("✅ Permission summary endpoint works")
        print("✅ Permission creation endpoint works")
        print("✅ Role-permission assignment works")
        print("✅ Permission filtering works")
        print("✅ Database state is consistent")
        
        return True
        
    except Exception as e:
        print(f"\n❌ Failed to test permission management API: {e}")
        import traceback
        traceback.print_exc()
        return False
        
    finally:
        if 'conn' in locals():
            await conn.close()
            print("\n📡 Database connection closed")

if __name__ == "__main__":
    success = asyncio.run(test_permission_management_api())
    sys.exit(0 if success else 1)

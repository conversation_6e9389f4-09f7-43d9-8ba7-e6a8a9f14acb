import React from 'react';
import { ExternalLink, Package, Building2, Tag, DollarSign } from 'lucide-react';
import { Product } from '../../types';

interface ProductDetailsCardProps {
  product: Product;
  className?: string;
}

const ProductDetailsCard: React.FC<ProductDetailsCardProps> = ({
  product,
  className = ""
}) => {
  // Parse price information
  const parsePrice = (priceStr: string): { price: number; unit: string } | null => {
    try {
      // Handle formats like "1 @ 240.69ea." or "$45.99"
      const match = priceStr.match(/(\d+\.?\d*)/);
      if (match) {
        return {
          price: parseFloat(match[1]),
          unit: priceStr.includes('ea') ? 'each' : 'unit'
        };
      }
    } catch (error) {
      console.warn('Error parsing price:', priceStr, error);
    }
    return null;
  };

  const priceInfo = parsePrice(product.price || '');

  return (
    <div className={`bg-white border border-gray-200 rounded-lg shadow-sm hover:shadow-md transition-shadow ${className}`}>
      <div className="p-6">
        {/* Header */}
        <div className="flex items-start justify-between mb-4">
          <div className="flex-1">
            <h3 className="text-lg font-semibold text-gray-900 mb-2 line-clamp-2">
              {product.name}
            </h3>
            <div className="flex items-center space-x-4 text-sm text-gray-600">
              <div className="flex items-center">
                <Package className="h-4 w-4 mr-1" />
                <span>MFR: {product.mfr}</span>
              </div>
              {product.category && (
                <div className="flex items-center">
                  <Tag className="h-4 w-4 mr-1" />
                  <span>{product.category}</span>
                </div>
              )}
            </div>
          </div>
          
          {/* Price */}
          {priceInfo && (
            <div className="text-right">
              <div className="text-2xl font-bold text-primary-600">
                ${priceInfo.price.toFixed(2)}
              </div>
              <div className="text-sm text-gray-500">
                per {priceInfo.unit}
              </div>
            </div>
          )}
        </div>

        {/* Product Details */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
          {/* Manufacturer */}
          {product.manufactured_by && (
            <div className="flex items-center">
              <Building2 className="h-4 w-4 text-gray-400 mr-2" />
              <div>
                <span className="text-sm text-gray-500">Manufacturer:</span>
                <span className="ml-2 text-sm font-medium text-gray-900">
                  {product.manufactured_by}
                </span>
              </div>
            </div>
          )}

          {/* Seller */}
          {product.seller && (
            <div className="flex items-center">
              <Building2 className="h-4 w-4 text-gray-400 mr-2" />
              <div>
                <span className="text-sm text-gray-500">Seller:</span>
                <span className="ml-2 text-sm font-medium text-gray-900">
                  {product.seller}
                </span>
              </div>
            </div>
          )}

          {/* Price Details */}
          {product.price && !priceInfo && (
            <div className="flex items-center">
              <DollarSign className="h-4 w-4 text-gray-400 mr-2" />
              <div>
                <span className="text-sm text-gray-500">Price:</span>
                <span className="ml-2 text-sm font-medium text-gray-900">
                  {product.price}
                </span>
              </div>
            </div>
          )}
        </div>

        {/* Actions */}
        <div className="flex items-center justify-between pt-4 border-t border-gray-100">
          <div className="text-xs text-gray-500">
            Product ID: {product.id}
          </div>
          
          {product.url && (
            <a
              href={product.url}
              target="_blank"
              rel="noopener noreferrer"
              className="inline-flex items-center px-3 py-2 text-sm font-medium text-primary-600 bg-primary-50 rounded-md hover:bg-primary-100 transition-colors"
            >
              <ExternalLink className="h-4 w-4 mr-1" />
              View Details
            </a>
          )}
        </div>
      </div>
    </div>
  );
};

export default ProductDetailsCard;

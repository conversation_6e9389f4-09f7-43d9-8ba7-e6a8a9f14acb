import { describe, it, expect, vi, beforeEach } from "vitest";
import { useAuthStore } from "../authStore";
import apiClient from "../../lib/api";

// Mock the API client
vi.mock("../../lib/api");
const mockApiClient = vi.mocked(apiClient);

// Mock localStorage
const localStorageMock = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn(),
};
Object.defineProperty(window, "localStorage", {
  value: localStorageMock,
});

describe("authStore", () => {
  beforeEach(() => {
    vi.clearAllMocks();
    localStorageMock.getItem.mockReturnValue(null);

    // Reset the store state
    useAuthStore.setState({
      user: null,
      tokens: null,
      isAuthenticated: false,
      isLoading: false,
      error: null,
      userRoles: [],
      userPermissions: [],
      isSuperAdmin: false,
    });
  });

  describe("login", () => {
    it("successfully logs in a user", async () => {
      const mockUser = {
        id: "1",
        email: "<EMAIL>",
        full_name: "Test User",
        is_active: true,
        is_superuser: false,
        created_at: "2024-01-01T00:00:00Z",
        roles: ["user"],
        permissions: ["product:read", "shopping_list:manage"],
      };

      const mockTokens = {
        access_token: "access_token",
        refresh_token: "refresh_token",
        token_type: "bearer",
        expires_in: 3600,
      };

      mockApiClient.login.mockResolvedValue({
        user: mockUser,
        token: mockTokens,
      });

      const { login } = useAuthStore.getState();
      await login({ email: "<EMAIL>", password: "password" });

      const state = useAuthStore.getState();
      expect(state.user).toEqual(mockUser);
      expect(state.tokens).toEqual(mockTokens);
      expect(state.isAuthenticated).toBe(true);
      expect(state.isLoading).toBe(false);
      expect(state.error).toBeNull();
      expect(state.userRoles).toEqual(["user"]);
      expect(state.userPermissions).toEqual([
        "product:read",
        "shopping_list:manage",
      ]);
      expect(state.isSuperAdmin).toBe(false);

      expect(localStorageMock.setItem).toHaveBeenCalledWith(
        "access_token",
        "access_token"
      );
      expect(localStorageMock.setItem).toHaveBeenCalledWith(
        "refresh_token",
        "refresh_token"
      );
    });

    it("handles login failure", async () => {
      const errorMessage = "Invalid credentials";
      mockApiClient.login.mockRejectedValue({
        response: { data: { detail: errorMessage } },
      });

      const { login } = useAuthStore.getState();

      await expect(
        login({ email: "<EMAIL>", password: "wrong" })
      ).rejects.toThrow();

      const state = useAuthStore.getState();
      expect(state.user).toBeNull();
      expect(state.tokens).toBeNull();
      expect(state.isAuthenticated).toBe(false);
      expect(state.isLoading).toBe(false);
      expect(state.error).toBe(errorMessage);
    });
  });

  describe("register", () => {
    it("successfully registers a user", async () => {
      const mockUser = {
        id: "1",
        email: "<EMAIL>",
        full_name: "New User",
        is_active: true,
        created_at: "2024-01-01T00:00:00Z",
      };

      const mockTokens = {
        access_token: "access_token",
        refresh_token: "refresh_token",
        token_type: "bearer",
        expires_in: 3600,
      };

      mockApiClient.register.mockResolvedValue({
        user: mockUser,
        token: mockTokens,
      });

      const { register } = useAuthStore.getState();
      await register({
        email: "<EMAIL>",
        password: "password",
        full_name: "New User",
      });

      const state = useAuthStore.getState();
      expect(state.user).toEqual(mockUser);
      expect(state.tokens).toEqual(mockTokens);
      expect(state.isAuthenticated).toBe(true);
      expect(state.isLoading).toBe(false);
      expect(state.error).toBeNull();
    });
  });

  describe("logout", () => {
    it("clears user state and localStorage", () => {
      // Set initial authenticated state
      useAuthStore.setState({
        user: { id: "1", email: "<EMAIL>" } as any,
        tokens: { access_token: "token" } as any,
        isAuthenticated: true,
      });

      const { logout } = useAuthStore.getState();
      logout();

      const state = useAuthStore.getState();
      expect(state.user).toBeNull();
      expect(state.tokens).toBeNull();
      expect(state.isAuthenticated).toBe(false);
      expect(state.error).toBeNull();

      expect(localStorageMock.removeItem).toHaveBeenCalledWith("access_token");
      expect(localStorageMock.removeItem).toHaveBeenCalledWith("refresh_token");
    });
  });

  describe("initializeAuth", () => {
    it("initializes auth from localStorage tokens", async () => {
      const mockUser = {
        id: "1",
        email: "<EMAIL>",
        full_name: "Test User",
        is_active: true,
        created_at: "2024-01-01T00:00:00Z",
      };

      localStorageMock.getItem.mockImplementation((key) => {
        if (key === "access_token") return "stored_access_token";
        if (key === "refresh_token") return "stored_refresh_token";
        return null;
      });

      mockApiClient.getCurrentUser.mockResolvedValue(mockUser);

      const { initializeAuth } = useAuthStore.getState();
      await initializeAuth();

      const state = useAuthStore.getState();
      expect(state.user).toEqual(mockUser);
      expect(state.isAuthenticated).toBe(true);
      expect(state.isLoading).toBe(false);
    });

    it("clears auth state when tokens are invalid", async () => {
      localStorageMock.getItem.mockImplementation((key) => {
        if (key === "access_token") return "invalid_token";
        if (key === "refresh_token") return "invalid_refresh_token";
        return null;
      });

      mockApiClient.getCurrentUser.mockRejectedValue(new Error("Unauthorized"));

      const { initializeAuth } = useAuthStore.getState();
      await initializeAuth();

      const state = useAuthStore.getState();
      expect(state.user).toBeNull();
      expect(state.isAuthenticated).toBe(false);
      expect(state.isLoading).toBe(false);
    });
  });

  describe("clearError", () => {
    it("clears the error state", () => {
      useAuthStore.setState({ error: "Some error" });

      const { clearError } = useAuthStore.getState();
      clearError();

      const state = useAuthStore.getState();
      expect(state.error).toBeNull();
    });
  });

  describe("role and permission checking", () => {
    beforeEach(() => {
      // Set up a user with roles and permissions
      useAuthStore.setState({
        user: {
          id: "1",
          email: "<EMAIL>",
          full_name: "Test User",
          is_active: true,
          is_superuser: false,
          created_at: "2024-01-01T00:00:00Z",
          roles: ["user", "editor"],
          permissions: ["product:read", "shopping_list:manage"],
        },
        isAuthenticated: true,
        userRoles: ["user", "editor"],
        userPermissions: ["product:read", "shopping_list:manage"],
        isSuperAdmin: false,
      });
    });

    it("hasRole returns true for existing role", () => {
      const { hasRole } = useAuthStore.getState();
      expect(hasRole("user")).toBe(true);
      expect(hasRole("editor")).toBe(true);
      expect(hasRole("admin")).toBe(false);
    });

    it("hasAnyRole returns true if user has any of the roles", () => {
      const { hasAnyRole } = useAuthStore.getState();
      expect(hasAnyRole(["user", "admin"])).toBe(true);
      expect(hasAnyRole(["admin", "moderator"])).toBe(false);
    });

    it("hasPermission returns true for existing permission", () => {
      const { hasPermission } = useAuthStore.getState();
      expect(hasPermission("product:read")).toBe(true);
      expect(hasPermission("shopping_list:manage")).toBe(true);
      expect(hasPermission("user:manage")).toBe(false);
    });

    it("hasAnyPermission returns true if user has any of the permissions", () => {
      const { hasAnyPermission } = useAuthStore.getState();
      expect(hasAnyPermission(["product:read", "user:manage"])).toBe(true);
      expect(hasAnyPermission(["user:manage", "role:manage"])).toBe(false);
    });

    it("superadmin bypasses all role and permission checks", () => {
      useAuthStore.setState({
        isSuperAdmin: true,
        userRoles: [],
        userPermissions: [],
      });

      const { hasRole, hasAnyRole, hasPermission, hasAnyPermission } =
        useAuthStore.getState();
      expect(hasRole("admin")).toBe(true);
      expect(hasAnyRole(["admin", "moderator"])).toBe(true);
      expect(hasPermission("user:manage")).toBe(true);
      expect(hasAnyPermission(["user:manage", "role:manage"])).toBe(true);
    });
  });
});

#!/usr/bin/env python3
"""
Database Optimization Script
Optimizes database performance with indexes, statistics, and configuration
"""

import asyncio
import sys
import time
from pathlib import Path

# Add the app directory to Python path
sys.path.insert(0, str(Path(__file__).parent / "app"))

from app.database import DatabaseManager


class DatabaseOptimizer:
    """Optimizes database performance."""
    
    def __init__(self):
        self.db = None
        
    async def initialize(self):
        """Initialize database connection."""
        self.db = DatabaseManager()
        await self.db.initialize()
        print("✅ Database connection established")
    
    async def close(self):
        """Close database connection."""
        if self.db:
            await self.db.close()
            print("✅ Database connection closed")
    
    async def analyze_current_performance(self):
        """Analyze current database performance."""
        print("📊 Analyzing current database performance...")
        
        async with self.db.get_connection() as conn:
            # Check table sizes
            table_sizes = await conn.fetch("""
                SELECT 
                    schemaname,
                    tablename,
                    pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as size,
                    pg_total_relation_size(schemaname||'.'||tablename) as size_bytes
                FROM pg_tables 
                WHERE schemaname = 'public'
                ORDER BY size_bytes DESC
            """)
            
            print("  📋 Table sizes:")
            for table in table_sizes:
                print(f"    {table['tablename']}: {table['size']}")
            
            # Check index usage
            index_usage = await conn.fetch("""
                SELECT 
                    indexrelname as index_name,
                    relname as table_name,
                    idx_scan as times_used,
                    pg_size_pretty(pg_relation_size(indexrelid)) as index_size
                FROM pg_stat_user_indexes 
                ORDER BY idx_scan DESC
            """)
            
            print("  📋 Index usage (top 10):")
            for idx in index_usage[:10]:
                print(f"    {idx['index_name']} on {idx['table_name']}: {idx['times_used']} scans, {idx['index_size']}")
            
            # Check slow queries (if pg_stat_statements is available)
            try:
                slow_queries = await conn.fetch("""
                    SELECT 
                        query,
                        calls,
                        total_time,
                        mean_time,
                        rows
                    FROM pg_stat_statements 
                    WHERE query LIKE '%products%'
                    ORDER BY mean_time DESC 
                    LIMIT 5
                """)
                
                if slow_queries:
                    print("  📋 Slowest queries:")
                    for query in slow_queries:
                        print(f"    {query['query'][:80]}... | Avg: {query['mean_time']:.2f}ms | Calls: {query['calls']}")
            except:
                print("  ℹ️ pg_stat_statements not available for query analysis")
            
            # Check database statistics
            db_stats = await conn.fetchrow("""
                SELECT 
                    pg_database_size(current_database()) as db_size,
                    (SELECT count(*) FROM products) as product_count,
                    (SELECT count(*) FROM products WHERE embedding IS NOT NULL) as embedded_count
            """)
            
            print(f"  📊 Database statistics:")
            print(f"    Database size: {db_stats['db_size'] / (1024**3):.2f} GB")
            print(f"    Product count: {db_stats['product_count']:,}")
            print(f"    Embedded products: {db_stats['embedded_count']:,}")
    
    async def optimize_indexes(self):
        """Create and optimize database indexes."""
        print("🔧 Optimizing database indexes...")
        
        async with self.db.get_connection() as conn:
            # Create additional performance indexes
            indexes_to_create = [
                {
                    'name': 'idx_products_search_gin',
                    'sql': """
                        CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_products_search_gin 
                        ON products USING gin(to_tsvector('english', search_text))
                    """,
                    'description': 'GIN index for full-text search'
                },
                {
                    'name': 'idx_products_seller_category',
                    'sql': """
                        CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_products_seller_category 
                        ON products (seller, maincat) WHERE seller IS NOT NULL AND maincat IS NOT NULL
                    """,
                    'description': 'Composite index for seller and category filtering'
                },
                {
                    'name': 'idx_products_brand_btree',
                    'sql': """
                        CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_products_brand_btree 
                        ON products (brand) WHERE brand IS NOT NULL
                    """,
                    'description': 'B-tree index for brand filtering'
                },
                {
                    'name': 'idx_products_price_btree',
                    'sql': """
                        CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_products_price_btree 
                        ON products (price) WHERE price IS NOT NULL AND price != ''
                    """,
                    'description': 'B-tree index for price sorting'
                },
                {
                    'name': 'idx_products_updated_at',
                    'sql': """
                        CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_products_updated_at 
                        ON products (updated_at DESC)
                    """,
                    'description': 'Index for recently updated products'
                }
            ]
            
            for index in indexes_to_create:
                try:
                    print(f"  Creating {index['name']}...")
                    await conn.execute(index['sql'])
                    print(f"    ✅ {index['description']}")
                except Exception as e:
                    print(f"    ⚠️ {index['name']}: {e}")
    
    async def update_statistics(self):
        """Update database statistics for query optimization."""
        print("📈 Updating database statistics...")
        
        async with self.db.get_connection() as conn:
            # Analyze tables to update statistics
            tables_to_analyze = ['products', 'users', 'shopping_lists', 'shopping_list_items']
            
            for table in tables_to_analyze:
                try:
                    print(f"  Analyzing {table}...")
                    await conn.execute(f"ANALYZE {table}")
                    print(f"    ✅ Statistics updated for {table}")
                except Exception as e:
                    print(f"    ⚠️ Failed to analyze {table}: {e}")
    
    async def optimize_configuration(self):
        """Optimize PostgreSQL configuration settings."""
        print("⚙️ Checking PostgreSQL configuration...")
        
        async with self.db.get_connection() as conn:
            # Check current configuration
            config_checks = [
                ('shared_buffers', '256MB', 'Memory for shared buffers'),
                ('effective_cache_size', '1GB', 'Estimate of disk cache size'),
                ('work_mem', '4MB', 'Memory for sort operations'),
                ('maintenance_work_mem', '64MB', 'Memory for maintenance operations'),
                ('checkpoint_completion_target', '0.9', 'Checkpoint completion target'),
                ('wal_buffers', '16MB', 'WAL buffer size'),
                ('default_statistics_target', '100', 'Statistics target for query planning')
            ]
            
            print("  📋 Current configuration:")
            for setting, recommended, description in config_checks:
                try:
                    current_value = await conn.fetchval(f"SHOW {setting}")
                    print(f"    {setting}: {current_value} (recommended: {recommended}) - {description}")
                except Exception as e:
                    print(f"    {setting}: Error reading - {e}")
    
    async def vacuum_and_reindex(self):
        """Perform vacuum and reindex operations."""
        print("🧹 Performing database maintenance...")
        
        async with self.db.get_connection() as conn:
            # Vacuum analyze products table
            try:
                print("  Running VACUUM ANALYZE on products table...")
                await conn.execute("VACUUM ANALYZE products")
                print("    ✅ VACUUM ANALYZE completed")
            except Exception as e:
                print(f"    ⚠️ VACUUM ANALYZE failed: {e}")
            
            # Check for bloated indexes
            try:
                bloated_indexes = await conn.fetch("""
                    SELECT 
                        schemaname, 
                        tablename, 
                        indexname,
                        pg_size_pretty(pg_relation_size(indexrelid)) as index_size
                    FROM pg_stat_user_indexes 
                    WHERE idx_scan < 10 AND pg_relation_size(indexrelid) > 1024*1024
                    ORDER BY pg_relation_size(indexrelid) DESC
                """)
                
                if bloated_indexes:
                    print("  📋 Potentially unused large indexes:")
                    for idx in bloated_indexes:
                        print(f"    {idx['indexname']} on {idx['tablename']}: {idx['index_size']} (only {idx.get('idx_scan', 0)} scans)")
            except Exception as e:
                print(f"    ⚠️ Failed to check for bloated indexes: {e}")
    
    async def test_search_performance(self):
        """Test search performance after optimization."""
        print("🧪 Testing search performance...")
        
        test_queries = [
            "dental",
            "implant system",
            "orthodontic brackets",
            "endodontic files",
            "composite filling"
        ]
        
        async with self.db.get_connection() as conn:
            total_time = 0
            total_results = 0
            
            for query in test_queries:
                start_time = time.time()
                
                results = await conn.fetch("""
                    SELECT id, name, seller, maincat
                    FROM products 
                    WHERE to_tsvector('english', search_text) @@ to_tsquery('english', $1)
                    ORDER BY ts_rank(to_tsvector('english', search_text), to_tsquery('english', $1)) DESC
                    LIMIT 20
                """, query)
                
                query_time = (time.time() - start_time) * 1000
                total_time += query_time
                total_results += len(results)
                
                print(f"  🔍 '{query}': {len(results)} results in {query_time:.1f}ms")
            
            avg_time = total_time / len(test_queries)
            print(f"  📊 Average search time: {avg_time:.1f}ms")
            print(f"  📊 Total results found: {total_results}")
    
    async def run_optimization(self):
        """Run complete database optimization."""
        print("🚀 ProfiDent Database Optimization")
        print("=" * 60)
        
        optimization_steps = [
            ("Current Performance Analysis", self.analyze_current_performance),
            ("Index Optimization", self.optimize_indexes),
            ("Statistics Update", self.update_statistics),
            ("Configuration Check", self.optimize_configuration),
            ("Vacuum and Maintenance", self.vacuum_and_reindex),
            ("Performance Testing", self.test_search_performance),
        ]
        
        for step_name, step_func in optimization_steps:
            try:
                print(f"\n📋 {step_name}")
                await step_func()
                print(f"✅ {step_name} completed")
            except Exception as e:
                print(f"❌ {step_name} failed: {e}")
                import traceback
                traceback.print_exc()
        
        print("\n" + "=" * 60)
        print("🎉 DATABASE OPTIMIZATION COMPLETED!")
        print("✅ Your database is now optimized for production performance")
        print("=" * 60)


async def main():
    """Main function to run database optimization."""
    optimizer = DatabaseOptimizer()
    
    try:
        await optimizer.initialize()
        await optimizer.run_optimization()
        return True
    except Exception as e:
        print(f"❌ Database optimization failed: {e}")
        return False
    finally:
        await optimizer.close()


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)

#!/usr/bin/env python3
"""
Comprehensive Playwright Test for Unified Search Interface
Tests the consolidated search layout, filter functionality, and filtered suggestions
"""

import asyncio
import sys
from playwright.async_api import async_playwright
import time

class UnifiedSearchTester:
    def __init__(self):
        self.browser = None
        self.page = None
        self.test_results = []

    async def setup(self):
        """Setup browser and page"""
        playwright = await async_playwright().start()
        self.browser = await playwright.chromium.launch(headless=False)
        self.page = await self.browser.new_page()
        
        # Set viewport
        await self.page.set_viewport_size({"width": 1280, "height": 720})
        
        print("🚀 Browser launched and ready for testing")

    async def log_test(self, test_name, success, details=""):
        """Log test results"""
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{status} {test_name}")
        if details:
            print(f"   {details}")
        
        self.test_results.append({
            "test": test_name,
            "success": success,
            "details": details
        })

    async def take_screenshot(self, name):
        """Take a screenshot for verification"""
        try:
            await self.page.screenshot(path=f"screenshot_{name}.png")
            print(f"📸 Screenshot saved: screenshot_{name}.png")
        except Exception as e:
            print(f"⚠️  Failed to take screenshot {name}: {e}")

    async def test_homepage_loads(self):
        """Test that homepage loads successfully"""
        try:
            await self.page.goto("http://localhost:3000")
            await self.page.wait_for_load_state("networkidle", timeout=15000)
            
            # Take initial screenshot
            await self.take_screenshot("01_homepage_loaded")
            
            # Check if page loaded
            title = await self.page.title()
            success = "ProfiDent" in title or len(title) > 0
            
            await self.log_test("Homepage loads successfully", success, f"Title: {title}")
            return success
        except Exception as e:
            await self.log_test("Homepage loads successfully", False, f"Error: {e}")
            return False

    async def test_unified_search_interface(self):
        """Test that the unified search interface is displayed correctly"""
        try:
            # Check for unified search interface elements
            search_container = await self.page.locator(".bg-white.border.border-gray-200.rounded-xl").first
            is_visible = await search_container.is_visible()
            
            # Check for filters section
            filters_section = await self.page.locator("text=Advanced Filters").first
            filters_visible = await filters_section.is_visible()
            
            # Check for search input
            search_input = await self.page.locator("input[placeholder*='Search dental products']").first
            input_visible = await search_input.is_visible()
            
            success = is_visible and filters_visible and input_visible
            details = f"Container: {is_visible}, Filters: {filters_visible}, Input: {input_visible}"
            
            await self.log_test("Unified search interface displayed", success, details)
            return success
        except Exception as e:
            await self.log_test("Unified search interface displayed", False, f"Error: {e}")
            return False

    async def test_filter_dropdowns_work(self):
        """Test that filter dropdowns work without white screen errors"""
        try:
            await self.take_screenshot("02_before_filter_test")
            
            # Test manufacturers dropdown
            print("   Testing manufacturers dropdown...")
            manufacturers_button = await self.page.locator("text=Manufacturers").first
            await manufacturers_button.click()
            await self.page.wait_for_timeout(1000)
            
            # Check if dropdown opened without errors
            dropdown_visible = await self.page.locator(".absolute.top-full.left-0.right-0").first.is_visible()
            
            # Take screenshot of opened dropdown
            await self.take_screenshot("03_manufacturers_dropdown_open")
            
            # Close dropdown
            await self.page.keyboard.press("Escape")
            await self.page.wait_for_timeout(500)
            
            # Test categories dropdown
            print("   Testing categories dropdown...")
            categories_button = await self.page.locator("text=Categories").first
            await categories_button.click()
            await self.page.wait_for_timeout(1000)
            
            # Check if dropdown opened
            categories_dropdown_visible = await self.page.locator(".absolute.top-full.left-0.right-0").first.is_visible()
            
            # Take screenshot
            await self.take_screenshot("04_categories_dropdown_open")
            
            # Close dropdown
            await self.page.keyboard.press("Escape")
            await self.page.wait_for_timeout(500)
            
            success = dropdown_visible and categories_dropdown_visible
            details = f"Manufacturers dropdown: {dropdown_visible}, Categories dropdown: {categories_dropdown_visible}"
            
            await self.log_test("Filter dropdowns work without errors", success, details)
            return success
        except Exception as e:
            await self.log_test("Filter dropdowns work without errors", False, f"Error: {e}")
            return False

    async def test_select_filters(self):
        """Test selecting specific filters (Hu-Friedy manufacturer and Instruments category)"""
        try:
            # Select manufacturer: Hu-Friedy
            print("   Selecting Hu-Friedy manufacturer...")
            manufacturers_button = await self.page.locator("text=Manufacturers").first
            await manufacturers_button.click()
            await self.page.wait_for_timeout(1000)
            
            # Look for Hu-Friedy option (case insensitive)
            hu_friedy_option = await self.page.locator("text*='hu-friedy', text*='Hu-Friedy'").first
            if await hu_friedy_option.is_visible():
                await hu_friedy_option.click()
                print("   ✓ Hu-Friedy selected")
            else:
                # Try alternative approach - look for any manufacturer option
                first_manufacturer = await self.page.locator(".absolute.top-full button").first
                if await first_manufacturer.is_visible():
                    await first_manufacturer.click()
                    print("   ✓ First manufacturer selected (fallback)")
            
            await self.page.wait_for_timeout(1000)
            
            # Select category: Instruments
            print("   Selecting Instruments category...")
            categories_button = await self.page.locator("text=Categories").first
            await categories_button.click()
            await self.page.wait_for_timeout(1000)
            
            # Look for Instruments option
            instruments_option = await self.page.locator("text*='Instruments'").first
            if await instruments_option.is_visible():
                await instruments_option.click()
                print("   ✓ Instruments selected")
            else:
                # Try alternative approach
                first_category = await self.page.locator(".absolute.top-full button").first
                if await first_category.is_visible():
                    await first_category.click()
                    print("   ✓ First category selected (fallback)")
            
            await self.page.wait_for_timeout(1000)
            
            # Take screenshot of selected filters
            await self.take_screenshot("05_filters_selected")
            
            # Check if filters are selected (look for selected indicators)
            selected_indicators = await self.page.locator(".bg-primary-100, .bg-primary-50").count()
            success = selected_indicators > 0
            
            await self.log_test("Filters selected successfully", success, f"Selected indicators: {selected_indicators}")
            return success
        except Exception as e:
            await self.log_test("Filters selected successfully", False, f"Error: {e}")
            return False

    async def test_filtered_suggestions(self):
        """Test that search suggestions are filtered based on selected filters"""
        try:
            # Type search query to trigger suggestions
            print("   Typing 'composite' to trigger suggestions...")
            search_input = await self.page.locator("input[placeholder*='Search dental products']").first
            await search_input.fill("composite")
            await self.page.wait_for_timeout(2000)
            
            # Check if suggestions appear
            suggestions_container = await self.page.locator(".absolute.top-full.left-0.right-0.mt-1").first
            suggestions_visible = await suggestions_container.is_visible()
            
            # Count suggestions
            suggestions_count = await self.page.locator(".absolute.top-full button").count()
            
            # Take screenshot of suggestions
            await self.take_screenshot("06_filtered_suggestions")
            
            success = suggestions_visible and suggestions_count > 0
            details = f"Suggestions visible: {suggestions_visible}, Count: {suggestions_count}"
            
            await self.log_test("Filtered suggestions appear", success, details)
            return success
        except Exception as e:
            await self.log_test("Filtered suggestions appear", False, f"Error: {e}")
            return False

    async def test_search_execution(self):
        """Test executing search with filters and verifying results"""
        try:
            # Execute search
            print("   Executing search...")
            search_button = await self.page.locator("button:has-text('Search')").first
            await search_button.click()
            await self.page.wait_for_timeout(3000)
            
            # Wait for navigation to search page
            await self.page.wait_for_url("**/search**", timeout=10000)
            
            # Take screenshot of search results
            await self.take_screenshot("07_search_results")
            
            # Check if search results are displayed
            results_found = await self.page.locator("text*='results', text*='found', [data-testid*='result']").count() > 0
            
            # Check URL contains filters
            current_url = self.page.url
            has_filters_in_url = "manufactured_by" in current_url or "category" in current_url
            
            success = results_found
            details = f"Results found: {results_found}, Filters in URL: {has_filters_in_url}"
            
            await self.log_test("Search executes with filters", success, details)
            return success
        except Exception as e:
            await self.log_test("Search executes with filters", False, f"Error: {e}")
            return False

    async def test_api_endpoints(self):
        """Test that API endpoints return correct data"""
        try:
            # Test manufacturers endpoint
            response = await self.page.request.get("http://localhost:8000/api/v1/products/manufacturers/?limit=5")
            manufacturers_ok = response.status == 200
            
            # Test categories endpoint
            response = await self.page.request.get("http://localhost:8000/api/v1/products/categories/?limit=5")
            categories_ok = response.status == 200
            
            # Test filtered suggestions
            response = await self.page.request.get("http://localhost:8000/api/v1/search/suggestions?q=comp&manufactured_by=hu-friedy")
            suggestions_ok = response.status == 200
            
            success = manufacturers_ok and categories_ok and suggestions_ok
            details = f"Manufacturers: {manufacturers_ok}, Categories: {categories_ok}, Suggestions: {suggestions_ok}"
            
            await self.log_test("API endpoints working correctly", success, details)
            return success
        except Exception as e:
            await self.log_test("API endpoints working correctly", False, f"Error: {e}")
            return False

    async def run_all_tests(self):
        """Run all tests"""
        print("🧪 Starting comprehensive unified search interface tests...")
        print("=" * 70)
        
        await self.setup()
        
        tests = [
            self.test_homepage_loads,
            self.test_unified_search_interface,
            self.test_filter_dropdowns_work,
            self.test_select_filters,
            self.test_filtered_suggestions,
            self.test_search_execution,
            self.test_api_endpoints,
        ]
        
        passed = 0
        total = len(tests)
        
        for test in tests:
            try:
                result = await test()
                if result:
                    passed += 1
                await asyncio.sleep(1)  # Brief pause between tests
            except Exception as e:
                print(f"❌ Test failed with exception: {e}")
        
        print("=" * 70)
        print(f"🎯 Test Results: {passed}/{total} tests passed")
        
        if passed == total:
            print("🎉 ALL TESTS PASSED! Unified search interface is working correctly.")
        else:
            print("⚠️  Some tests failed. Check the details above.")
        
        print("\n📸 Screenshots taken:")
        print("   - screenshot_01_homepage_loaded.png")
        print("   - screenshot_02_before_filter_test.png")
        print("   - screenshot_03_manufacturers_dropdown_open.png")
        print("   - screenshot_04_categories_dropdown_open.png")
        print("   - screenshot_05_filters_selected.png")
        print("   - screenshot_06_filtered_suggestions.png")
        print("   - screenshot_07_search_results.png")
        
        print("\n📋 Manual Testing Instructions:")
        print("1. The browser window is open for manual testing")
        print("2. Test the following manually:")
        print("   - Verify filters and search are in the same container")
        print("   - Select different manufacturers and categories")
        print("   - Type search queries and verify suggestions are filtered")
        print("   - Perform searches and verify results match filters")
        print("   - Ensure no white screen errors occur")
        print("3. Press Enter when done to close browser")
        
        # Keep browser open for manual testing
        input("\nPress Enter to close browser and finish testing...")
        
        await self.browser.close()
        return passed == total

async def main():
    """Main test runner"""
    tester = UnifiedSearchTester()
    success = await tester.run_all_tests()
    return 0 if success else 1

if __name__ == "__main__":
    try:
        exit_code = asyncio.run(main())
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n🛑 Tests interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"❌ Test runner failed: {e}")
        sys.exit(1)

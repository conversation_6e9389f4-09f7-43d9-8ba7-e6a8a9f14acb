#!/usr/bin/env python3
"""
Test script to verify enhanced JWT tokens with role and permission information
"""

import asyncio
import asyncpg
import sys
from pathlib import Path

# Add the app directory to Python path
sys.path.append(str(Path(__file__).parent))

from app.config import settings
from app.utils.security import create_token_response, verify_token, extract_token_data
from app.models.user import UserRepository
from app.services.auth_service import AuthService

async def test_enhanced_jwt_tokens():
    """Test enhanced JWT tokens with role and permission information."""
    
    print("🔐 Testing Enhanced JWT Tokens with RBAC")
    print("=" * 50)
    
    try:
        # Connect to database
        db_url = settings.DATABASE_URL.replace("postgresql+asyncpg://", "postgresql://")
        conn = await asyncpg.connect(db_url)
        print("✅ Database connection successful")
        
        # Test 1: Get a test user with roles and permissions
        print("\n👤 Test 1: Loading user with roles and permissions...")
        test_users = await conn.fetch("""
            SELECT id, email, is_superuser 
            FROM users 
            WHERE email LIKE '%test%' 
            LIMIT 1
        """)
        
        if not test_users:
            print("❌ No test users found")
            return False
        
        test_user = test_users[0]
        user_id = test_user['id']
        email = test_user['email']
        
        print(f"✅ Test user: {email}")
        
        # Load user with RBAC data
        user_with_roles = await UserRepository.get_user_with_roles_and_permissions(conn, user_id)
        if user_with_roles:
            print(f"✅ User loaded with roles: {user_with_roles.roles}")
            print(f"✅ User permissions: {list(user_with_roles.permissions)[:5]}...")  # Show first 5
        else:
            print("❌ Failed to load user with RBAC data")
            return False
        
        # Test 2: Create enhanced JWT token
        print("\n🔑 Test 2: Creating enhanced JWT token...")
        tokens = create_token_response(
            user_id=user_with_roles.id,
            email=user_with_roles.email,
            roles=user_with_roles.roles,
            permissions=list(user_with_roles.permissions),
            is_superuser=user_with_roles.is_superuser
        )
        
        print("✅ Enhanced JWT tokens created successfully")
        print(f"✅ Token type: {tokens['token_type']}")
        print(f"✅ Expires in: {tokens['expires_in']} seconds")
        
        # Test 3: Verify and decode the token
        print("\n🔍 Test 3: Verifying enhanced JWT token...")
        access_token = tokens['access_token']
        payload = verify_token(access_token)
        
        if payload:
            print("✅ JWT token verified successfully")
            print(f"✅ Token contains user_id: {payload.get('sub')}")
            print(f"✅ Token contains email: {payload.get('email')}")
            print(f"✅ Token contains roles: {payload.get('roles')}")
            print(f"✅ Token contains permissions: {len(payload.get('permissions', []))} permissions")
            print(f"✅ Token contains is_superuser: {payload.get('is_superuser')}")
        else:
            print("❌ JWT token verification failed")
            return False
        
        # Test 4: Extract TokenData from payload
        print("\n📊 Test 4: Extracting TokenData from payload...")
        token_data = extract_token_data(payload)
        
        print(f"✅ TokenData extracted successfully")
        print(f"✅ User ID: {token_data.user_id}")
        print(f"✅ Email: {token_data.email}")
        print(f"✅ Roles: {token_data.roles}")
        print(f"✅ Is superuser: {token_data.is_superuser}")
        print(f"✅ Permission count: {len(token_data.permissions)}")
        
        # Test 5: Test permission checking methods
        print("\n🔐 Test 5: Testing permission checking methods...")
        
        # Test has_permission method
        has_products_read = token_data.has_permission('products.read')
        has_admin_access = token_data.has_permission('system.admin')
        
        print(f"✅ Has 'products.read' permission: {has_products_read}")
        print(f"✅ Has 'system.admin' permission: {has_admin_access}")
        
        # Test has_role method
        has_user_role = token_data.has_role('user')
        has_superadmin_role = token_data.has_role('superadmin')
        
        print(f"✅ Has 'user' role: {has_user_role}")
        print(f"✅ Has 'superadmin' role: {has_superadmin_role}")
        
        # Test 6: Test AuthService with enhanced tokens
        print("\n🔧 Test 6: Testing AuthService with enhanced tokens...")
        
        # Test get_current_user with the new token
        current_user = await AuthService.get_current_user(conn, access_token)
        if current_user:
            print(f"✅ AuthService.get_current_user() works with enhanced token")
            print(f"✅ Current user: {current_user.email}")
            print(f"✅ Current user roles: {current_user.roles}")
            print(f"✅ Current user has 'products.read': {current_user.has_permission('products.read')}")
        else:
            print("❌ AuthService.get_current_user() failed")
            return False
        
        # Test 7: Compare old vs new token structure
        print("\n📋 Test 7: Comparing token structures...")
        
        # Create old-style token for comparison
        old_tokens = create_token_response(user_id, email)  # Without role parameters
        old_payload = verify_token(old_tokens['access_token'])
        
        print("Old token payload keys:", list(old_payload.keys()))
        print("New token payload keys:", list(payload.keys()))
        
        # Verify backward compatibility
        if old_payload.get('sub') == payload.get('sub') and old_payload.get('email') == payload.get('email'):
            print("✅ Backward compatibility maintained")
        else:
            print("❌ Backward compatibility broken")
            return False
        
        print("\n🎉 Enhanced JWT Token Test Completed Successfully!")
        print("=" * 50)
        print("✅ JWT tokens now include role and permission information")
        print("✅ TokenData class provides convenient permission checking")
        print("✅ AuthService loads users with RBAC data")
        print("✅ Backward compatibility is maintained")
        
        return True
        
    except Exception as e:
        print(f"\n❌ Enhanced JWT Token Test Failed: {e}")
        import traceback
        traceback.print_exc()
        return False
        
    finally:
        if 'conn' in locals():
            await conn.close()
            print("\n📡 Database connection closed")

if __name__ == "__main__":
    success = asyncio.run(test_enhanced_jwt_tokens())
    sys.exit(0 if success else 1)

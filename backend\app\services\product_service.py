"""
Product service for product matching and management
Handles finding same products across different sellers using MFR codes
"""

import time
import json
from typing import List, Dict, Any, Optional, Tuple
from asyncpg import Connection
import redis.asyncio as redis

try:
    from ..models.product import Product, ProductRepository
    from ..schemas.product import (
        ProductMatchRequest, ProductMatchResponse, ProductResponse,
        ProductCategoryResponse, ProductBrandResponse, ProductManufacturerResponse, ProductSellerResponse
    )
    from ..config import settings
except ImportError:
    from models.product import Product, ProductRepository
    from schemas.product import (
        ProductMatchRequest, ProductMatchResponse, ProductResponse,
        ProductCategoryResponse, ProductBrandResponse, ProductManufacturerResponse, ProductSellerResponse
    )
    from config import settings


class ProductService:
    """Service for product operations and matching."""
    
    @staticmethod
    async def find_product_matches(
        conn: Connection,
        redis_client: redis.Redis,
        match_request: ProductMatchRequest
    ) -> ProductMatchResponse:
        """
        Find all products with the same MFR code across different sellers.
        This allows users to compare prices for the same product from different sellers.
        """
        start_time = time.time()
        
        # Create cache key
        cache_key = f"product_match:{match_request.mfr}:{match_request.limit}:{match_request.offset}"
        
        # Try to get from cache first
        try:
            cached_result = await redis_client.get(cache_key)
            if cached_result:
                cached_data = json.loads(cached_result)
                cached_data["message"] = "Product matches found successfully (cached)"
                return ProductMatchResponse(**cached_data)
        except Exception:
            # Cache miss or error, continue with database search
            pass
        
        # Get products by MFR code
        products, total = await ProductRepository.get_products_by_mfr(
            conn, match_request.mfr, match_request.limit, match_request.offset
        )
        
        # Convert to response format
        product_responses = []
        for product in products:
            product_response = ProductResponse(
                id=product.id,
                mfr=product.mfr,
                name=product.name,
                url=product.url,
                maincat=product.maincat,
                brand=product.brand,
                manufactured_by=product.manufactured_by,
                category=product.category,
                seller=product.seller,
                price=product.price,
                search_text=product.search_text,
                created_at=product.created_at,
                updated_at=product.updated_at
            )
            product_responses.append(product_response)
        
        # Calculate pagination
        total_pages = (total + match_request.limit - 1) // match_request.limit
        current_page = (match_request.offset // match_request.limit) + 1
        
        pagination = {
            "total": total,
            "page": current_page,
            "per_page": match_request.limit,
            "total_pages": total_pages,
            "has_next": current_page < total_pages,
            "has_prev": current_page > 1
        }
        
        # Create response
        response_data = {
            "success": True,
            "message": "Product matches found successfully",
            "mfr": match_request.mfr,
            "total": total,
            "products": [product.dict() for product in product_responses],
            "pagination": pagination
        }
        
        # Cache the result
        try:
            await redis_client.setex(
                cache_key, 
                settings.SEARCH_CACHE_TTL, 
                json.dumps(response_data, default=str)
            )
        except Exception:
            # Cache error, continue without caching
            pass
        
        return ProductMatchResponse(**response_data)
    
    @staticmethod
    async def get_product_by_id(
        conn: Connection,
        redis_client: redis.Redis,
        product_id: int
    ) -> Optional[ProductResponse]:
        """Get a single product by ID with caching."""
        cache_key = f"product:{product_id}"
        
        # Try cache first
        try:
            cached_result = await redis_client.get(cache_key)
            if cached_result:
                product_data = json.loads(cached_result)
                return ProductResponse(**product_data)
        except Exception:
            pass
        
        # Get from database
        product = await ProductRepository.get_product_by_id(conn, product_id)
        if not product:
            return None
        
        product_response = ProductResponse(
            id=product.id,
            mfr=product.mfr,
            name=product.name,
            url=product.url,
            maincat=product.maincat,
            brand=product.brand,
            manufactured_by=product.manufactured_by,
            category=product.category,
            seller=product.seller,
            price=product.price,
            search_text=product.search_text,
            created_at=product.created_at,
            updated_at=product.updated_at
        )
        
        # Cache the product
        try:
            await redis_client.setex(
                cache_key, 
                3600,  # 1 hour
                json.dumps(product_response.dict(), default=str)
            )
        except Exception:
            pass
        
        return product_response
    
    @staticmethod
    async def get_product_categories(
        conn: Connection,
        redis_client: redis.Redis,
        limit: int = 100
    ) -> ProductCategoryResponse:
        """Get all product categories with product counts."""
        cache_key = f"categories:{limit}"

        # Try cache first
        try:
            cached_result = await redis_client.get(cache_key)
            if cached_result:
                categories = json.loads(cached_result)
                return ProductCategoryResponse(categories=categories)
        except Exception:
            pass

        # Get from database - FIXED: Use 'category' column instead of 'maincat'
        query = """
            SELECT category, COUNT(*) as product_count
            FROM products
            WHERE category IS NOT NULL AND category != ''
            GROUP BY category
            ORDER BY product_count DESC, category
            LIMIT $1
        """
        
        records = await conn.fetch(query, limit)
        categories = [
            {"category": record["category"], "product_count": record["product_count"]}
            for record in records
        ]

        # Cache categories
        try:
            await redis_client.setex(cache_key, 3600, json.dumps(categories))  # 1 hour
        except Exception:
            pass

        return ProductCategoryResponse(categories=categories)
    
    @staticmethod
    async def get_product_manufacturers(
        conn: Connection,
        redis_client: redis.Redis,
        limit: int = 100
    ) -> ProductManufacturerResponse:
        """Get all product manufacturers with product counts."""
        cache_key = f"manufacturers:{limit}"

        # Try cache first
        try:
            cached_result = await redis_client.get(cache_key)
            if cached_result:
                manufacturers = json.loads(cached_result)
                return ProductManufacturerResponse(manufacturers=manufacturers)
        except Exception:
            pass

        # Get from database - FIXED: Use 'manufactured_by' column instead of 'brand'
        query = """
            SELECT manufactured_by, COUNT(*) as product_count
            FROM products
            WHERE manufactured_by IS NOT NULL AND manufactured_by != ''
            GROUP BY manufactured_by
            ORDER BY product_count DESC, manufactured_by
            LIMIT $1
        """

        records = await conn.fetch(query, limit)
        manufacturers = [
            {"manufacturer": record["manufactured_by"], "product_count": record["product_count"]}
            for record in records
        ]

        # Cache brands
        try:
            await redis_client.setex(cache_key, 3600, json.dumps(manufacturers))  # 1 hour
        except Exception:
            pass

        return ProductManufacturerResponse(manufacturers=manufacturers)

    # Keep the old method for backward compatibility during transition
    @staticmethod
    async def get_product_brands(
        conn: Connection,
        redis_client: redis.Redis,
        limit: int = 100
    ) -> ProductBrandResponse:
        """Get all product brands with product counts. DEPRECATED: Use get_product_manufacturers instead."""
        # Redirect to manufacturers for now
        manufacturer_result = await ProductService.get_product_manufacturers(conn, redis_client, limit)
        # Convert format for backward compatibility
        brands = [
            {"brand": item["manufacturer"], "product_count": item["product_count"]}
            for item in manufacturer_result.manufacturers
        ]
        return ProductBrandResponse(brands=brands)
    
    @staticmethod
    async def get_product_sellers(
        conn: Connection,
        redis_client: redis.Redis,
        limit: int = 50
    ) -> ProductSellerResponse:
        """Get all sellers with product counts."""
        cache_key = f"sellers:{limit}"
        
        # Try cache first
        try:
            cached_result = await redis_client.get(cache_key)
            if cached_result:
                sellers = json.loads(cached_result)
                return ProductSellerResponse(sellers=sellers)
        except Exception:
            pass
        
        # Get from database
        query = """
            SELECT seller, COUNT(*) as product_count
            FROM products 
            GROUP BY seller
            ORDER BY product_count DESC, seller
            LIMIT $1
        """
        
        records = await conn.fetch(query, limit)
        sellers = [
            {"seller": record["seller"], "product_count": record["product_count"]}
            for record in records
        ]
        
        # Cache sellers
        try:
            await redis_client.setex(cache_key, 3600, json.dumps(sellers))  # 1 hour
        except Exception:
            pass
        
        return ProductSellerResponse(sellers=sellers)
    
    @staticmethod
    async def get_price_comparison(
        conn: Connection,
        redis_client: redis.Redis,
        mfr: str
    ) -> Dict[str, Any]:
        """
        Get price comparison for a product across all sellers.
        Returns pricing information and statistics.
        """
        cache_key = f"price_comparison:{mfr}"
        
        # Try cache first
        try:
            cached_result = await redis_client.get(cache_key)
            if cached_result:
                return json.loads(cached_result)
        except Exception:
            pass
        
        # Get all products with this MFR code
        products, _ = await ProductRepository.get_products_by_mfr(conn, mfr, limit=100, offset=0)
        
        if not products:
            return {
                "mfr": mfr,
                "found": False,
                "message": "No products found with this MFR code"
            }
        
        # Parse prices and calculate statistics
        price_data = []
        for product in products:
            if product.price:
                try:
                    # Extract numeric price (assuming format like "$123.45" or "123.45")
                    price_str = product.price.replace("$", "").replace(",", "").strip()
                    if price_str and price_str.replace(".", "").isdigit():
                        price_float = float(price_str)
                        price_data.append({
                            "seller": product.seller,
                            "price": price_float,
                            "price_str": product.price,
                            "url": product.url,
                            "product_id": product.id
                        })
                except (ValueError, AttributeError):
                    continue
        
        if not price_data:
            comparison_data = {
                "mfr": mfr,
                "found": True,
                "product_name": products[0].name,
                "total_sellers": len(products),
                "sellers_with_prices": 0,
                "message": "Products found but no valid prices available",
                "products": [
                    {
                        "seller": p.seller,
                        "price": p.price,
                        "url": p.url,
                        "product_id": p.id
                    } for p in products
                ]
            }
        else:
            # Calculate price statistics
            prices = [item["price"] for item in price_data]
            min_price = min(prices)
            max_price = max(prices)
            avg_price = sum(prices) / len(prices)
            
            # Sort by price
            price_data.sort(key=lambda x: x["price"])
            
            comparison_data = {
                "mfr": mfr,
                "found": True,
                "product_name": products[0].name,
                "total_sellers": len(products),
                "sellers_with_prices": len(price_data),
                "price_stats": {
                    "min_price": min_price,
                    "max_price": max_price,
                    "avg_price": round(avg_price, 2),
                    "price_range": max_price - min_price,
                    "savings_potential": round(max_price - min_price, 2)
                },
                "price_comparison": price_data,
                "best_deal": price_data[0] if price_data else None,
                "most_expensive": price_data[-1] if price_data else None
            }
        
        # Cache the comparison
        try:
            await redis_client.setex(cache_key, 1800, json.dumps(comparison_data, default=str))  # 30 minutes
        except Exception:
            pass
        
        return comparison_data

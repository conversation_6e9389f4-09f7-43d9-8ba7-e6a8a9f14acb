import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { useState, useEffect } from "react";
import { useSearchStore, buildSearchRequest } from "../stores/searchStore";
import apiClient from "../lib/api";
import { SearchRequest } from "../types";

// Search products
export const useSearch = (searchRequest?: SearchRequest) => {
  const { query, filters, pagination } = useSearchStore();
  const { setResults, setError, setLoading } = useSearchStore();

  const finalRequest =
    searchRequest || buildSearchRequest(useSearchStore.getState());

  return useQuery({
    queryKey: ["search", finalRequest],
    queryFn: async () => {
      setLoading(true);
      try {
        const response = await apiClient.search(finalRequest);
        setResults(response.results, response.total, response.search_time_ms);
        return response;
      } catch (error: any) {
        const errorMessage = error.response?.data?.detail || "Search failed";
        setError(errorMessage);
        throw error;
      }
    },
    enabled: !!finalRequest.q && finalRequest.q.length > 0,
    staleTime: 1000 * 60 * 5, // 5 minutes
    retry: 1,
  });
};

// Search suggestions with optional filtering
export const useSearchSuggestions = (
  query: string,
  enabled = true,
  filters?: {
    category?: string;
    manufactured_by?: string;
    brand?: string;
    seller?: string;
  }
) => {
  const { setSuggestions } = useSearchStore();

  return useQuery({
    queryKey: ["search-suggestions", query, filters],
    queryFn: async () => {
      const response = await apiClient.getSuggestions(query, 50, filters);
      setSuggestions(response.suggestions);
      return response;
    },
    enabled: enabled && query.length >= 2,
    staleTime: 1000 * 60 * 10, // 10 minutes
    retry: 1,
  });
};

// Popular searches
export const usePopularSearches = () => {
  const { setPopularSearches } = useSearchStore();

  return useQuery({
    queryKey: ["popular-searches"],
    queryFn: async () => {
      const response = await apiClient.getPopularSearches();
      // Extract just the term strings from the response
      const searchTerms = response.popular_searches.map(
        (search) => search.term
      );
      setPopularSearches(searchTerms);
      return response;
    },
    staleTime: 1000 * 60 * 30, // 30 minutes
    retry: 1,
  });
};

// Filter data hooks
export const useCategories = (search?: string) => {
  return useQuery({
    queryKey: ["categories", search],
    queryFn: async () => {
      const response = await apiClient.getCategories(search);
      return response;
    },
    staleTime: 1000 * 60 * 15, // 15 minutes
    retry: 1,
  });
};

export const useManufacturers = (search?: string) => {
  return useQuery({
    queryKey: ["manufacturers", search],
    queryFn: async () => {
      const response = await apiClient.getManufacturers(search);
      return response;
    },
    staleTime: 1000 * 60 * 15, // 15 minutes
    retry: 1,
  });
};

export const useBrands = (search?: string) => {
  // DEPRECATED: Use useManufacturers instead
  return useQuery({
    queryKey: ["brands", search],
    queryFn: async () => {
      const response = await apiClient.getBrands(search);
      return response;
    },
    staleTime: 1000 * 60 * 15, // 15 minutes
    retry: 1,
  });
};

export const useSellers = (search?: string) => {
  return useQuery({
    queryKey: ["sellers", search],
    queryFn: async () => {
      const response = await apiClient.getSellers(search);
      return response;
    },
    staleTime: 1000 * 60 * 15, // 15 minutes
    retry: 1,
  });
};

// Single product
export const useProduct = (id: number) => {
  return useQuery({
    queryKey: ["product", id],
    queryFn: () => apiClient.getProduct(id),
    enabled: !!id,
    staleTime: 1000 * 60 * 10, // 10 minutes
    retry: 1,
  });
};

// Custom hook for debounced search
export const useDebouncedSearch = (query: string, delay = 300) => {
  const [debouncedQuery, setDebouncedQuery] = useState(query);

  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedQuery(query);
    }, delay);

    return () => clearTimeout(timer);
  }, [query, delay]);

  return useSearch({ q: debouncedQuery, limit: 20, offset: 0 });
};

// Search with automatic state management
export const useAutoSearch = () => {
  const queryClient = useQueryClient();
  const {
    query,
    filters,
    pagination,
    setLoading,
    setResults,
    setError,
    addRecentSearch,
  } = useSearchStore();

  const searchMutation = useMutation({
    mutationFn: async (searchRequest: SearchRequest) => {
      setLoading(true);
      return await apiClient.search(searchRequest);
    },
    onSuccess: (data) => {
      setResults(data.results, data.total, data.search_time_ms);
      if (data.query) {
        addRecentSearch(data.query);
      }
      // Invalidate related queries
      queryClient.invalidateQueries({ queryKey: ["search-suggestions"] });
    },
    onError: (error: any) => {
      const errorMessage = error.response?.data?.detail || "Search failed";
      setError(errorMessage);
    },
  });

  const performSearch = (customRequest?: Partial<SearchRequest>) => {
    const request =
      customRequest || buildSearchRequest(useSearchStore.getState());
    if (request.q && request.q.length > 0) {
      searchMutation.mutate(request as SearchRequest);
    }
  };

  return {
    performSearch,
    isLoading: searchMutation.isPending,
    error: searchMutation.error,
    data: searchMutation.data,
  };
};

import { Outlet } from "react-router-dom";
import { useAuthStore } from "../stores/authStore";
import Header from "./Header";
import Sidebar from "./Sidebar";
import { ShoppingListNotifications } from "./shopping-list/ShoppingListNotifications";
import { useState } from "react";

const Layout = () => {
  const { isAuthenticated } = useAuthStore();
  const [sidebarOpen, setSidebarOpen] = useState(false);

  if (!isAuthenticated) {
    return <Outlet />;
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <Header onMenuClick={() => setSidebarOpen(!sidebarOpen)} />

      <div className="flex">
        <Sidebar isOpen={sidebarOpen} onClose={() => setSidebarOpen(false)} />

        <main className="flex-1 lg:ml-64">
          <div className="px-4 sm:px-6 lg:px-8 py-8">
            <Outlet />
          </div>
        </main>
      </div>

      {/* Shopping List Notifications */}
      <ShoppingListNotifications />
    </div>
  );
};

export default Layout;

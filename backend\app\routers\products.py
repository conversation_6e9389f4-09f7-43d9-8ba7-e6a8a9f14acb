"""
Product API routes for product matching and management
"""

from fastapi import APIRouter, Depends, HTTPException, status, Query, Path
from asyncpg import Connection
import redis.asyncio as redis

try:
    from ..database import get_db_connection, get_redis_client
    from ..services.product_service import ProductService
    from ..schemas.product import (
        ProductMatchRequest, ProductMatchResponse, ProductResponse,
        ProductCategoryResponse, ProductBrandResponse, ProductManufacturerResponse, ProductSellerResponse
    )
    from ..dependencies import get_current_user, api_rate_limiter
    from ..models.user import User
    from ..middleware.authorization import require_permission
except ImportError:
    from database import get_db_connection, get_redis_client
    from services.product_service import ProductService
    from schemas.product import (
        ProductMatchRequest, ProductMatchResponse, ProductResponse,
        ProductCategoryResponse, ProductBrandResponse, ProductManufacturerResponse, ProductSellerResponse
    )
    from dependencies import get_current_user, api_rate_limiter
    from models.user import User
    from middleware.authorization import require_permission

# Create router
router = APIRouter(prefix="/products", tags=["products"])


async def get_current_user_with_product_permission(current_user: User = Depends(get_current_user)) -> User:
    """
    Dependency for product endpoints that require product permissions when user is authenticated.

    This maintains backward compatibility:
    - If no user is authenticated (current_user is None), allow access
    - If user is authenticated, they must have products.read permission
    """
    if current_user is None:
        return None

    # Check if authenticated user has product read permission
    if not current_user.has_permission("products.read") and not current_user.is_superuser:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Insufficient permissions. Product access requires 'products.read' permission."
        )

    return current_user


@router.get("/match/{mfr}", response_model=ProductMatchResponse)
async def find_product_matches(
    mfr: str = Path(..., description="Manufacturer code to find matches for"),
    limit: int = Query(20, ge=1, le=100, description="Maximum results"),
    offset: int = Query(0, ge=0, description="Results offset"),
    conn: Connection = Depends(get_db_connection),
    redis_client: redis.Redis = Depends(get_redis_client),
    current_user: User = Depends(get_current_user_with_product_permission)
):
    """
    Find all products with the same MFR code across different sellers.
    
    This endpoint allows users to compare prices for the same product from different sellers.
    The MFR (manufacturer) code is used to identify identical products across different sellers.
    
    **Features:**
    - Find same products across all sellers
    - Compare prices from different sellers
    - Sorted by seller and price for easy comparison
    - Pagination support for large result sets
    - Redis caching for improved performance
    
    **Use Cases:**
    - Price comparison shopping
    - Finding the best deal for a specific product
    - Checking product availability across sellers
    - Bulk purchasing decisions
    """
    try:
        # Apply rate limiting
        if current_user:
            await api_rate_limiter(current_user.id, redis_client)
        
        # Create match request
        match_request = ProductMatchRequest(
            mfr=mfr,
            limit=limit,
            offset=offset
        )
        
        # Find product matches
        result = await ProductService.find_product_matches(conn, redis_client, match_request)
        
        return result
    
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to find product matches: {str(e)}"
        )


@router.get("/{product_id}", response_model=ProductResponse)
async def get_product(
    product_id: int = Path(..., description="Product ID"),
    conn: Connection = Depends(get_db_connection),
    redis_client: redis.Redis = Depends(get_redis_client),
    current_user: User = Depends(get_current_user_with_product_permission)
):
    """
    Get a single product by ID.
    
    Returns detailed information about a specific product including
    all available fields and metadata.
    """
    try:
        product = await ProductService.get_product_by_id(conn, redis_client, product_id)
        
        if not product:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Product not found"
            )
        
        return product
    
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get product: {str(e)}"
        )


@router.get("/compare/{mfr}", response_model=dict)
async def get_price_comparison(
    mfr: str = Path(..., description="Manufacturer code for price comparison"),
    conn: Connection = Depends(get_db_connection),
    redis_client: redis.Redis = Depends(get_redis_client),
    current_user: User = Depends(get_current_user_with_product_permission)
):
    """
    Get detailed price comparison for a product across all sellers.
    
    Returns comprehensive pricing information including:
    - Price statistics (min, max, average)
    - Best deals and most expensive options
    - Savings potential
    - Detailed price breakdown by seller
    
    **Features:**
    - Price statistics and analysis
    - Savings potential calculation
    - Best deal identification
    - Seller comparison
    - Price trend analysis
    """
    try:
        # Apply rate limiting
        if current_user:
            await api_rate_limiter(current_user.id, redis_client)
        
        comparison = await ProductService.get_price_comparison(conn, redis_client, mfr)
        
        return {
            "success": True,
            "message": "Price comparison retrieved successfully",
            "data": comparison
        }
    
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get price comparison: {str(e)}"
        )


@router.get("/categories/", response_model=ProductCategoryResponse)
async def get_product_categories(
    limit: int = Query(100, ge=1, le=500, description="Maximum categories"),
    conn: Connection = Depends(get_db_connection),
    redis_client: redis.Redis = Depends(get_redis_client)
):
    """
    Get all product categories with product counts.
    
    Returns a list of all available product categories sorted by
    product count (most popular first) with the number of products
    in each category.
    """
    try:
        result = await ProductService.get_product_categories(conn, redis_client, limit)
        return result
    
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get categories: {str(e)}"
        )


@router.get("/manufacturers/", response_model=ProductManufacturerResponse)
async def get_product_manufacturers(
    limit: int = Query(100, ge=1, le=500, description="Maximum manufacturers"),
    conn: Connection = Depends(get_db_connection),
    redis_client: redis.Redis = Depends(get_redis_client)
):
    """
    Get all product manufacturers with product counts.

    Returns a list of all available product manufacturers sorted by
    product count (most popular first) with the number of products
    for each manufacturer.
    """
    try:
        result = await ProductService.get_product_manufacturers(conn, redis_client, limit)
        return result

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get manufacturers: {str(e)}"
        )


@router.get("/brands/", response_model=ProductBrandResponse)
async def get_product_brands(
    limit: int = Query(100, ge=1, le=500, description="Maximum brands"),
    conn: Connection = Depends(get_db_connection),
    redis_client: redis.Redis = Depends(get_redis_client)
):
    """
    Get all product brands with product counts. DEPRECATED: Use /manufacturers/ instead.

    Returns a list of all available product brands sorted by
    product count (most popular first) with the number of products
    for each brand.
    """
    try:
        result = await ProductService.get_product_brands(conn, redis_client, limit)
        return result

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get brands: {str(e)}"
        )


@router.get("/sellers/", response_model=ProductSellerResponse)
async def get_product_sellers(
    limit: int = Query(50, ge=1, le=200, description="Maximum sellers"),
    conn: Connection = Depends(get_db_connection),
    redis_client: redis.Redis = Depends(get_redis_client)
):
    """
    Get all sellers with product counts.
    
    Returns a list of all available sellers sorted by
    product count (largest inventory first) with the number of products
    each seller offers.
    """
    try:
        result = await ProductService.get_product_sellers(conn, redis_client, limit)
        return result
    
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get sellers: {str(e)}"
        )


@router.get("/stats/", response_model=dict)
async def get_product_stats(
    conn: Connection = Depends(get_db_connection),
    redis_client: redis.Redis = Depends(get_redis_client)
):
    """
    Get comprehensive product statistics.
    
    Returns statistics about the product database including:
    - Total products
    - Unique MFR codes
    - Categories, brands, and sellers counts
    - Database health information
    """
    try:
        # Get basic stats
        total_products = await conn.fetchval("SELECT COUNT(*) FROM products")
        unique_mfr = await conn.fetchval("SELECT COUNT(DISTINCT mfr) FROM products")
        unique_categories = await conn.fetchval(
            "SELECT COUNT(DISTINCT maincat) FROM products WHERE maincat IS NOT NULL"
        )
        unique_brands = await conn.fetchval(
            "SELECT COUNT(DISTINCT brand) FROM products WHERE brand IS NOT NULL"
        )
        unique_sellers = await conn.fetchval("SELECT COUNT(DISTINCT seller) FROM products")
        
        # Get products with multiple sellers (for price comparison)
        products_with_multiple_sellers = await conn.fetchval("""
            SELECT COUNT(DISTINCT mfr) 
            FROM products 
            WHERE mfr IN (
                SELECT mfr 
                FROM products 
                GROUP BY mfr 
                HAVING COUNT(DISTINCT seller) > 1
            )
        """)
        
        stats = {
            "total_products": total_products,
            "unique_mfr_codes": unique_mfr,
            "unique_categories": unique_categories,
            "unique_brands": unique_brands,
            "unique_sellers": unique_sellers,
            "products_with_multiple_sellers": products_with_multiple_sellers,
            "price_comparison_opportunities": products_with_multiple_sellers
        }
        
        return {
            "success": True,
            "message": "Product statistics retrieved successfully",
            "stats": stats
        }
    
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get product stats: {str(e)}"
        )


# Health check for products service
@router.get("/health/", response_model=dict)
async def products_health_check(
    conn: Connection = Depends(get_db_connection),
    redis_client: redis.Redis = Depends(get_redis_client)
):
    """Products service health check."""
    try:
        # Test database connection
        product_count = await conn.fetchval("SELECT COUNT(*) FROM products LIMIT 1")
        
        # Test Redis connection
        await redis_client.ping()
        
        return {
            "success": True,
            "message": "Products service is healthy",
            "database_connection": "healthy",
            "redis_connection": "healthy",
            "sample_product_count": product_count or 0
        }
    
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail=f"Products service unhealthy: {str(e)}"
        )

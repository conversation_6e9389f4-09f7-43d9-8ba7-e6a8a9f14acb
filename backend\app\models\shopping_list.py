"""
Shopping list database models and operations
"""

import uuid
from datetime import datetime
from typing import Optional, List, Dict, Any
from asyncpg import Connection

try:
    from ..models.product import Product
except ImportError:
    from models.product import Product


class ShoppingList:
    """Shopping list model for database operations."""
    
    def __init__(
        self,
        id: int = None,
        user_id: str = None,
        name: str = None,
        description: str = None,
        is_default: bool = False,
        created_at: datetime = None,
        updated_at: datetime = None
    ):
        self.id = id
        self.user_id = user_id
        self.name = name
        self.description = description
        self.is_default = is_default
        self.created_at = created_at
        self.updated_at = updated_at
    
    @classmethod
    def from_record(cls, record) -> "ShoppingList":
        """Create ShoppingList instance from database record."""
        if not record:
            return None
        
        return cls(
            id=record["id"],
            user_id=str(record["user_id"]),
            name=record["name"],
            description=record["description"],
            is_default=record.get("is_default", False),
            created_at=record["created_at"],
            updated_at=record["updated_at"]
        )
    
    def to_dict(self) -> dict:
        """Convert shopping list to dictionary."""
        return {
            "id": self.id,
            "user_id": self.user_id,
            "name": self.name,
            "description": self.description,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None,
        }


class ShoppingListItem:
    """Shopping list item model for database operations."""
    
    def __init__(
        self,
        id: int = None,
        shopping_list_id: int = None,
        product_id: int = None,
        quantity: int = 1,
        notes: str = None,
        added_at: datetime = None
    ):
        self.id = id
        self.shopping_list_id = shopping_list_id
        self.product_id = product_id
        self.quantity = quantity
        self.notes = notes
        self.added_at = added_at
    
    @classmethod
    def from_record(cls, record) -> "ShoppingListItem":
        """Create ShoppingListItem instance from database record."""
        if not record:
            return None
        
        return cls(
            id=record["id"],
            shopping_list_id=record["shopping_list_id"],
            product_id=record["product_id"],
            quantity=record["quantity"],
            notes=record["notes"],
            added_at=record["added_at"]
        )
    
    def to_dict(self) -> dict:
        """Convert shopping list item to dictionary."""
        return {
            "id": self.id,
            "shopping_list_id": self.shopping_list_id,
            "product_id": self.product_id,
            "quantity": self.quantity,
            "notes": self.notes,
            "added_at": self.added_at.isoformat() if self.added_at else None,
        }


class ShoppingListRepository:
    """Shopping list database operations."""
    
    @staticmethod
    async def create_shopping_list(
        conn: Connection,
        user_id: str,
        name: str,
        description: str = None,
        is_default: bool = False
    ) -> ShoppingList:
        """Create a new shopping list."""
        # If this should be the default list, unset any existing default
        if is_default:
            await conn.execute(
                "UPDATE shopping_lists SET is_default = FALSE WHERE user_id = $1 AND is_default = TRUE",
                user_id
            )

        query = """
            INSERT INTO shopping_lists (user_id, name, description, is_default)
            VALUES ($1, $2, $3, $4)
            RETURNING *
        """

        record = await conn.fetchrow(query, user_id, name, description, is_default)
        return ShoppingList.from_record(record)
    
    @staticmethod
    async def get_shopping_list_by_id(
        conn: Connection,
        list_id: int,
        user_id: str = None
    ) -> Optional[ShoppingList]:
        """Get shopping list by ID, optionally filtered by user."""
        if user_id:
            query = "SELECT * FROM shopping_lists WHERE id = $1 AND user_id = $2"
            record = await conn.fetchrow(query, list_id, user_id)
        else:
            query = "SELECT * FROM shopping_lists WHERE id = $1"
            record = await conn.fetchrow(query, list_id)
        
        return ShoppingList.from_record(record)
    
    @staticmethod
    async def get_user_shopping_lists(
        conn: Connection,
        user_id: str,
        limit: int = 50,
        offset: int = 0
    ) -> tuple[List[ShoppingList], int]:
        """Get user's shopping lists with pagination."""
        where_clause = "WHERE user_id = $1"
        params = [user_id]
        
        # Count query
        count_query = f"SELECT COUNT(*) FROM shopping_lists {where_clause}"
        total_count = await conn.fetchval(count_query, *params)
        
        # Main query
        query = f"""
            SELECT * FROM shopping_lists 
            {where_clause}
            ORDER BY updated_at DESC, created_at DESC
            LIMIT $2 OFFSET $3
        """
        
        params.extend([limit, offset])
        records = await conn.fetch(query, *params)
        
        shopping_lists = [ShoppingList.from_record(record) for record in records]
        return shopping_lists, total_count
    
    @staticmethod
    async def update_shopping_list(
        conn: Connection,
        list_id: int,
        user_id: str,
        name: str = None,
        description: str = None,
        is_default: bool = None
    ) -> Optional[ShoppingList]:
        """Update shopping list."""
        # If setting as default, unset any existing default first
        if is_default is True:
            await conn.execute(
                "UPDATE shopping_lists SET is_default = FALSE WHERE user_id = $1 AND is_default = TRUE AND id != $2",
                user_id, list_id
            )

        updates = []
        values = []
        param_count = 1

        if name is not None:
            updates.append(f"name = ${param_count}")
            values.append(name)
            param_count += 1

        if description is not None:
            updates.append(f"description = ${param_count}")
            values.append(description)
            param_count += 1

        if is_default is not None:
            updates.append(f"is_default = ${param_count}")
            values.append(is_default)
            param_count += 1

        if not updates:
            return await ShoppingListRepository.get_shopping_list_by_id(conn, list_id, user_id)

        updates.append(f"updated_at = NOW()")
        values.extend([list_id, user_id])

        query = f"""
            UPDATE shopping_lists
            SET {', '.join(updates)}
            WHERE id = ${param_count} AND user_id = ${param_count + 1}
            RETURNING *
        """
        
        record = await conn.fetchrow(query, *values)
        return ShoppingList.from_record(record)

    @staticmethod
    async def get_default_shopping_list(
        conn: Connection,
        user_id: str
    ) -> Optional[ShoppingList]:
        """Get user's default shopping list."""
        query = """
            SELECT * FROM shopping_lists
            WHERE user_id = $1 AND is_default = TRUE
            LIMIT 1
        """

        record = await conn.fetchrow(query, user_id)
        return ShoppingList.from_record(record)
    
    @staticmethod
    async def delete_shopping_list(
        conn: Connection,
        list_id: int,
        user_id: str
    ) -> bool:
        """Delete shopping list (hard delete)."""
        query = """
            DELETE FROM shopping_lists
            WHERE id = $1 AND user_id = $2
        """

        result = await conn.execute(query, list_id, user_id)
        return "DELETE 1" in result


class ShoppingListItemRepository:
    """Shopping list item database operations."""
    
    @staticmethod
    async def add_item_to_list(
        conn: Connection,
        shopping_list_id: int,
        product_id: int,
        quantity: int = 1,
        notes: str = None
    ) -> ShoppingListItem:
        """Add item to shopping list."""
        query = """
            INSERT INTO shopping_list_items (shopping_list_id, product_id, quantity, notes)
            VALUES ($1, $2, $3, $4)
            RETURNING *
        """
        
        record = await conn.fetchrow(query, shopping_list_id, product_id, quantity, notes)
        return ShoppingListItem.from_record(record)
    
    @staticmethod
    async def get_list_items(
        conn: Connection,
        shopping_list_id: int,
        include_purchased: bool = True
    ) -> List[Dict[str, Any]]:
        """Get all items in a shopping list with product details."""
        where_clause = "WHERE sli.shopping_list_id = $1"
        params = [shopping_list_id]
        
        query = f"""
            SELECT 
                sli.*,
                p.mfr, p.name as product_name, p.brand, p.seller, p.price, p.url
            FROM shopping_list_items sli
            JOIN products p ON sli.product_id = p.id
            {where_clause}
            ORDER BY sli.added_at DESC
        """
        
        records = await conn.fetch(query, *params)
        
        items = []
        for record in records:
            item = ShoppingListItem.from_record(record)
            item_dict = item.to_dict()
            item_dict["product"] = {
                "id": record["product_id"],
                "mfr": record["mfr"],
                "name": record["product_name"],
                "brand": record["brand"],
                "seller": record["seller"],
                "price": record["price"],
                "url": record["url"]
            }
            items.append(item_dict)
        
        return items
    
    @staticmethod
    async def update_list_item(
        conn: Connection,
        item_id: int,
        quantity: int = None,
        notes: str = None
    ) -> Optional[ShoppingListItem]:
        """Update shopping list item."""
        updates = []
        values = []
        param_count = 1

        if quantity is not None:
            updates.append(f"quantity = ${param_count}")
            values.append(quantity)
            param_count += 1

        if notes is not None:
            updates.append(f"notes = ${param_count}")
            values.append(notes)
            param_count += 1

        if not updates:
            record = await conn.fetchrow("SELECT * FROM shopping_list_items WHERE id = $1", item_id)
            return ShoppingListItem.from_record(record)

        values.append(item_id)

        query = f"""
            UPDATE shopping_list_items
            SET {', '.join(updates)}
            WHERE id = ${param_count}
            RETURNING *
        """

        record = await conn.fetchrow(query, *values)
        return ShoppingListItem.from_record(record)
    
    @staticmethod
    async def remove_item_from_list(
        conn: Connection,
        item_id: int
    ) -> bool:
        """Remove item from shopping list."""
        query = "DELETE FROM shopping_list_items WHERE id = $1"
        result = await conn.execute(query, item_id)
        return "DELETE 1" in result
    
    @staticmethod
    async def get_list_summary(
        conn: Connection,
        shopping_list_id: int
    ) -> Dict[str, Any]:
        """Get shopping list summary with statistics."""
        query = """
            SELECT
                COUNT(*) as total_items,
                SUM(quantity) as total_quantity
            FROM shopping_list_items
            WHERE shopping_list_id = $1
        """

        record = await conn.fetchrow(query, shopping_list_id)

        return {
            "total_items": record["total_items"] or 0,
            "purchased_items": 0,  # Not supported in current schema
            "remaining_items": record["total_items"] or 0,
            "total_quantity": record["total_quantity"] or 0,
            "purchased_quantity": 0,  # Not supported in current schema
            "completion_percentage": 0  # Not supported in current schema
        }

import { useState } from "react";
import {
  User,
  Mail,
  Calendar,
  Settings,
  Shield,
  CheckCircle,
  XCircle,
} from "lucide-react";
import { useAuthStore } from "../stores/authStore";
import {
  useSearchPermissions,
  useShoppingListPermissions,
  useAdminPermissions,
} from "../hooks/useRoles";
import { formatDate } from "../lib/utils";

const ProfilePage = () => {
  const { user, logout } = useAuthStore();
  const [activeTab, setActiveTab] = useState("profile");

  // Get permission information
  const searchPermissions = useSearchPermissions();
  const shoppingListPermissions = useShoppingListPermissions();
  const adminPermissions = useAdminPermissions();

  if (!user) {
    return (
      <div className="max-w-4xl mx-auto text-center py-12">
        <h1 className="text-2xl font-bold text-gray-900 mb-4">
          Profile Not Found
        </h1>
        <p className="text-gray-600">Please log in to view your profile.</p>
      </div>
    );
  }

  const tabs = [
    { id: "profile", name: "Profile", icon: User },
    { id: "roles", name: "Roles & Permissions", icon: Shield },
    { id: "settings", name: "Settings", icon: Settings },
    { id: "security", name: "Security", icon: Shield },
  ];

  return (
    <div className="max-w-4xl mx-auto">
      {/* Header */}
      <div className="bg-white rounded-lg border border-gray-200 mb-8">
        <div className="px-6 py-8">
          <div className="flex items-center space-x-6">
            <div className="flex-shrink-0">
              <div className="w-20 h-20 bg-primary-100 rounded-full flex items-center justify-center">
                <User className="w-10 h-10 text-primary-600" />
              </div>
            </div>
            <div className="flex-1">
              <h1 className="text-2xl font-bold text-gray-900">
                {user.full_name}
              </h1>
              <p className="text-gray-600 flex items-center mt-1">
                <Mail className="w-4 h-4 mr-2" />
                {user.email}
              </p>
              <p className="text-sm text-gray-500 flex items-center mt-1">
                <Calendar className="w-4 h-4 mr-2" />
                Member since {formatDate(user.created_at)}
              </p>
            </div>
            <div className="flex-shrink-0">
              <span
                className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${
                  user.is_active
                    ? "bg-green-100 text-green-800"
                    : "bg-red-100 text-red-800"
                }`}
              >
                {user.is_active ? "Active" : "Inactive"}
              </span>
            </div>
          </div>
        </div>
      </div>

      {/* Tabs */}
      <div className="bg-white rounded-lg border border-gray-200">
        <div className="border-b border-gray-200">
          <nav className="flex space-x-8 px-6">
            {tabs.map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`py-4 px-1 border-b-2 font-medium text-sm transition-colors ${
                  activeTab === tab.id
                    ? "border-primary-500 text-primary-600"
                    : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"
                }`}
              >
                <tab.icon className="w-4 h-4 inline mr-2" />
                {tab.name}
              </button>
            ))}
          </nav>
        </div>

        <div className="p-6">
          {/* Profile Tab */}
          {activeTab === "profile" && (
            <div className="space-y-6">
              <div>
                <h2 className="text-lg font-semibold text-gray-900 mb-4">
                  Profile Information
                </h2>
                <div className="grid md:grid-cols-2 gap-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Full Name
                    </label>
                    <input
                      type="text"
                      value={user.full_name}
                      readOnly
                      className="w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-50 text-gray-500"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Email Address
                    </label>
                    <input
                      type="email"
                      value={user.email}
                      readOnly
                      className="w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-50 text-gray-500"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Account Status
                    </label>
                    <input
                      type="text"
                      value={user.is_active ? "Active" : "Inactive"}
                      readOnly
                      className="w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-50 text-gray-500"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Member Since
                    </label>
                    <input
                      type="text"
                      value={formatDate(user.created_at)}
                      readOnly
                      className="w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-50 text-gray-500"
                    />
                  </div>
                </div>
                <div className="mt-6">
                  <p className="text-sm text-gray-600">
                    Profile editing will be available in a future update.
                  </p>
                </div>
              </div>
            </div>
          )}

          {/* Roles & Permissions Tab */}
          {activeTab === "roles" && (
            <div className="space-y-6">
              <div>
                <h2 className="text-lg font-semibold text-gray-900 mb-4">
                  Roles & Permissions
                </h2>

                {/* User Roles */}
                <div className="mb-6">
                  <h3 className="text-md font-medium text-gray-900 mb-3">
                    Your Roles
                  </h3>
                  <div className="flex flex-wrap gap-2">
                    {user.roles && user.roles.length > 0 ? (
                      user.roles.map((role, index) => (
                        <span
                          key={index}
                          className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800"
                        >
                          <Shield className="w-4 h-4 mr-1" />
                          {role}
                        </span>
                      ))
                    ) : (
                      <span className="text-sm text-gray-500">
                        No specific roles assigned
                      </span>
                    )}

                    {user.is_superuser && (
                      <span className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-red-100 text-red-800">
                        <Shield className="w-4 h-4 mr-1" />
                        Super Administrator
                      </span>
                    )}
                  </div>
                </div>

                {/* Permission Summary */}
                <div className="mb-6">
                  <h3 className="text-md font-medium text-gray-900 mb-3">
                    Permission Summary
                  </h3>
                  <div className="grid md:grid-cols-2 gap-4">
                    {/* Search Permissions */}
                    <div className="bg-gray-50 rounded-lg p-4">
                      <h4 className="font-medium text-gray-900 mb-2 flex items-center">
                        <CheckCircle className="w-4 h-4 mr-2 text-green-500" />
                        Search & Products
                      </h4>
                      <div className="space-y-1 text-sm">
                        <div className="flex items-center justify-between">
                          <span>Search Products</span>
                          {searchPermissions.canSearch ? (
                            <CheckCircle className="w-4 h-4 text-green-500" />
                          ) : (
                            <XCircle className="w-4 h-4 text-red-500" />
                          )}
                        </div>
                        <div className="flex items-center justify-between">
                          <span>View Product Details</span>
                          {searchPermissions.canViewProducts ? (
                            <CheckCircle className="w-4 h-4 text-green-500" />
                          ) : (
                            <XCircle className="w-4 h-4 text-red-500" />
                          )}
                        </div>
                      </div>
                    </div>

                    {/* Shopping List Permissions */}
                    <div className="bg-gray-50 rounded-lg p-4">
                      <h4 className="font-medium text-gray-900 mb-2 flex items-center">
                        <CheckCircle className="w-4 h-4 mr-2 text-green-500" />
                        Shopping Lists
                      </h4>
                      <div className="space-y-1 text-sm">
                        <div className="flex items-center justify-between">
                          <span>View Lists</span>
                          {shoppingListPermissions.canRead ? (
                            <CheckCircle className="w-4 h-4 text-green-500" />
                          ) : (
                            <XCircle className="w-4 h-4 text-red-500" />
                          )}
                        </div>
                        <div className="flex items-center justify-between">
                          <span>Create Lists</span>
                          {shoppingListPermissions.canCreate ? (
                            <CheckCircle className="w-4 h-4 text-green-500" />
                          ) : (
                            <XCircle className="w-4 h-4 text-red-500" />
                          )}
                        </div>
                        <div className="flex items-center justify-between">
                          <span>Manage Lists</span>
                          {shoppingListPermissions.canManage ? (
                            <CheckCircle className="w-4 h-4 text-green-500" />
                          ) : (
                            <XCircle className="w-4 h-4 text-red-500" />
                          )}
                        </div>
                      </div>
                    </div>

                    {/* Admin Permissions */}
                    {adminPermissions.canAccessAdmin && (
                      <div className="bg-gray-50 rounded-lg p-4 md:col-span-2">
                        <h4 className="font-medium text-gray-900 mb-2 flex items-center">
                          <Shield className="w-4 h-4 mr-2 text-blue-500" />
                          Administrative Access
                        </h4>
                        <div className="grid md:grid-cols-3 gap-4 text-sm">
                          <div className="flex items-center justify-between">
                            <span>User Management</span>
                            {adminPermissions.hasUserManagement ? (
                              <CheckCircle className="w-4 h-4 text-green-500" />
                            ) : (
                              <XCircle className="w-4 h-4 text-red-500" />
                            )}
                          </div>
                          <div className="flex items-center justify-between">
                            <span>Role Management</span>
                            {adminPermissions.hasRoleManagement ? (
                              <CheckCircle className="w-4 h-4 text-green-500" />
                            ) : (
                              <XCircle className="w-4 h-4 text-red-500" />
                            )}
                          </div>
                          <div className="flex items-center justify-between">
                            <span>System Access</span>
                            {adminPermissions.hasSystemAccess ? (
                              <CheckCircle className="w-4 h-4 text-green-500" />
                            ) : (
                              <XCircle className="w-4 h-4 text-red-500" />
                            )}
                          </div>
                        </div>
                      </div>
                    )}
                  </div>
                </div>

                {/* Raw Permissions (for debugging) */}
                {user.permissions && user.permissions.length > 0 && (
                  <div>
                    <h3 className="text-md font-medium text-gray-900 mb-3">
                      Raw Permissions
                    </h3>
                    <div className="bg-gray-50 rounded-lg p-4">
                      <div className="flex flex-wrap gap-1">
                        {user.permissions.map((permission, index) => (
                          <span
                            key={index}
                            className="inline-block px-2 py-1 text-xs font-mono bg-gray-200 text-gray-700 rounded"
                          >
                            {permission}
                          </span>
                        ))}
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </div>
          )}

          {/* Settings Tab */}
          {activeTab === "settings" && (
            <div className="space-y-6">
              <div>
                <h2 className="text-lg font-semibold text-gray-900 mb-4">
                  Account Settings
                </h2>
                <div className="space-y-4">
                  <div className="flex items-center justify-between py-3 border-b border-gray-200">
                    <div>
                      <h3 className="text-sm font-medium text-gray-900">
                        Email Notifications
                      </h3>
                      <p className="text-sm text-gray-600">
                        Receive email updates about your account
                      </p>
                    </div>
                    <input
                      type="checkbox"
                      defaultChecked
                      className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                    />
                  </div>

                  <div className="flex items-center justify-between py-3 border-b border-gray-200">
                    <div>
                      <h3 className="text-sm font-medium text-gray-900">
                        Search History
                      </h3>
                      <p className="text-sm text-gray-600">
                        Save your search history for quick access
                      </p>
                    </div>
                    <input
                      type="checkbox"
                      defaultChecked
                      className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                    />
                  </div>

                  <div className="flex items-center justify-between py-3">
                    <div>
                      <h3 className="text-sm font-medium text-gray-900">
                        Marketing Communications
                      </h3>
                      <p className="text-sm text-gray-600">
                        Receive updates about new features and products
                      </p>
                    </div>
                    <input
                      type="checkbox"
                      className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                    />
                  </div>
                </div>
                <div className="mt-6">
                  <p className="text-sm text-gray-600">
                    Settings will be functional in a future update.
                  </p>
                </div>
              </div>
            </div>
          )}

          {/* Security Tab */}
          {activeTab === "security" && (
            <div className="space-y-6">
              <div>
                <h2 className="text-lg font-semibold text-gray-900 mb-4">
                  Security Settings
                </h2>
                <div className="space-y-4">
                  <div className="bg-gray-50 rounded-lg p-4">
                    <h3 className="text-sm font-medium text-gray-900 mb-2">
                      Password
                    </h3>
                    <p className="text-sm text-gray-600 mb-3">
                      Last changed: Not available
                    </p>
                    <button className="text-sm text-primary-600 hover:text-primary-700 font-medium">
                      Change Password
                    </button>
                  </div>

                  <div className="bg-gray-50 rounded-lg p-4">
                    <h3 className="text-sm font-medium text-gray-900 mb-2">
                      Two-Factor Authentication
                    </h3>
                    <p className="text-sm text-gray-600 mb-3">
                      Add an extra layer of security to your account
                    </p>
                    <button className="text-sm text-primary-600 hover:text-primary-700 font-medium">
                      Enable 2FA
                    </button>
                  </div>

                  <div className="bg-red-50 rounded-lg p-4">
                    <h3 className="text-sm font-medium text-red-900 mb-2">
                      Danger Zone
                    </h3>
                    <p className="text-sm text-red-600 mb-3">
                      Permanently delete your account and all associated data
                    </p>
                    <button className="text-sm text-red-600 hover:text-red-700 font-medium">
                      Delete Account
                    </button>
                  </div>
                </div>
                <div className="mt-6">
                  <p className="text-sm text-gray-600">
                    Security features will be implemented in a future update.
                  </p>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Logout Button */}
      <div className="mt-8 text-center">
        <button
          onClick={logout}
          className="inline-flex items-center px-4 py-2 border border-red-300 text-sm font-medium rounded-md text-red-700 bg-white hover:bg-red-50 transition-colors"
        >
          Sign Out
        </button>
      </div>
    </div>
  );
};

export default ProfilePage;

#!/usr/bin/env python3
"""
Test script for enhanced password hashing with stronger algorithms.
Tests Argon2, bcrypt, scrypt algorithms and password migration.
"""

import asyncio
import time
from app.utils.security import (
    get_password_hash, 
    verify_password, 
    verify_and_upgrade_password,
    needs_password_rehash,
    get_password_hash_info
)
from app.config import settings

def test_password_hashing_algorithms():
    """Test different password hashing algorithms."""
    print("🔐 Testing Enhanced Password Hashing Algorithms")
    print("=" * 60)
    
    test_password = "TestPassword123!"
    
    # Test each algorithm
    algorithms = ["argon2", "bcrypt", "scrypt"]
    
    for algorithm in algorithms:
        print(f"\n🧪 Testing {algorithm.upper()} algorithm...")
        
        try:
            # Time the hashing operation
            start_time = time.time()
            hashed = get_password_hash(test_password, algorithm=algorithm)
            hash_time = time.time() - start_time
            
            print(f"✅ {algorithm} hashing successful")
            print(f"   Hash length: {len(hashed)} characters")
            print(f"   Hash time: {hash_time:.3f} seconds")
            print(f"   Hash preview: {hashed[:50]}...")
            
            # Test verification
            start_time = time.time()
            is_valid = verify_password(test_password, hashed)
            verify_time = time.time() - start_time
            
            if is_valid:
                print(f"✅ {algorithm} verification successful")
                print(f"   Verify time: {verify_time:.3f} seconds")
            else:
                print(f"❌ {algorithm} verification failed")
                return False
            
            # Test wrong password
            if not verify_password("WrongPassword", hashed):
                print(f"✅ {algorithm} correctly rejects wrong password")
            else:
                print(f"❌ {algorithm} incorrectly accepts wrong password")
                return False
            
            # Test hash info
            hash_info = get_password_hash_info(hashed)
            print(f"   Algorithm detected: {hash_info['algorithm']}")
            print(f"   Needs update: {hash_info['needs_update']}")
            
        except Exception as e:
            print(f"❌ {algorithm} test failed: {e}")
            return False
    
    return True

def test_default_algorithm():
    """Test the default configured algorithm."""
    print(f"\n🎯 Testing Default Algorithm ({settings.PASSWORD_HASH_ALGORITHM})")
    print("=" * 60)
    
    test_password = "DefaultAlgorithmTest123!"
    
    try:
        # Hash with default algorithm
        hashed = get_password_hash(test_password)
        print(f"✅ Default algorithm hashing successful")
        
        # Check which algorithm was used
        hash_info = get_password_hash_info(hashed)
        expected_algorithm = settings.PASSWORD_HASH_ALGORITHM
        actual_algorithm = hash_info['algorithm']
        
        if actual_algorithm == expected_algorithm:
            print(f"✅ Correct algorithm used: {actual_algorithm}")
        else:
            print(f"⚠️ Expected {expected_algorithm}, got {actual_algorithm}")
        
        # Verify password
        if verify_password(test_password, hashed):
            print("✅ Default algorithm verification successful")
        else:
            print("❌ Default algorithm verification failed")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Default algorithm test failed: {e}")
        return False

def test_password_migration():
    """Test password hash migration/upgrade functionality."""
    print("\n🔄 Testing Password Migration/Upgrade")
    print("=" * 60)
    
    test_password = "MigrationTest123!"
    
    try:
        # Create an old bcrypt hash (simulating existing password)
        old_hash = get_password_hash(test_password, algorithm="bcrypt")
        print(f"✅ Created old bcrypt hash")
        
        # Test if it needs upgrade (depends on configuration)
        needs_upgrade = needs_password_rehash(old_hash)
        print(f"   Needs upgrade: {needs_upgrade}")
        
        # Test upgrade functionality
        is_valid, new_hash = verify_and_upgrade_password(test_password, old_hash)
        
        if not is_valid:
            print("❌ Password verification failed during migration test")
            return False
        
        if new_hash:
            print("✅ Password hash upgrade performed")
            print(f"   Old hash algorithm: {get_password_hash_info(old_hash)['algorithm']}")
            print(f"   New hash algorithm: {get_password_hash_info(new_hash)['algorithm']}")
            
            # Verify new hash works
            if verify_password(test_password, new_hash):
                print("✅ New hash verification successful")
            else:
                print("❌ New hash verification failed")
                return False
        else:
            print("ℹ️ No upgrade needed (hash is already current)")
        
        return True
        
    except Exception as e:
        print(f"❌ Password migration test failed: {e}")
        return False

def test_configuration_values():
    """Test that configuration values are properly loaded."""
    print("\n⚙️ Testing Configuration Values")
    print("=" * 60)
    
    try:
        print(f"Password hash algorithm: {settings.PASSWORD_HASH_ALGORITHM}")
        print(f"Bcrypt rounds: {settings.BCRYPT_ROUNDS}")
        print(f"Argon2 time cost: {settings.ARGON2_TIME_COST}")
        print(f"Argon2 memory cost: {settings.ARGON2_MEMORY_COST}")
        print(f"Argon2 parallelism: {settings.ARGON2_PARALLELISM}")
        print(f"Scrypt rounds: {settings.SCRYPT_ROUNDS}")
        print(f"Scrypt block size: {settings.SCRYPT_BLOCK_SIZE}")
        print(f"Scrypt parallelism: {settings.SCRYPT_PARALLELISM}")
        
        # Validate configuration values
        if settings.BCRYPT_ROUNDS < 10 or settings.BCRYPT_ROUNDS > 20:
            print("⚠️ Bcrypt rounds should be between 10-20 for security/performance balance")
        
        if settings.ARGON2_TIME_COST < 1:
            print("⚠️ Argon2 time cost should be at least 1")
        
        if settings.ARGON2_MEMORY_COST < 1024:
            print("⚠️ Argon2 memory cost should be at least 1024 KB")
        
        print("✅ Configuration values loaded successfully")
        return True
        
    except Exception as e:
        print(f"❌ Configuration test failed: {e}")
        return False

def test_performance_comparison():
    """Compare performance of different algorithms."""
    print("\n⚡ Performance Comparison")
    print("=" * 60)
    
    test_password = "PerformanceTest123!"
    algorithms = ["argon2", "bcrypt", "scrypt"]
    
    results = {}
    
    for algorithm in algorithms:
        try:
            # Test hashing performance
            start_time = time.time()
            hashed = get_password_hash(test_password, algorithm=algorithm)
            hash_time = time.time() - start_time
            
            # Test verification performance
            start_time = time.time()
            verify_password(test_password, hashed)
            verify_time = time.time() - start_time
            
            results[algorithm] = {
                "hash_time": hash_time,
                "verify_time": verify_time
            }
            
            print(f"{algorithm:8} - Hash: {hash_time:.3f}s, Verify: {verify_time:.3f}s")
            
        except Exception as e:
            print(f"❌ {algorithm} performance test failed: {e}")
    
    if results:
        print("\n📊 Performance Summary:")
        fastest_hash = min(results.keys(), key=lambda k: results[k]["hash_time"])
        fastest_verify = min(results.keys(), key=lambda k: results[k]["verify_time"])
        print(f"   Fastest hashing: {fastest_hash}")
        print(f"   Fastest verification: {fastest_verify}")
    
    return True

def main():
    """Run all enhanced password hashing tests."""
    print("🧪 Enhanced Password Hashing Test Suite")
    print("=" * 60)
    
    tests = [
        ("Configuration Values", test_configuration_values),
        ("Password Hashing Algorithms", test_password_hashing_algorithms),
        ("Default Algorithm", test_default_algorithm),
        ("Password Migration", test_password_migration),
        ("Performance Comparison", test_performance_comparison),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n🔍 Running: {test_name}")
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name}: PASSED")
            else:
                print(f"❌ {test_name}: FAILED")
        except Exception as e:
            print(f"💥 {test_name}: ERROR - {e}")
    
    print("\n" + "=" * 60)
    print("📊 ENHANCED PASSWORD HASHING TEST SUMMARY")
    print("=" * 60)
    print(f"✅ Passed: {passed}/{total}")
    print(f"❌ Failed: {total - passed}/{total}")
    
    if passed == total:
        print("\n🎉 All enhanced password hashing tests passed!")
        return True
    else:
        print("\n⚠️ Some tests failed. Please review the results above.")
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)

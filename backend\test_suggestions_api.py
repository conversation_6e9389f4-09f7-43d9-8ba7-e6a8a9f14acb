#!/usr/bin/env python3
"""
Test the updated suggestions API that returns product objects instead of strings.
Validates that suggestions now include complete product information for shopping list functionality.
"""

import asyncio
import asyncpg
import os
import sys
import time
import json
from typing import List, Dict, Any

# Add the app directory to the path to import config
sys.path.append(os.path.join(os.path.dirname(__file__), 'app'))

try:
    from config import settings, get_database_url_sync
    from services.search_service import SearchService
    from database import DatabaseManager
    import redis.asyncio as redis
except ImportError:
    settings = None

class SuggestionsAPITester:
    """Tests the updated suggestions API with product objects."""
    
    def __init__(self):
        if settings:
            self.db_url = get_database_url_sync()
        else:
            self.db_url = "postgresql://profident_user:profident_dev_password@localhost:5433/profident_dev"
        
        print(f"🔗 Using database URL: {self.db_url[:50]}...")
    
    async def get_connection(self):
        """Get database connection."""
        return await asyncpg.connect(self.db_url)
    
    async def get_redis_client(self):
        """Get Redis client."""
        return redis.Redis(host='localhost', port=6380, db=0, decode_responses=True)
    
    async def test_suggestions_api(self):
        """Test the suggestions API with various queries."""
        print("🔍 Testing updated suggestions API...")
        
        conn = await self.get_connection()
        redis_client = await self.get_redis_client()
        
        try:
            # Test queries
            test_queries = [
                "dental",
                "implant", 
                "110402",  # MFR search
                "crown"
            ]
            
            for query in test_queries:
                print(f"\n🧪 Testing query: '{query}'")
                
                start_time = time.time()
                
                # Call the SearchService method directly
                result = await SearchService.get_search_suggestions(
                    conn, redis_client, query, limit=5
                )
                
                query_time = (time.time() - start_time) * 1000
                
                print(f"  ⏱️ Response time: {query_time:.1f}ms")
                print(f"  📊 Found {len(result.suggestions)} suggestions")
                print(f"  🎯 Query: {result.query}")
                print(f"  ✅ Success: {result.success}")
                
                # Validate suggestion structure
                for i, suggestion in enumerate(result.suggestions[:3]):
                    print(f"    {i+1}. ID: {suggestion.id}")
                    print(f"       MFR: {suggestion.mfr}")
                    print(f"       Name: {suggestion.name[:50]}...")
                    print(f"       Seller: {suggestion.seller}")
                    print(f"       Price: {suggestion.price}")
                    print(f"       URL: {suggestion.url[:50]}...")
                    
                    # Validate required fields for shopping list
                    assert suggestion.id is not None, "ID is required for shopping list"
                    assert suggestion.mfr is not None, "MFR is required"
                    assert suggestion.name is not None, "Name is required"
                    assert suggestion.seller is not None, "Seller is required"
                    assert suggestion.url is not None, "URL is required"
                    
                    print(f"       ✅ All required fields present")
                
                print()
        
        finally:
            await conn.close()
            await redis_client.close()
    
    async def test_suggestions_with_filters(self):
        """Test suggestions API with filters."""
        print("🔧 Testing suggestions with filters...")
        
        conn = await self.get_connection()
        redis_client = await self.get_redis_client()
        
        try:
            # Test with category filter
            print("🧪 Testing with category filter...")
            
            result = await SearchService.get_search_suggestions(
                conn, redis_client, "dental", limit=3, 
                filters={"category": "Restorative"}
            )
            
            print(f"  📊 Found {len(result.suggestions)} filtered suggestions")
            
            for suggestion in result.suggestions:
                print(f"    - {suggestion.name[:40]}... (Category: {suggestion.category})")
            
            # Test with seller filter
            print("\n🧪 Testing with seller filter...")
            
            result = await SearchService.get_search_suggestions(
                conn, redis_client, "implant", limit=3,
                filters={"seller": "benco"}
            )
            
            print(f"  📊 Found {len(result.suggestions)} seller-filtered suggestions")
            
            for suggestion in result.suggestions:
                print(f"    - {suggestion.name[:40]}... (Seller: {suggestion.seller})")
        
        finally:
            await conn.close()
            await redis_client.close()
    
    async def test_cache_functionality(self):
        """Test that caching works with the new product objects."""
        print("💾 Testing cache functionality...")
        
        conn = await self.get_connection()
        redis_client = await self.get_redis_client()
        
        try:
            query = "dental"
            
            # Clear any existing cache
            cache_key = f"suggestions:{query.lower()}:5"
            await redis_client.delete(cache_key)
            
            # First call - should hit database
            print("🔍 First call (database)...")
            start_time = time.time()
            result1 = await SearchService.get_search_suggestions(
                conn, redis_client, query, limit=5
            )
            time1 = (time.time() - start_time) * 1000
            
            # Second call - should hit cache
            print("🔍 Second call (cache)...")
            start_time = time.time()
            result2 = await SearchService.get_search_suggestions(
                conn, redis_client, query, limit=5
            )
            time2 = (time.time() - start_time) * 1000
            
            print(f"  📊 Database call: {time1:.1f}ms")
            print(f"  📊 Cache call: {time2:.1f}ms")
            print(f"  🚀 Cache speedup: {time1/time2:.1f}x")
            
            # Verify results are identical
            assert len(result1.suggestions) == len(result2.suggestions), "Cache results differ in count"
            
            for i, (s1, s2) in enumerate(zip(result1.suggestions, result2.suggestions)):
                assert s1.id == s2.id, f"Suggestion {i} ID mismatch"
                assert s1.name == s2.name, f"Suggestion {i} name mismatch"
                assert s1.mfr == s2.mfr, f"Suggestion {i} MFR mismatch"
            
            print("  ✅ Cache results match database results")
        
        finally:
            await conn.close()
            await redis_client.close()

async def main():
    """Main function to test suggestions API."""
    tester = SuggestionsAPITester()
    
    try:
        print("🎯 Testing Enhanced Suggestions API (Product Objects)")
        print("=" * 60)
        
        await tester.test_suggestions_api()
        await tester.test_suggestions_with_filters()
        await tester.test_cache_functionality()
        
        print("\n🎉 Suggestions API testing completed successfully!")
        print("✅ Suggestions now return complete product objects for shopping list integration")
        
    except Exception as e:
        print(f"❌ Error testing suggestions API: {e}")
        import traceback
        traceback.print_exc()
        raise

if __name__ == "__main__":
    asyncio.run(main())

#!/usr/bin/env python3
"""
Add missing role management permissions to the database
"""

import asyncio
import asyncpg
import sys
from pathlib import Path

# Add the app directory to Python path
sys.path.append(str(Path(__file__).parent))

from app.config import settings

async def add_role_permissions():
    """Add role management permissions to the database."""
    
    print("🔐 Adding Role Management Permissions")
    print("=" * 40)
    
    try:
        # Connect to database
        db_url = settings.DATABASE_URL.replace("postgresql+asyncpg://", "postgresql://")
        conn = await asyncpg.connect(db_url)
        print("✅ Database connection successful")
        
        # First, update the constraint to allow new resource types
        print("🔧 Updating database constraints...")

        # Drop the old constraint
        await conn.execute("ALTER TABLE permissions DROP CONSTRAINT IF EXISTS chk_permissions_valid_resource")

        # Add new constraint with additional resource types
        await conn.execute("""
            ALTER TABLE permissions ADD CONSTRAINT chk_permissions_valid_resource
            CHECK (resource IN ('users', 'products', 'shopping_lists', 'system', 'roles', 'permissions'))
        """)

        # Also update action constraint to include new actions
        await conn.execute("ALTER TABLE permissions DROP CONSTRAINT IF EXISTS chk_permissions_valid_action")
        await conn.execute("""
            ALTER TABLE permissions ADD CONSTRAINT chk_permissions_valid_action
            CHECK (action IN ('create', 'read', 'update', 'delete', 'manage', 'admin', 'assign', 'search', 'revoke'))
        """)

        print("✅ Database constraints updated")

        # Define role management permissions (using only allowed resources)
        role_permissions = [
            ("roles.read", "roles", "read", "View roles and role information"),
            ("roles.create", "roles", "create", "Create new roles"),
            ("roles.update", "roles", "update", "Update existing roles"),
            ("roles.delete", "roles", "delete", "Delete roles"),
            ("roles.manage", "roles", "manage", "Full role management access"),
            ("permissions.read", "permissions", "read", "View permissions"),
            ("permissions.create", "permissions", "create", "Create new permissions"),
            ("permissions.update", "permissions", "update", "Update existing permissions"),
            ("permissions.delete", "permissions", "delete", "Delete permissions"),
            ("permissions.manage", "permissions", "manage", "Full permission management access"),
        ]
        
        # Check existing permissions
        existing_perms = await conn.fetch("SELECT name FROM permissions")
        existing_names = {perm['name'] for perm in existing_perms}
        
        print(f"✅ Found {len(existing_names)} existing permissions")
        
        # Add missing permissions
        added_count = 0
        for name, resource, action, description in role_permissions:
            if name not in existing_names:
                # Generate UUID for permission
                import uuid
                perm_id = str(uuid.uuid4())
                
                await conn.execute("""
                    INSERT INTO permissions (id, name, resource, action, description, is_system_permission)
                    VALUES ($1, $2, $3, $4, $5, TRUE)
                """, perm_id, name, resource, action, description)
                
                print(f"✅ Added permission: {name}")
                added_count += 1
            else:
                print(f"ℹ️  Permission already exists: {name}")
        
        print(f"\n✅ Added {added_count} new permissions")
        
        # Now assign role management permissions to superadmin role
        print("\n🔧 Assigning permissions to superadmin role...")
        
        # Get superadmin role ID
        superadmin_role = await conn.fetchrow("SELECT id FROM roles WHERE name = 'superadmin'")
        if not superadmin_role:
            print("❌ Superadmin role not found")
            return False
        
        superadmin_role_id = superadmin_role['id']
        
        # Get all role management permission IDs
        role_mgmt_perms = await conn.fetch("""
            SELECT id, name FROM permissions
            WHERE name LIKE 'roles.%' OR name LIKE 'permissions.%'
        """)
        
        # Assign permissions to superadmin role
        assigned_count = 0
        for perm in role_mgmt_perms:
            perm_id = perm['id']
            perm_name = perm['name']
            
            # Check if assignment already exists
            existing = await conn.fetchval("""
                SELECT EXISTS(
                    SELECT 1 FROM role_permissions 
                    WHERE role_id = $1 AND permission_id = $2 AND is_active = TRUE
                )
            """, superadmin_role_id, perm_id)
            
            if not existing:
                # Create assignment
                import uuid
                assignment_id = str(uuid.uuid4())
                
                await conn.execute("""
                    INSERT INTO role_permissions (id, role_id, permission_id, granted_at)
                    VALUES ($1, $2, $3, NOW())
                """, assignment_id, superadmin_role_id, perm_id)
                
                print(f"✅ Assigned {perm_name} to superadmin")
                assigned_count += 1
            else:
                print(f"ℹ️  Permission already assigned: {perm_name}")
        
        print(f"\n✅ Assigned {assigned_count} new permissions to superadmin role")
        
        # Optionally assign basic role reading to user role
        print("\n👤 Assigning basic role reading to user role...")
        
        user_role = await conn.fetchrow("SELECT id FROM roles WHERE name = 'user'")
        if user_role:
            user_role_id = user_role['id']
            
            # Assign roles.read permission to user role
            roles_read_perm = await conn.fetchrow("SELECT id FROM permissions WHERE name = 'roles.read'")
            if roles_read_perm:
                perm_id = roles_read_perm['id']
                
                # Check if assignment already exists
                existing = await conn.fetchval("""
                    SELECT EXISTS(
                        SELECT 1 FROM role_permissions 
                        WHERE role_id = $1 AND permission_id = $2 AND is_active = TRUE
                    )
                """, user_role_id, perm_id)
                
                if not existing:
                    import uuid
                    assignment_id = str(uuid.uuid4())
                    
                    await conn.execute("""
                        INSERT INTO role_permissions (id, role_id, permission_id, granted_at)
                        VALUES ($1, $2, $3, NOW())
                    """, assignment_id, user_role_id, perm_id)
                    
                    print(f"✅ Assigned roles.read to user role")
                else:
                    print(f"ℹ️  roles.read already assigned to user role")
        
        # Verify the changes
        print("\n🔍 Verifying changes...")
        
        total_perms = await conn.fetchval("SELECT COUNT(*) FROM permissions")
        role_mgmt_perms_count = await conn.fetchval("""
            SELECT COUNT(*) FROM permissions
            WHERE name LIKE 'roles.%' OR name LIKE 'permissions.%'
        """)
        
        superadmin_perms_count = await conn.fetchval("""
            SELECT COUNT(*) FROM role_permissions rp
            JOIN permissions p ON rp.permission_id = p.id
            WHERE rp.role_id = $1 AND rp.is_active = TRUE
            AND (p.name LIKE 'roles.%' OR p.name LIKE 'permissions.%')
        """, superadmin_role_id)
        
        print(f"✅ Total permissions in database: {total_perms}")
        print(f"✅ Role management permissions: {role_mgmt_perms_count}")
        print(f"✅ Superadmin role management permissions: {superadmin_perms_count}")
        
        print("\n🎉 Role Management Permissions Added Successfully!")
        print("=" * 40)
        
        return True
        
    except Exception as e:
        print(f"\n❌ Failed to add role permissions: {e}")
        import traceback
        traceback.print_exc()
        return False
        
    finally:
        if 'conn' in locals():
            await conn.close()
            print("\n📡 Database connection closed")

if __name__ == "__main__":
    success = asyncio.run(add_role_permissions())
    sys.exit(0 if success else 1)

#!/usr/bin/env python3
"""
Test script for secure logout functionality with token blacklisting.
"""

import asyncio
import aiohttp
import json
from datetime import datetime

async def test_secure_logout():
    """Test secure logout functionality with token blacklisting."""
    print("🔐 Testing Secure Logout with Token Blacklisting")
    print("=" * 60)
    
    base_url = "http://localhost:8000"
    
    async with aiohttp.ClientSession() as session:
        # Test 1: Register or login to get a token
        print("\n1. Getting authentication token...")
        login_data = {
            "email": "<EMAIL>",
            "password": "SuperYzn123!"
        }
        
        access_token = None
        try:
            async with session.post(f"{base_url}/api/v1/auth/login", json=login_data) as response:
                if response.status == 200:
                    response_data = await response.json()
                    access_token = response_data.get("access_token")
                    print(f"✅ Login successful, got token: {access_token[:20]}...")
                else:
                    print(f"❌ Login failed with status {response.status}")
                    print(await response.text())
                    return False
        except Exception as e:
            print(f"❌ Login error: {e}")
            return False
        
        if not access_token:
            print("❌ No access token received")
            return False
        
        # Test 2: Use token to access protected endpoint
        print("\n2. Testing token access to protected endpoint...")
        headers = {"Authorization": f"Bearer {access_token}"}
        
        try:
            async with session.get(f"{base_url}/api/v1/shopping-lists/", headers=headers) as response:
                if response.status == 200:
                    print("✅ Token works - can access protected endpoint")
                else:
                    print(f"❌ Token access failed with status {response.status}")
                    print(await response.text())
        except Exception as e:
            print(f"❌ Protected endpoint access error: {e}")
        
        # Test 3: Logout (blacklist token)
        print("\n3. Testing secure logout...")
        try:
            async with session.post(f"{base_url}/api/v1/auth/logout", headers=headers) as response:
                if response.status == 200:
                    response_data = await response.json()
                    print(f"✅ Logout successful: {response_data.get('message', 'No message')}")
                else:
                    print(f"❌ Logout failed with status {response.status}")
                    print(await response.text())
                    return False
        except Exception as e:
            print(f"❌ Logout error: {e}")
            return False
        
        # Test 4: Try to use token after logout (should fail)
        print("\n4. Testing token access after logout (should fail)...")
        try:
            async with session.get(f"{base_url}/api/v1/shopping-lists/", headers=headers) as response:
                if response.status == 401:
                    print("✅ Token correctly rejected after logout")
                elif response.status == 200:
                    print("❌ Token still works after logout (blacklist not working)")
                    return False
                else:
                    print(f"⚠️ Unexpected status after logout: {response.status}")
                    print(await response.text())
        except Exception as e:
            print(f"❌ Post-logout access test error: {e}")
        
        # Test 5: Try to logout again with blacklisted token
        print("\n5. Testing logout with already blacklisted token...")
        try:
            async with session.post(f"{base_url}/api/v1/auth/logout", headers=headers) as response:
                if response.status == 401:
                    print("✅ Logout correctly rejected with blacklisted token")
                elif response.status == 200:
                    print("⚠️ Logout succeeded with blacklisted token (may be expected)")
                else:
                    print(f"⚠️ Unexpected logout status with blacklisted token: {response.status}")
                    print(await response.text())
        except Exception as e:
            print(f"❌ Second logout test error: {e}")
        
        # Test 6: Get new token and verify it works
        print("\n6. Getting new token after logout...")
        try:
            async with session.post(f"{base_url}/api/v1/auth/login", json=login_data) as response:
                if response.status == 200:
                    response_data = await response.json()
                    new_token = response_data.get("access_token")
                    print(f"✅ New login successful, got token: {new_token[:20]}...")
                    
                    # Test new token
                    new_headers = {"Authorization": f"Bearer {new_token}"}
                    async with session.get(f"{base_url}/api/v1/shopping-lists/", headers=new_headers) as test_response:
                        if test_response.status == 200:
                            print("✅ New token works correctly")
                        else:
                            print(f"❌ New token failed with status {test_response.status}")
                else:
                    print(f"❌ New login failed with status {response.status}")
        except Exception as e:
            print(f"❌ New login error: {e}")
    
    print("\n✅ Secure logout testing completed!")
    return True

async def test_logout_without_token():
    """Test logout endpoint without providing a token."""
    print("\n🔍 Testing logout without authentication token...")
    
    base_url = "http://localhost:8000"
    
    async with aiohttp.ClientSession() as session:
        try:
            async with session.post(f"{base_url}/api/v1/auth/logout") as response:
                if response.status == 401:
                    print("✅ Logout correctly requires authentication")
                else:
                    print(f"❌ Expected 401, got {response.status}")
                    print(await response.text())
        except Exception as e:
            print(f"❌ Logout without token test error: {e}")

async def test_logout_with_invalid_token():
    """Test logout endpoint with invalid token."""
    print("\n🔍 Testing logout with invalid token...")
    
    base_url = "http://localhost:8000"
    invalid_token = "invalid.jwt.token"
    headers = {"Authorization": f"Bearer {invalid_token}"}
    
    async with aiohttp.ClientSession() as session:
        try:
            async with session.post(f"{base_url}/api/v1/auth/logout", headers=headers) as response:
                if response.status == 401:
                    print("✅ Logout correctly rejects invalid token")
                elif response.status == 200:
                    print("⚠️ Logout succeeded with invalid token (may handle gracefully)")
                else:
                    print(f"⚠️ Unexpected status with invalid token: {response.status}")
                    print(await response.text())
        except Exception as e:
            print(f"❌ Invalid token logout test error: {e}")

async def main():
    """Run all secure logout tests."""
    print("🧪 Secure Logout Test Suite")
    print("=" * 60)
    
    tests = [
        ("Secure Logout Flow", test_secure_logout),
        ("Logout Without Token", test_logout_without_token),
        ("Logout With Invalid Token", test_logout_with_invalid_token),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n🔍 Running: {test_name}")
        try:
            if await test_func():
                passed += 1
                print(f"✅ {test_name}: PASSED")
            else:
                print(f"❌ {test_name}: FAILED")
        except Exception as e:
            print(f"💥 {test_name}: ERROR - {e}")
    
    print("\n" + "=" * 60)
    print("📊 SECURE LOGOUT TEST SUMMARY")
    print("=" * 60)
    print(f"✅ Passed: {passed}/{total}")
    print(f"❌ Failed: {total - passed}/{total}")
    
    if passed >= total - 1:  # Allow 1 failure for edge cases
        print("\n🎉 Secure logout tests passed!")
        return True
    else:
        print("\n⚠️ Some tests failed. Please review the results above.")
        return False

if __name__ == "__main__":
    success = asyncio.run(main())
    exit(0 if success else 1)

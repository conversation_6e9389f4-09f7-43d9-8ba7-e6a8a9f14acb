#!/usr/bin/env python3
"""
Comprehensive test script for authentication flows with the new RBAC system.
Tests user registration, login, token refresh, role validation, and permission checking.
"""

import asyncio
import aiohttp
import json
import time
from app.config import settings


async def test_user_registration_flow():
    """Test user registration with automatic role assignment."""
    print("🔐 Testing User Registration Flow")
    print("=" * 50)
    
    base_url = "http://localhost:8000"
    
    async with aiohttp.ClientSession() as session:
        # Test 1: Register new user
        print("\n🔍 Test 1: Register new user...")
        
        timestamp = int(time.time())
        test_user_data = {
            "email": f"auth_test_{timestamp}@profident.com",
            "password": "AuthTest123!",
            "full_name": "Auth Test User"
        }
        
        async with session.post(f"{base_url}/api/v1/auth/register", json=test_user_data) as response:
            if response.status == 201:
                registration_data = await response.json()
                user_id = registration_data["user"]["id"]
                access_token = registration_data["token"]["access_token"]
                
                print(f"✅ User registered successfully: {test_user_data['email']}")
                print(f"   - User ID: {user_id}")
                print(f"   - Roles: {registration_data['user'].get('roles', [])}")
                print(f"   - Permissions: {len(registration_data['user'].get('permissions', []))} permissions")
                
                # Verify default role assignment
                if 'user' in registration_data['user'].get('roles', []):
                    print("✅ Default 'user' role assigned automatically")
                else:
                    print("⚠️  Default 'user' role not assigned")
                
                return {
                    "user_id": user_id,
                    "email": test_user_data['email'],
                    "password": test_user_data['password'],
                    "access_token": access_token
                }
            else:
                print(f"❌ Registration failed: {response.status}")
                text = await response.text()
                print(f"Response: {text}")
                return None


async def test_user_login_flow(user_credentials):
    """Test user login with role validation."""
    print("\n🔐 Testing User Login Flow")
    print("=" * 50)
    
    base_url = "http://localhost:8000"
    
    async with aiohttp.ClientSession() as session:
        # Test 1: Login with correct credentials
        print("\n🔍 Test 1: Login with correct credentials...")
        
        login_data = {
            "email": user_credentials["email"],
            "password": user_credentials["password"]
        }
        
        async with session.post(f"{base_url}/api/v1/auth/login", json=login_data) as response:
            if response.status == 200:
                login_response = await response.json()
                
                print(f"✅ Login successful: {user_credentials['email']}")
                print(f"   - Roles: {login_response['user'].get('roles', [])}")
                print(f"   - Permissions: {len(login_response['user'].get('permissions', []))} permissions")
                print(f"   - Is Superuser: {login_response['user'].get('is_superuser', False)}")
                
                # Verify role validation during login
                if login_response['user'].get('roles'):
                    print("✅ Role validation passed during login")
                else:
                    print("⚠️  No roles found for user")
                
                return login_response["token"]["access_token"]
            else:
                print(f"❌ Login failed: {response.status}")
                text = await response.text()
                print(f"Response: {text}")
                return None
        
        # Test 2: Login with incorrect password
        print("\n🔍 Test 2: Login with incorrect password...")
        
        wrong_login_data = {
            "email": user_credentials["email"],
            "password": "WrongPassword123!"
        }
        
        async with session.post(f"{base_url}/api/v1/auth/login", json=wrong_login_data) as response:
            if response.status == 401:
                print("✅ Login correctly rejected with wrong password")
            else:
                print(f"⚠️  Expected 401, got {response.status}")
        
        # Test 3: Login with non-existent user
        print("\n🔍 Test 3: Login with non-existent user...")
        
        nonexistent_login_data = {
            "email": "<EMAIL>",
            "password": "SomePassword123!"
        }
        
        async with session.post(f"{base_url}/api/v1/auth/login", json=nonexistent_login_data) as response:
            if response.status == 401:
                print("✅ Login correctly rejected for non-existent user")
            else:
                print(f"⚠️  Expected 401, got {response.status}")


async def test_token_validation_and_refresh(access_token):
    """Test token validation and refresh functionality."""
    print("\n🔐 Testing Token Validation and Refresh")
    print("=" * 50)
    
    base_url = "http://localhost:8000"
    
    async with aiohttp.ClientSession() as session:
        # Test 1: Access protected endpoint with valid token
        print("\n🔍 Test 1: Access protected endpoint with valid token...")
        
        headers = {"Authorization": f"Bearer {access_token}"}
        
        async with session.get(f"{base_url}/api/v1/shopping-lists/", headers=headers) as response:
            if response.status == 200:
                print("✅ Protected endpoint access successful with valid token")
            else:
                print(f"❌ Protected endpoint access failed: {response.status}")
        
        # Test 2: Access protected endpoint without token
        print("\n🔍 Test 2: Access protected endpoint without token...")
        
        async with session.get(f"{base_url}/api/v1/shopping-lists/") as response:
            if response.status == 401:
                print("✅ Protected endpoint correctly rejected request without token")
            else:
                print(f"⚠️  Expected 401, got {response.status}")
        
        # Test 3: Access protected endpoint with invalid token
        print("\n🔍 Test 3: Access protected endpoint with invalid token...")
        
        invalid_headers = {"Authorization": "Bearer invalid_token_here"}
        
        async with session.get(f"{base_url}/api/v1/shopping-lists/", headers=invalid_headers) as response:
            if response.status == 401:
                print("✅ Protected endpoint correctly rejected invalid token")
            else:
                print(f"⚠️  Expected 401, got {response.status}")


async def test_permission_based_access():
    """Test permission-based access control."""
    print("\n🔐 Testing Permission-Based Access Control")
    print("=" * 50)
    
    base_url = "http://localhost:8000"
    
    async with aiohttp.ClientSession() as session:
        # Login as regular user
        print("\n🔍 Step 1: Login as regular user...")
        
        timestamp = int(time.time())
        regular_user_data = {
            "email": f"permission_test_{timestamp}@profident.com",
            "password": "PermTest123!",
            "full_name": "Permission Test User"
        }
        
        # Register regular user
        async with session.post(f"{base_url}/api/v1/auth/register", json=regular_user_data) as response:
            if response.status == 201:
                reg_data = await response.json()
                regular_token = reg_data["token"]["access_token"]
                print(f"✅ Regular user registered: {regular_user_data['email']}")
            else:
                print(f"❌ Failed to register regular user: {response.status}")
                return
        
        # Test 1: Regular user accessing search (should work)
        print("\n🔍 Test 1: Regular user accessing search...")
        
        headers = {"Authorization": f"Bearer {regular_token}"}
        
        async with session.get(f"{base_url}/api/v1/search/suggestions?q=test", headers=headers) as response:
            if response.status == 200:
                print("✅ Regular user can access search (has products.search permission)")
            else:
                print(f"❌ Regular user cannot access search: {response.status}")
        
        # Test 2: Regular user accessing admin endpoints (should fail)
        print("\n🔍 Test 2: Regular user accessing admin endpoints...")
        
        async with session.get(f"{base_url}/api/v1/admin/users", headers=headers) as response:
            if response.status == 403:
                print("✅ Regular user correctly blocked from admin endpoints")
            else:
                print(f"⚠️  Expected 403, got {response.status}")
        
        # Test 3: Superadmin accessing admin endpoints (should work)
        print("\n🔍 Test 3: Superadmin accessing admin endpoints...")
        
        # Login as superadmin
        superadmin_login = {
            "email": "<EMAIL>",
            "password": "SuperYzn123!"
        }
        
        async with session.post(f"{base_url}/api/v1/auth/login", json=superadmin_login) as response:
            if response.status == 200:
                superadmin_data = await response.json()
                superadmin_token = superadmin_data["token"]["access_token"]
                print("✅ Superadmin login successful")
            else:
                print(f"❌ Superadmin login failed: {response.status}")
                return
        
        superadmin_headers = {"Authorization": f"Bearer {superadmin_token}"}
        
        async with session.get(f"{base_url}/api/v1/admin/users", headers=superadmin_headers) as response:
            if response.status == 200:
                print("✅ Superadmin can access admin endpoints")
            else:
                print(f"❌ Superadmin cannot access admin endpoints: {response.status}")


async def test_role_assignment_flows():
    """Test role assignment and permission inheritance."""
    print("\n🔐 Testing Role Assignment Flows")
    print("=" * 50)
    
    base_url = "http://localhost:8000"
    
    async with aiohttp.ClientSession() as session:
        # Login as superadmin for role management
        superadmin_login = {
            "email": "<EMAIL>",
            "password": "SuperYzn123!"
        }
        
        async with session.post(f"{base_url}/api/v1/auth/login", json=superadmin_login) as response:
            if response.status == 200:
                superadmin_data = await response.json()
                superadmin_token = superadmin_data["token"]["access_token"]
                print("✅ Superadmin authenticated for role management")
            else:
                print(f"❌ Superadmin login failed: {response.status}")
                return
        
        headers = {"Authorization": f"Bearer {superadmin_token}"}
        
        # Create test user for role assignment
        timestamp = int(time.time())
        test_user_data = {
            "email": f"role_assign_test_{timestamp}@profident.com",
            "password": "RoleTest123!",
            "full_name": "Role Assignment Test User"
        }
        
        async with session.post(f"{base_url}/api/v1/auth/register", json=test_user_data) as response:
            if response.status == 201:
                user_data = await response.json()
                test_user_id = user_data["user"]["id"]
                print(f"✅ Test user created: {test_user_data['email']}")
            else:
                print(f"❌ Failed to create test user: {response.status}")
                return
        
        # Test 1: Check initial user roles
        print(f"\n🔍 Test 1: Check initial user roles...")
        
        async with session.get(f"{base_url}/api/v1/admin/users/{test_user_id}", headers=headers) as response:
            if response.status == 200:
                user_details = await response.json()
                initial_roles = user_details.get('roles', [])
                print(f"✅ Initial roles: {initial_roles}")
                
                if 'user' in initial_roles:
                    print("✅ Default 'user' role correctly assigned")
                else:
                    print("⚠️  Default 'user' role not found")
            else:
                print(f"❌ Failed to get user details: {response.status}")


async def main():
    """Run all authentication flow tests."""
    print("🚀 Starting Comprehensive Authentication Flow Tests")
    print("=" * 60)
    print("Make sure the FastAPI server is running on port 8000.")
    print()
    
    try:
        # Test 1: User Registration Flow
        user_credentials = await test_user_registration_flow()
        if not user_credentials:
            print("❌ Registration test failed, stopping tests")
            return False
        
        # Test 2: User Login Flow
        access_token = await test_user_login_flow(user_credentials)
        if not access_token:
            print("❌ Login test failed, stopping tests")
            return False
        
        # Test 3: Token Validation and Refresh
        await test_token_validation_and_refresh(access_token)
        
        # Test 4: Permission-Based Access Control
        await test_permission_based_access()
        
        # Test 5: Role Assignment Flows
        await test_role_assignment_flows()
        
        print("\n🎉 All Authentication Flow Tests Completed!")
        print("=" * 60)
        print("✅ User registration with role assignment working")
        print("✅ User login with role validation working")
        print("✅ Token validation and protection working")
        print("✅ Permission-based access control working")
        print("✅ Role assignment flows working")
        
        return True
        
    except Exception as e:
        print(f"\n❌ Test execution failed: {str(e)}")
        return False


if __name__ == "__main__":
    success = asyncio.run(main())
    
    if success:
        print("\n🎉 All authentication flow tests passed!")
        exit(0)
    else:
        print("\n❌ Some authentication flow tests failed!")
        exit(1)

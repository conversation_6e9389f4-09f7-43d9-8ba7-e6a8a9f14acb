"""
Authentication schemas for request/response validation
"""

from datetime import datetime
from typing import Optional
from pydantic import BaseModel, EmailStr, validator, Field, root_validator


class UserBase(BaseModel):
    """Base user schema."""
    email: EmailStr = Field(..., max_length=254, description="User email address")
    full_name: Optional[str] = Field(None, max_length=100, description="User full name")
    is_active: bool = True




class UserCreate(UserBase):
    """Schema for user creation."""
    password: str = Field(..., min_length=8, max_length=128, description="User password")




class UserUpdate(BaseModel):
    """Schema for user updates."""
    email: Optional[EmailStr] = None
    full_name: Optional[str] = None
    is_active: Optional[bool] = None


class UserResponse(UserBase):
    """Schema for user response."""
    id: str
    is_superuser: bool = False
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
    
    class Config:
        from_attributes = True


class UserLogin(BaseModel):
    """Schema for user login."""
    email: EmailStr = Field(..., max_length=254, description="User email address")
    password: str = Field(..., min_length=1, max_length=128, description="User password")

    @validator('email')
    def validate_email_format(cls, v):
        """Basic email validation for login."""
        # Simple email normalization
        return v.lower().strip()

    @validator('password')
    def validate_password_basic(cls, v):
        """Basic password validation for login."""
        if not v:
            raise ValueError("Password is required")

        if len(v) > 128:
            raise ValueError("Password exceeds maximum length")

        return v

    @root_validator(skip_on_failure=True)
    def validate_login_data(cls, values):
        """Basic validation of login data."""
        # Simple validation without complex dependencies
        email = values.get('email')
        password = values.get('password')

        if email and password:
            # Basic validation only - just normalize email
            values['email'] = email.lower().strip()

        return values


class PasswordChange(BaseModel):
    """Schema for password change."""
    current_password: str = Field(..., min_length=1)
    new_password: str = Field(..., min_length=8, max_length=100)
    
    @validator('new_password')
    def validate_new_password(cls, v):
        """Validate new password strength."""
        from ..utils.security import validate_password_strength
        
        is_valid, errors = validate_password_strength(v)
        if not is_valid:
            raise ValueError(f"Password validation failed: {'; '.join(errors)}")
        return v


class PasswordReset(BaseModel):
    """Schema for password reset."""
    email: EmailStr


class PasswordResetConfirm(BaseModel):
    """Schema for password reset confirmation."""
    token: str
    new_password: str = Field(..., min_length=8, max_length=100)
    
    @validator('new_password')
    def validate_new_password(cls, v):
        """Validate new password strength."""
        from ..utils.security import validate_password_strength
        
        is_valid, errors = validate_password_strength(v)
        if not is_valid:
            raise ValueError(f"Password validation failed: {'; '.join(errors)}")
        return v


class Token(BaseModel):
    """Schema for token response."""
    access_token: str
    refresh_token: str
    token_type: str = "bearer"
    expires_in: int  # seconds


class TokenData(BaseModel):
    """Schema for token data."""
    user_id: Optional[str] = None
    email: Optional[str] = None


class RefreshToken(BaseModel):
    """Schema for refresh token request."""
    refresh_token: str


class AuthResponse(BaseModel):
    """Schema for authentication response."""
    user: UserResponse
    token: Token


class EmailVerification(BaseModel):
    """Schema for email verification."""
    token: str


class UserList(BaseModel):
    """Schema for user list response."""
    users: list[UserResponse]
    total: int
    page: int
    per_page: int
    has_next: bool
    has_prev: bool


class APIResponse(BaseModel):
    """Generic API response schema."""
    success: bool = True
    message: str
    data: Optional[dict] = None


class ErrorResponse(BaseModel):
    """Error response schema."""
    success: bool = False
    message: str
    errors: Optional[list[str]] = None
    error_code: Optional[str] = None

#!/usr/bin/env python3
"""
Test JSON endpoint functionality after middleware fixes.
"""

import asyncio
import httpx
import time


async def test_json_endpoints():
    """Test JSON request body parsing for authentication endpoints."""
    print("🔧 Testing JSON Endpoint Functionality After Middleware Fixes")
    print("=" * 70)
    
    base_url = "http://localhost:8000"
    
    try:
        async with httpx.AsyncClient(timeout=30.0) as client:
            
            # Test 1: CSRF Token Generation
            print("1. Testing CSRF Token Generation...")
            start_time = time.time()
            response = await client.get(f"{base_url}/api/v1/auth/csrf-token")
            end_time = time.time()
            
            if response.status_code == 200:
                data = response.json()
                csrf_token = data.get('data', {}).get('csrf_token')
                if not csrf_token:
                    print(f"   ❌ CSRF token not found in response: {data}")
                    return False
                print(f"   ✅ CSRF token generated: {(end_time - start_time) * 1000:.2f}ms")
                print(f"   Token: {csrf_token[:20]}...")
            else:
                print(f"   ❌ CSRF token generation failed: {response.status_code}")
                return False
            
            # Test 2: Registration with JSON Body
            print("\n2. Testing Registration with JSON Body...")
            registration_data = {
                "email": f"test_{int(time.time())}@example.com",
                "password": "Test123!@#",
                "name": "Test User"
            }
            
            headers = {
                "Content-Type": "application/json"
                # "X-CSRF-Token": csrf_token  # Temporarily disabled for testing
            }
            
            start_time = time.time()
            response = await client.post(
                f"{base_url}/api/v1/auth/register",
                json=registration_data,
                headers=headers
            )
            end_time = time.time()
            
            if response.status_code == 201:
                data = response.json()
                print(f"   ✅ Registration successful: {(end_time - start_time) * 1000:.2f}ms")
                print(f"   User ID: {data.get('user', {}).get('id')}")
                user_email = registration_data["email"]
                user_password = registration_data["password"]
            else:
                print(f"   ❌ Registration failed: {response.status_code}")
                print(f"   Response: {response.text}")
                return False
            
            # Test 3: Login with JSON Body
            print("\n3. Testing Login with JSON Body...")
            login_data = {
                "email": user_email,
                "password": user_password
            }
            
            start_time = time.time()
            response = await client.post(
                f"{base_url}/api/v1/auth/login",
                json=login_data,
                headers=headers
            )
            end_time = time.time()
            
            if response.status_code == 200:
                data = response.json()
                access_token = data.get('access_token')
                print(f"   ✅ Login successful: {(end_time - start_time) * 1000:.2f}ms")
                print(f"   Token: {access_token[:20]}...")
            else:
                print(f"   ❌ Login failed: {response.status_code}")
                print(f"   Response: {response.text}")
                return False
            
            # Test 4: Authenticated Request
            print("\n4. Testing Authenticated Request...")
            auth_headers = {
                "Authorization": f"Bearer {access_token}",
                "Content-Type": "application/json"
            }
            
            start_time = time.time()
            response = await client.get(
                f"{base_url}/api/v1/shopping-lists/",
                headers=auth_headers
            )
            end_time = time.time()
            
            if response.status_code == 200:
                data = response.json()
                print(f"   ✅ Authenticated request successful: {(end_time - start_time) * 1000:.2f}ms")
                print(f"   Shopping lists: {len(data.get('shopping_lists', []))}")
            else:
                print(f"   ❌ Authenticated request failed: {response.status_code}")
                print(f"   Response: {response.text}")
                return False
            
            # Test 5: Search Functionality
            print("\n5. Testing Search Functionality...")
            start_time = time.time()
            response = await client.get(f"{base_url}/api/v1/search/suggestions?q=dental&limit=5")
            end_time = time.time()
            
            if response.status_code == 200:
                data = response.json()
                suggestions = data.get('suggestions', [])
                print(f"   ✅ Search suggestions: {(end_time - start_time) * 1000:.2f}ms")
                print(f"   Suggestions: {len(suggestions)}")
            else:
                print(f"   ❌ Search suggestions failed: {response.status_code}")
                return False
            
            # Test 6: Concurrent JSON Requests
            print("\n6. Testing Concurrent JSON Requests...")
            
            async def concurrent_login():
                async with httpx.AsyncClient(timeout=30.0) as concurrent_client:
                    start_time = time.time()
                    response = await concurrent_client.post(
                        f"{base_url}/api/v1/auth/login",
                        json=login_data,
                        headers=headers
                    )
                    end_time = time.time()
                    return response.status_code, (end_time - start_time) * 1000
            
            # Run 5 concurrent login requests
            tasks = [concurrent_login() for _ in range(5)]
            results = await asyncio.gather(*tasks)
            
            successful_requests = sum(1 for status, _ in results if status == 200)
            avg_time = sum(time for _, time in results) / len(results)
            
            print(f"   Concurrent requests: {successful_requests}/5 successful")
            print(f"   Average response time: {avg_time:.2f}ms")
            
            if successful_requests >= 4:  # Allow 1 failure due to rate limiting
                print(f"   ✅ Concurrent JSON requests working")
            else:
                print(f"   ❌ Concurrent JSON requests failing")
                return False
            
            print("\n🎉 JSON Endpoint Testing Completed!")
            print("=" * 70)
            print("✅ CSRF token generation working")
            print("✅ Registration with JSON body working")
            print("✅ Login with JSON body working")
            print("✅ Authenticated requests working")
            print("✅ Search functionality working")
            print("✅ Concurrent JSON requests working")
            
            return True
            
    except Exception as e:
        print(f"❌ JSON endpoint test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


async def main():
    """Run JSON endpoint tests."""
    success = await test_json_endpoints()
    
    if success:
        print("\n✅ All JSON endpoints are working correctly!")
    else:
        print("\n❌ JSON endpoint issues detected!")
        exit(1)


if __name__ == "__main__":
    asyncio.run(main())

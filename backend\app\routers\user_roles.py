"""
User Role Assignment API endpoints for RBAC system.
"""

from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, status, Query
from asyncpg import Connection

from ..database import get_db_connection
from ..dependencies import get_current_active_user
from ..models.user import User, UserRepository
from ..models.role import Role, RoleRepository
from ..schemas.rbac import (
    UserRoleAssignmentCreate,
    UserRoleAssignmentResponse,
    UserRoleAssignmentUpdate,
    UserRoleAssignmentList,
    UserWithRolesResponse
)

router = APIRouter(prefix="/user-roles", tags=["User Role Management"])


async def require_user_admin_permission(current_user: User = Depends(get_current_active_user)) -> User:
    """Dependency to require user management permissions."""
    if not current_user.has_permission("users.manage") and not current_user.is_superuser:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Insufficient permissions. User role management requires 'users.manage' permission."
        )
    return current_user


@router.post("/assign", response_model=UserRoleAssignmentResponse, status_code=status.HTTP_201_CREATED)
async def assign_role_to_user(
    assignment: UserRoleAssignmentCreate,
    current_user: User = Depends(require_user_admin_permission),
    conn: Connection = Depends(get_db_connection)
):
    """
    Assign a role to a user.
    
    Requires 'users.manage' permission or superuser status.
    """
    try:
        # Verify user exists
        user = await UserRepository.get_user_by_id(conn, assignment.user_id)
        if not user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"User with ID {assignment.user_id} not found"
            )

        # Verify role exists
        role = await RoleRepository.get_role_by_id(conn, assignment.role_id)
        if not role:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Role with ID {assignment.role_id} not found"
            )
        
        # Assign role to user
        user_role = await UserRepository.assign_role_to_user(
            conn=conn,
            user_id=assignment.user_id,
            role_id=assignment.role_id,
            assigned_by=current_user.id
        )
        
        return UserRoleAssignmentResponse(
            id=user_role.id,
            user_id=user_role.user_id,
            role_id=user_role.role_id,
            assigned_by=user_role.assigned_by,
            assigned_at=user_role.assigned_at,
            is_active=user_role.is_active,
            notes=user_role.notes,
            created_at=user_role.created_at,
            updated_at=user_role.updated_at
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to assign role to user: {str(e)}"
        )


@router.delete("/revoke/{user_id}/{role_id}", status_code=status.HTTP_204_NO_CONTENT)
async def revoke_role_from_user(
    user_id: str,
    role_id: str,
    current_user: User = Depends(require_user_admin_permission),
    conn: Connection = Depends(get_db_connection)
):
    """
    Revoke a role from a user.
    
    Requires 'users.manage' permission or superuser status.
    """
    try:
        # Verify user exists
        user = await UserRepository.get_user_by_id(conn, user_id)
        if not user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"User with ID {user_id} not found"
            )

        # Verify role exists
        role = await RoleRepository.get_role_by_id(conn, role_id)
        if not role:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Role with ID {role_id} not found"
            )
        
        # Revoke role from user
        success = await UserRepository.revoke_role_from_user(
            conn=conn,
            user_id=user_id,
            role_id=role_id
        )
        
        if not success:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"User {user_id} does not have role {role_id}"
            )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to revoke role from user: {str(e)}"
        )


@router.get("/user/{user_id}", response_model=UserWithRolesResponse)
async def get_user_roles(
    user_id: str,
    current_user: User = Depends(require_user_admin_permission),
    conn: Connection = Depends(get_db_connection)
):
    """
    Get all roles assigned to a specific user.
    
    Requires 'users.manage' permission or superuser status.
    """
    try:
        # Get user with roles
        user_with_roles = await UserRepository.get_user_with_roles_and_permissions(conn, user_id)
        if not user_with_roles:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"User with ID {user_id} not found"
            )
        
        return UserWithRolesResponse(
            id=user_with_roles.id,
            email=user_with_roles.email,
            full_name=user_with_roles.full_name,
            is_active=user_with_roles.is_active,
            is_superuser=user_with_roles.is_superuser,
            created_at=user_with_roles.created_at,
            updated_at=user_with_roles.updated_at,
            roles=user_with_roles.roles,
            permissions=list(user_with_roles.permissions)
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get user roles: {str(e)}"
        )


@router.get("/role/{role_id}/users", response_model=UserRoleAssignmentList)
async def get_role_users(
    role_id: str,
    skip: int = Query(0, ge=0, description="Number of records to skip"),
    limit: int = Query(20, ge=1, le=100, description="Number of records to return"),
    current_user: User = Depends(require_user_admin_permission),
    conn: Connection = Depends(get_db_connection)
):
    """
    Get all users assigned to a specific role.
    
    Requires 'users.manage' permission or superuser status.
    """
    try:
        # Verify role exists
        role = await RoleRepository.get_role_by_id(conn, role_id)
        if not role:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Role with ID {role_id} not found"
            )
        
        # Get users with this role
        assignments = await UserRepository.get_users_by_role(
            conn=conn,
            role_id=role_id,
            skip=skip,
            limit=limit
        )

        # Get total count
        total = await UserRepository.count_users_by_role(conn, role_id)

        # Convert assignments to response format
        assignment_responses = [
            UserRoleAssignmentResponse(**assignment.to_dict())
            for assignment in assignments
        ]

        return UserRoleAssignmentList(
            assignments=assignment_responses,
            total=total,
            skip=skip,
            limit=limit
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get role users: {str(e)}"
        )


@router.get("/assignments", response_model=UserRoleAssignmentList)
async def list_user_role_assignments(
    skip: int = Query(0, ge=0, description="Number of records to skip"),
    limit: int = Query(20, ge=1, le=100, description="Number of records to return"),
    user_id: Optional[str] = Query(None, description="Filter by user ID"),
    role_id: Optional[str] = Query(None, description="Filter by role ID"),
    is_active: Optional[bool] = Query(None, description="Filter by active status"),
    current_user: User = Depends(require_user_admin_permission),
    conn: Connection = Depends(get_db_connection)
):
    """
    List all user-role assignments with optional filtering.
    
    Requires 'users.manage' permission or superuser status.
    """
    try:
        # Get assignments with filtering
        assignments = await UserRepository.list_user_role_assignments(
            conn=conn,
            skip=skip,
            limit=limit,
            user_id=user_id,
            role_id=role_id,
            is_active=is_active
        )

        # Get total count with same filters
        total = await UserRepository.count_user_role_assignments(
            conn=conn,
            user_id=user_id,
            role_id=role_id,
            is_active=is_active
        )

        # Convert assignments to response format
        assignment_responses = [
            UserRoleAssignmentResponse(**assignment.to_dict())
            for assignment in assignments
        ]

        return UserRoleAssignmentList(
            assignments=assignment_responses,
            total=total,
            skip=skip,
            limit=limit
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to list user role assignments: {str(e)}"
        )


@router.put("/assignments/{assignment_id}", response_model=UserRoleAssignmentResponse)
async def update_user_role_assignment(
    assignment_id: str,
    update_data: UserRoleAssignmentUpdate,
    current_user: User = Depends(require_user_admin_permission),
    conn: Connection = Depends(get_db_connection)
):
    """
    Update a user-role assignment (e.g., add notes, change active status).
    
    Requires 'users.manage' permission or superuser status.
    """
    try:
        # Update assignment
        updated_assignment = await UserRepository.update_user_role_assignment(
            conn=conn,
            assignment_id=assignment_id,
            update_data=update_data.dict(exclude_unset=True)
        )
        
        if not updated_assignment:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"User role assignment with ID {assignment_id} not found"
            )
        
        return UserRoleAssignmentResponse(
            id=updated_assignment.id,
            user_id=updated_assignment.user_id,
            role_id=updated_assignment.role_id,
            assigned_by=updated_assignment.assigned_by,
            assigned_at=updated_assignment.assigned_at,
            is_active=updated_assignment.is_active,
            notes=updated_assignment.notes,
            created_at=updated_assignment.created_at,
            updated_at=updated_assignment.updated_at
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to update user role assignment: {str(e)}"
        )

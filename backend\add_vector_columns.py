#!/usr/bin/env python3
"""
Add Vector Columns Migration
Adds embedding columns to the products table for vector similarity search
"""

import asyncio
import sys
from pathlib import Path

# Add the app directory to Python path
sys.path.insert(0, str(Path(__file__).parent / "app"))

from app.database import DatabaseManager


async def add_vector_columns():
    """Add vector embedding columns to products table."""
    print("🔧 Adding vector embedding columns to products table...")
    
    db = DatabaseManager()
    await db.initialize()
    
    try:
        async with db.get_connection() as conn:
            # Add embedding column (vector type with 384 dimensions)
            print("   Adding embedding column...")
            await conn.execute("""
                ALTER TABLE products 
                ADD COLUMN IF NOT EXISTS embedding vector(384)
            """)
            
            # Add embedding_text column for storing the text used to generate embeddings
            print("   Adding embedding_text column...")
            await conn.execute("""
                ALTER TABLE products 
                ADD COLUMN IF NOT EXISTS embedding_text TEXT
            """)
            
            print("✅ Vector columns added successfully")
            
            # Check the table structure
            columns = await conn.fetch("""
                SELECT column_name, data_type 
                FROM information_schema.columns 
                WHERE table_name = 'products' 
                AND column_name IN ('embedding', 'embedding_text')
                ORDER BY column_name
            """)
            
            print("📊 Vector columns in products table:")
            for col in columns:
                print(f"   - {col['column_name']}: {col['data_type']}")
            
    except Exception as e:
        print(f"❌ Failed to add vector columns: {e}")
        return False
    
    finally:
        await db.close()
    
    return True


if __name__ == "__main__":
    success = asyncio.run(add_vector_columns())
    sys.exit(0 if success else 1)

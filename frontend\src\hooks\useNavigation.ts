import { useMemo } from 'react';
import { Home, Search, ShoppingCart, User, Settings, Users, Shield } from 'lucide-react';
import { useAdminPermissions } from './useRoles';

export interface NavigationItem {
  name: string;
  href: string;
  icon: any;
  permissions?: string[];
  roles?: string[];
  requireAll?: boolean;
}

/**
 * Hook that provides role-based navigation items
 * @returns Object containing different navigation arrays
 */
export const useNavigation = () => {
  const adminPermissions = useAdminPermissions();

  // Base navigation items available to all authenticated users
  const baseNavigation: NavigationItem[] = useMemo(() => [
    { name: 'Home', href: '/', icon: Home },
    { name: 'Search', href: '/search', icon: Search },
    { name: 'Shopping Lists', href: '/shopping-lists', icon: ShoppingCart },
    { name: 'Profile', href: '/profile', icon: User },
  ], []);

  // Admin navigation items
  const adminNavigation: NavigationItem[] = useMemo(() => [
    { 
      name: 'Admin Dashboard', 
      href: '/admin', 
      icon: Settings, 
      permissions: ['user:manage', 'role:manage', 'system:access'],
      requireAll: false
    },
    { 
      name: 'User Management', 
      href: '/admin/users', 
      icon: Users, 
      permissions: ['user:manage'] 
    },
    { 
      name: 'Role Management', 
      href: '/admin/roles', 
      icon: Shield, 
      permissions: ['role:manage'] 
    },
  ], []);

  // Filtered admin navigation based on user permissions
  const filteredAdminNavigation = useMemo(() => {
    if (!adminPermissions.canAccessAdmin) {
      return [];
    }
    
    return adminNavigation.filter(item => {
      if (!item.permissions) return true;
      
      if (adminPermissions.isSuperAdmin) return true;
      
      if (item.requireAll) {
        return item.permissions.every(permission => {
          switch (permission) {
            case 'user:manage':
              return adminPermissions.hasUserManagement;
            case 'role:manage':
              return adminPermissions.hasRoleManagement;
            case 'system:access':
              return adminPermissions.hasSystemAccess;
            default:
              return false;
          }
        });
      } else {
        return item.permissions.some(permission => {
          switch (permission) {
            case 'user:manage':
              return adminPermissions.hasUserManagement;
            case 'role:manage':
              return adminPermissions.hasRoleManagement;
            case 'system:access':
              return adminPermissions.hasSystemAccess;
            default:
              return false;
          }
        });
      }
    });
  }, [adminNavigation, adminPermissions]);

  // All navigation items combined
  const allNavigation = useMemo(() => [
    ...baseNavigation,
    ...filteredAdminNavigation,
  ], [baseNavigation, filteredAdminNavigation]);

  return {
    baseNavigation,
    adminNavigation: filteredAdminNavigation,
    allNavigation,
    hasAdminAccess: adminPermissions.canAccessAdmin,
    adminPermissions,
  };
};

/**
 * Hook for getting navigation breadcrumbs
 * @param currentPath - Current route path
 * @returns Breadcrumb items for the current path
 */
export const useBreadcrumbs = (currentPath: string) => {
  const { allNavigation } = useNavigation();

  return useMemo(() => {
    const breadcrumbs = [];
    
    // Always start with Home
    if (currentPath !== '/') {
      breadcrumbs.push({ name: 'Home', href: '/' });
    }
    
    // Find current page in navigation
    const currentItem = allNavigation.find(item => item.href === currentPath);
    if (currentItem && currentPath !== '/') {
      breadcrumbs.push(currentItem);
    }
    
    // Handle admin sub-pages
    if (currentPath.startsWith('/admin/')) {
      if (!breadcrumbs.find(item => item.href === '/admin')) {
        breadcrumbs.push({ name: 'Admin Dashboard', href: '/admin' });
      }
      
      if (currentPath === '/admin/users') {
        breadcrumbs.push({ name: 'User Management', href: '/admin/users' });
      } else if (currentPath === '/admin/roles') {
        breadcrumbs.push({ name: 'Role Management', href: '/admin/roles' });
      }
    }
    
    return breadcrumbs;
  }, [currentPath, allNavigation]);
};

/**
 * Hook for checking if a navigation item should be visible
 * @param item - Navigation item to check
 * @returns Boolean indicating if item should be visible
 */
export const useNavigationItemVisible = (item: NavigationItem) => {
  const adminPermissions = useAdminPermissions();

  return useMemo(() => {
    // If no permissions required, always visible
    if (!item.permissions && !item.roles) {
      return true;
    }

    // Superadmin can see everything
    if (adminPermissions.isSuperAdmin) {
      return true;
    }

    // Check permissions
    if (item.permissions) {
      const hasPermissions = item.requireAll
        ? item.permissions.every(permission => {
            switch (permission) {
              case 'user:manage':
                return adminPermissions.hasUserManagement;
              case 'role:manage':
                return adminPermissions.hasRoleManagement;
              case 'system:access':
                return adminPermissions.hasSystemAccess;
              default:
                return false;
            }
          })
        : item.permissions.some(permission => {
            switch (permission) {
              case 'user:manage':
                return adminPermissions.hasUserManagement;
              case 'role:manage':
                return adminPermissions.hasRoleManagement;
              case 'system:access':
                return adminPermissions.hasSystemAccess;
              default:
                return false;
            }
          });

      if (!hasPermissions) {
        return false;
      }
    }

    // TODO: Add role checking when needed
    // if (item.roles) {
    //   // Check roles logic here
    // }

    return true;
  }, [item, adminPermissions]);
};

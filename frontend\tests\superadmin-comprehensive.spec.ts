import { test, expect } from '@playwright/test';

test.describe('Superadmin Comprehensive Functionality Test', () => {
  test.beforeEach(async ({ page }) => {
    // Navigate to the application
    await page.goto('http://localhost:3000');
    
    // Wait for the page to load
    await page.waitForLoadState('networkidle');
  });

  test('Complete superadmin flow: login → search → shopping list functionality', async ({ page }) => {
    console.log('🧪 Starting comprehensive superadmin test...');
    
    // Step 1: Take initial screenshot
    await page.screenshot({ path: 'test-results/01-homepage.png', fullPage: true });
    
    // Step 2: Navigate to login page
    console.log('📝 Step 2: Navigating to login...');
    
    // Look for login button or link
    const loginButton = page.locator('a[href="/login"], button').filter({ hasText: /login|sign in/i }).first();
    
    if (await loginButton.isVisible()) {
      await loginButton.click();
      await page.waitForLoadState('networkidle');
    } else {
      // Try navigating directly to login page
      await page.goto('http://localhost:3000/login');
      await page.waitForLoadState('networkidle');
    }
    
    await page.screenshot({ path: 'test-results/02-login-page.png', fullPage: true });
    
    // Step 3: Login with superadmin credentials
    console.log('🔐 Step 3: Logging in as superadmin...');
    
    // Find email and password inputs
    const emailInput = page.locator('input[type="email"], input[name="email"]').first();
    const passwordInput = page.locator('input[type="password"], input[name="password"]').first();
    
    await expect(emailInput).toBeVisible();
    await expect(passwordInput).toBeVisible();
    
    // Enter superadmin credentials
    await emailInput.fill('<EMAIL>');
    await passwordInput.fill('admin123!@#');
    
    // Find and click login button
    const submitButton = page.locator('button[type="submit"], button').filter({ hasText: /login|sign in/i }).first();
    await submitButton.click();
    
    // Wait for login to complete
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(2000); // Give time for authentication
    
    await page.screenshot({ path: 'test-results/03-after-login.png', fullPage: true });
    
    // Step 4: Verify successful login
    console.log('✅ Step 4: Verifying successful login...');
    
    // Check for user indicator or dashboard elements
    const userIndicator = page.locator('[data-testid="user-menu"], .user-menu, button').filter({ hasText: /admin|profile|logout/i }).first();
    
    if (await userIndicator.isVisible()) {
      console.log('✅ Login successful - user indicator found');
    } else {
      // Check if we're on the homepage (successful login redirect)
      const currentUrl = page.url();
      if (currentUrl.includes('localhost:3000') && !currentUrl.includes('/login')) {
        console.log('✅ Login successful - redirected to homepage');
      } else {
        console.log('⚠️ Login status unclear, continuing with test...');
      }
    }
    
    // Step 5: Test search functionality
    console.log('🔍 Step 5: Testing search functionality...');
    
    // Find search input
    const searchInput = page.locator('input[placeholder*="Search"], input[type="search"], input[name="search"]').first();
    
    if (await searchInput.isVisible()) {
      console.log('✅ Search input found');
      
      // Type search query
      await searchInput.fill('dental');
      await page.waitForTimeout(1000); // Wait for suggestions
      
      await page.screenshot({ path: 'test-results/04-search-suggestions.png', fullPage: true });
      
      // Check for search suggestions
      const suggestions = page.locator('[data-testid="search-suggestions"], .suggestions, .dropdown').first();
      
      if (await suggestions.isVisible()) {
        console.log('✅ Search suggestions appeared');
        
        // Look for suggestion items
        const suggestionItems = suggestions.locator('div, li').filter({ hasText: /dental/i });
        const suggestionCount = await suggestionItems.count();
        
        console.log(`Found ${suggestionCount} search suggestions`);
        
        if (suggestionCount > 0) {
          // Test adding to shopping list from suggestions
          console.log('🛒 Step 6: Testing add to shopping list from suggestions...');
          
          // Look for add to list button in first suggestion
          const firstSuggestion = suggestionItems.first();
          const addButton = firstSuggestion.locator('button').filter({ hasText: /add|cart|\+/i }).first();
          
          if (await addButton.isVisible()) {
            await addButton.click();
            await page.waitForTimeout(1000); // Wait for animation
            
            console.log('✅ Clicked add to shopping list');
            await page.screenshot({ path: 'test-results/05-added-to-list.png', fullPage: true });
          } else {
            console.log('⚠️ Add to list button not found in suggestions');
          }
        }
      } else {
        console.log('⚠️ No search suggestions appeared');
      }
      
      // Test search results page
      await searchInput.press('Enter');
      await page.waitForLoadState('networkidle');
      
      await page.screenshot({ path: 'test-results/06-search-results.png', fullPage: true });
      
      // Check for search results
      const resultsContainer = page.locator('[data-testid="search-results"], .search-results, .results').first();
      
      if (await resultsContainer.isVisible()) {
        console.log('✅ Search results page loaded');
        
        const resultItems = resultsContainer.locator('div, article').filter({ hasText: /dental/i });
        const resultCount = await resultItems.count();
        console.log(`Found ${resultCount} search results`);
      } else {
        console.log('⚠️ Search results not found');
      }
      
    } else {
      console.log('⚠️ Search input not found');
    }
    
    // Step 7: Test shopping list functionality
    console.log('🛒 Step 7: Testing shopping list functionality...');
    
    // Look for shopping list icon/button
    const shoppingListIcon = page.locator('[data-testid="shopping-list-icon"], button').filter({ hasText: /shopping|cart|list/i }).first();
    
    if (await shoppingListIcon.isVisible()) {
      console.log('✅ Shopping list icon found');
      
      await shoppingListIcon.click();
      await page.waitForTimeout(1000);
      
      await page.screenshot({ path: 'test-results/07-shopping-list-dropdown.png', fullPage: true });
      
      // Check for shopping list dropdown or page
      const shoppingListContent = page.locator('[data-testid="shopping-list-dropdown"], .shopping-list-dropdown, .dropdown').first();
      
      if (await shoppingListContent.isVisible()) {
        console.log('✅ Shopping list dropdown opened');
        
        // Look for items in the list
        const listItems = shoppingListContent.locator('div, li').filter({ hasText: /dental/i });
        const itemCount = await listItems.count();
        
        console.log(`Found ${itemCount} items in shopping list`);
        
        if (itemCount > 0) {
          console.log('✅ Shopping list contains items');
        }
      } else {
        console.log('⚠️ Shopping list dropdown not visible');
      }
    } else {
      console.log('⚠️ Shopping list icon not found');
    }
    
    // Step 8: Test admin functionality (if available)
    console.log('👑 Step 8: Testing admin functionality...');
    
    // Look for admin menu or admin-specific features
    const adminMenu = page.locator('[data-testid="admin-menu"], .admin-menu, button').filter({ hasText: /admin|manage|settings/i }).first();
    
    if (await adminMenu.isVisible()) {
      console.log('✅ Admin menu found');
      await adminMenu.click();
      await page.waitForTimeout(1000);
      
      await page.screenshot({ path: 'test-results/08-admin-menu.png', fullPage: true });
    } else {
      console.log('⚠️ Admin menu not found (may not be implemented in UI)');
    }
    
    // Final screenshot
    await page.screenshot({ path: 'test-results/09-final-state.png', fullPage: true });
    
    console.log('🎉 Comprehensive superadmin test completed!');
  });

  test('Performance and network validation', async ({ page }) => {
    console.log('⚡ Testing performance and network requests...');
    
    // Track network requests
    const requests: any[] = [];
    page.on('request', request => {
      requests.push({
        url: request.url(),
        method: request.method(),
        timestamp: Date.now()
      });
    });
    
    // Navigate and measure load time
    const startTime = Date.now();
    await page.goto('http://localhost:3000');
    await page.waitForLoadState('networkidle');
    const loadTime = Date.now() - startTime;
    
    console.log(`Page load time: ${loadTime}ms`);
    expect(loadTime).toBeLessThan(10000); // 10 second max load time
    
    // Check for API requests
    const apiRequests = requests.filter(req => req.url.includes('localhost:8000'));
    console.log(`API requests made: ${apiRequests.length}`);
    
    // Test search performance
    const searchInput = page.locator('input[placeholder*="Search"], input[type="search"]').first();
    
    if (await searchInput.isVisible()) {
      await searchInput.fill('dental');
      
      const searchStart = Date.now();
      await page.waitForTimeout(2000); // Wait for search results
      const searchTime = Date.now() - searchStart;
      
      console.log(`Search response time: ${searchTime}ms`);
      expect(searchTime).toBeLessThan(5000); // 5 second max search time
    }
    
    console.log('✅ Performance test completed');
  });
});

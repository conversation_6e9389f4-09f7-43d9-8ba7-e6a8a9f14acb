#!/usr/bin/env python3
"""
Comprehensive API Testing Suite
Tests all API endpoints with authentication, validation, and performance
"""

import asyncio
import sys
import json
import time
from pathlib import Path
from typing import Dict, Any

# Add the app directory to Python path
sys.path.insert(0, str(Path(__file__).parent / "app"))

from app.database import DatabaseManager
from app.services.auth_service import AuthService
from app.schemas.auth import UserCreate, UserLogin


class APITester:
    """Comprehensive API testing suite."""
    
    def __init__(self):
        self.db = None
        self.test_user_token = None
        self.test_user_id = None
        self.test_shopping_list_id = None
        self.test_product_id = None
        
    async def initialize(self):
        """Initialize database and create test user."""
        self.db = DatabaseManager()
        await self.db.initialize()
        
        # Create test user for API testing
        async with self.db.get_connection() as conn:
            import uuid
            unique_email = f"api_test_{int(time.time())}@example.com"
            
            user_data = UserCreate(
                email=unique_email,
                password="TestPassword123!",
                full_name="API Test User"
            )
            
            user, tokens = await AuthService.register_user(conn, user_data)
            self.test_user_token = tokens['access_token']
            self.test_user_id = user.id
            
            # Get a test product ID
            self.test_product_id = await conn.fetchval("SELECT id FROM products LIMIT 1")
        
        print("✅ Test environment initialized")
    
    async def close(self):
        """Clean up test environment."""
        if self.db:
            async with self.db.get_connection() as conn:
                # Clean up test data
                if self.test_user_id:
                    await conn.execute("DELETE FROM users WHERE id = $1", self.test_user_id)
            await self.db.close()
        print("✅ Test environment cleaned up")
    
    async def test_auth_endpoints(self):
        """Test authentication endpoints."""
        print("\n🔐 Testing Authentication Endpoints...")
        
        from fastapi.testclient import TestClient
        from app.main import app
        
        with TestClient(app) as client:
            # Test health endpoint
            response = client.get("/api/v1/auth/health")
            assert response.status_code == 200, f"Auth health failed: {response.status_code}"
            print("  ✅ GET /api/v1/auth/health")
            
            # Test registration
            unique_email = f"test_reg_{int(time.time())}@example.com"
            reg_data = {
                "email": unique_email,
                "password": "TestPassword123!",
                "full_name": "Test Registration User"
            }
            response = client.post("/api/v1/auth/register", json=reg_data)
            assert response.status_code == 201, f"Registration failed: {response.status_code}"
            reg_result = response.json()
            assert "token" in reg_result and "access_token" in reg_result["token"], "No access token in registration response"
            print("  ✅ POST /api/v1/auth/register")
            
            # Test login
            login_data = {
                "email": unique_email,
                "password": "TestPassword123!"
            }
            response = client.post("/api/v1/auth/login", json=login_data)
            assert response.status_code == 200, f"Login failed: {response.status_code}"
            login_result = response.json()
            assert "token" in login_result and "access_token" in login_result["token"], "No access token in login response"
            test_token = login_result["token"]["access_token"]
            print("  ✅ POST /api/v1/auth/login")
            
            # Test protected endpoint
            headers = {"Authorization": f"Bearer {test_token}"}
            response = client.get("/api/v1/auth/me", headers=headers)
            assert response.status_code == 200, f"Get current user failed: {response.status_code}"
            user_data = response.json()
            assert user_data["email"] == unique_email, "Wrong user data returned"
            print("  ✅ GET /api/v1/auth/me")
            
            # Test token refresh
            refresh_data = {"refresh_token": login_result["token"]["refresh_token"]}
            response = client.post("/api/v1/auth/refresh", json=refresh_data)
            assert response.status_code == 200, f"Token refresh failed: {response.status_code}"
            refresh_result = response.json()
            assert "access_token" in refresh_result, "No access token in refresh response"
            print("  ✅ POST /api/v1/auth/refresh")
            
            # Clean up test user
            async with self.db.get_connection() as conn:
                await conn.execute("DELETE FROM users WHERE email = $1", unique_email)
    
    async def test_search_endpoints(self):
        """Test search endpoints."""
        print("\n🔍 Testing Search Endpoints...")
        
        from fastapi.testclient import TestClient
        from app.main import app
        
        with TestClient(app) as client:
            headers = {"Authorization": f"Bearer {self.test_user_token}"}
            
            # Test health endpoint
            response = client.get("/api/v1/search/health")
            assert response.status_code == 200, f"Search health failed: {response.status_code}"
            print("  ✅ GET /api/v1/search/health")
            
            # Test full-text search
            response = client.get("/api/v1/search/?q=dental", headers=headers)
            assert response.status_code == 200, f"Full-text search failed: {response.status_code}"
            search_result = response.json()
            assert "results" in search_result, "No results in search response"
            assert search_result["total"] > 0, "No search results found"
            print(f"  ✅ GET /api/v1/search/ (found {search_result['total']} results)")

            # Test search with filters
            response = client.get("/api/v1/search/?q=dental&seller=Benco&limit=5", headers=headers)
            assert response.status_code == 200, f"Filtered search failed: {response.status_code}"
            filtered_result = response.json()
            assert len(filtered_result["results"]) <= 5, "Limit not respected"
            print("  ✅ GET /api/v1/search/ (with filters)")
            
            # Test suggestions
            response = client.get("/api/v1/search/suggestions?q=dent", headers=headers)
            assert response.status_code == 200, f"Suggestions failed: {response.status_code}"
            suggestions_result = response.json()
            assert "suggestions" in suggestions_result, "No suggestions in response"
            print("  ✅ GET /api/v1/search/suggestions")
            
            # Test popular searches
            response = client.get("/api/v1/search/popular", headers=headers)
            assert response.status_code == 200, f"Popular searches failed: {response.status_code}"
            popular_result = response.json()
            assert "popular_searches" in popular_result, "No popular searches"
            print("  ✅ GET /api/v1/search/popular")
    
    async def test_products_endpoints(self):
        """Test products endpoints."""
        print("\n📦 Testing Products Endpoints...")
        
        from fastapi.testclient import TestClient
        from app.main import app
        
        with TestClient(app) as client:
            headers = {"Authorization": f"Bearer {self.test_user_token}"}
            
            # Test health endpoint
            response = client.get("/api/v1/products/health/")
            assert response.status_code == 200, f"Products health failed: {response.status_code}"
            print("  ✅ GET /api/v1/products/health/")

            # Test get product categories
            response = client.get("/api/v1/products/categories/?limit=10", headers=headers)
            assert response.status_code == 200, f"Get categories failed: {response.status_code}"
            categories_result = response.json()
            assert "categories" in categories_result, "No categories in response"
            print("  ✅ GET /api/v1/products/categories/")
            
            # Test get single product
            if self.test_product_id:
                response = client.get(f"/api/v1/products/{self.test_product_id}", headers=headers)
                assert response.status_code == 200, f"Get product failed: {response.status_code}"
                product_result = response.json()
                assert product_result["id"] == self.test_product_id, "Wrong product returned"
                print("  ✅ GET /api/v1/products/{id}")
            
            # Test get product brands
            response = client.get("/api/v1/products/brands/?limit=5", headers=headers)
            assert response.status_code == 200, f"Get brands failed: {response.status_code}"
            brands_result = response.json()
            assert "brands" in brands_result, "No brands in response"
            print("  ✅ GET /api/v1/products/brands/")

            # Test get product sellers
            response = client.get("/api/v1/products/sellers/?limit=5", headers=headers)
            assert response.status_code == 200, f"Get sellers failed: {response.status_code}"
            sellers_result = response.json()
            assert "sellers" in sellers_result, "No sellers in response"
            print("  ✅ GET /api/v1/products/sellers/")
    
    async def test_shopping_lists_endpoints(self):
        """Test shopping lists endpoints."""
        print("\n🛒 Testing Shopping Lists Endpoints...")
        
        from fastapi.testclient import TestClient
        from app.main import app
        
        with TestClient(app) as client:
            headers = {"Authorization": f"Bearer {self.test_user_token}"}
            
            # Test health endpoint
            response = client.get("/api/v1/shopping-lists/health/")
            assert response.status_code == 200, f"Shopping lists health failed: {response.status_code}"
            print("  ✅ GET /api/v1/shopping-lists/health/")
            
            # Test create shopping list
            list_data = {
                "name": "API Test Shopping List",
                "description": "Test list created by API tests"
            }
            response = client.post("/api/v1/shopping-lists/", json=list_data, headers=headers)
            assert response.status_code == 201, f"Create shopping list failed: {response.status_code}"
            list_result = response.json()
            assert list_result["name"] == list_data["name"], "Wrong list name"
            self.test_shopping_list_id = list_result["id"]
            print("  ✅ POST /api/v1/shopping-lists/")
            
            # Test get shopping lists
            response = client.get("/api/v1/shopping-lists/", headers=headers)
            assert response.status_code == 200, f"Get shopping lists failed: {response.status_code}"
            lists_result = response.json()
            assert "data" in lists_result and "items" in lists_result["data"], "No items in shopping lists response"
            print("  ✅ GET /api/v1/shopping-lists/")
            
            # Test get shopping list detail
            response = client.get(f"/api/v1/shopping-lists/{self.test_shopping_list_id}", headers=headers)
            assert response.status_code == 200, f"Get shopping list detail failed: {response.status_code}"
            detail_result = response.json()
            assert detail_result["id"] == self.test_shopping_list_id, "Wrong list returned"
            print("  ✅ GET /api/v1/shopping-lists/{id}")
            
            # Test add item to list
            if self.test_product_id:
                item_data = {
                    "product_id": self.test_product_id,
                    "quantity": 2,
                    "notes": "Test item"
                }
                response = client.post(f"/api/v1/shopping-lists/{self.test_shopping_list_id}/items", 
                                     json=item_data, headers=headers)
                assert response.status_code == 201, f"Add item failed: {response.status_code}"
                item_result = response.json()
                assert item_result["product_id"] == self.test_product_id, "Wrong product added"
                print("  ✅ POST /api/v1/shopping-lists/{id}/items")
            
            # Test update shopping list
            update_data = {
                "name": "Updated API Test List",
                "description": "Updated description"
            }
            response = client.put(f"/api/v1/shopping-lists/{self.test_shopping_list_id}", 
                                json=update_data, headers=headers)
            assert response.status_code == 200, f"Update shopping list failed: {response.status_code}"
            update_result = response.json()
            assert update_result["name"] == update_data["name"], "List not updated"
            print("  ✅ PUT /api/v1/shopping-lists/{id}")
            
            # Test get price analysis
            response = client.get(f"/api/v1/shopping-lists/{self.test_shopping_list_id}/analysis", headers=headers)
            assert response.status_code == 200, f"Price analysis failed: {response.status_code}"
            analysis_result = response.json()
            assert "analysis" in analysis_result, "No analysis in response"
            print("  ✅ GET /api/v1/shopping-lists/{id}/analysis")
    
    async def test_performance(self):
        """Test API performance."""
        print("\n⚡ Testing API Performance...")
        
        from fastapi.testclient import TestClient
        from app.main import app
        
        with TestClient(app) as client:
            headers = {"Authorization": f"Bearer {self.test_user_token}"}
            
            # Test search performance
            start_time = time.time()
            response = client.get("/api/v1/search/?q=dental&limit=50", headers=headers)
            search_time = (time.time() - start_time) * 1000
            assert response.status_code == 200, "Search performance test failed"
            print(f"  ✅ Search performance: {search_time:.2f}ms for 50 results")
            
            # Test categories list performance
            start_time = time.time()
            response = client.get("/api/v1/products/categories/?limit=100", headers=headers)
            categories_time = (time.time() - start_time) * 1000
            assert response.status_code == 200, "Categories list performance test failed"
            print(f"  ✅ Categories list performance: {categories_time:.2f}ms for 100 categories")
            
            # Test concurrent requests
            import concurrent.futures
            import requests
            
            def make_request():
                return requests.get(
                    "http://localhost:8000/api/v1/search/products?q=dental&limit=10",
                    headers=headers
                )
            
            # Note: This would require the server to be running
            print("  ℹ️ Concurrent request testing requires running server")


async def main():
    """Run comprehensive API tests."""
    print("🚀 ProfiDent Comprehensive API Testing")
    print("=" * 50)
    
    tester = APITester()
    
    try:
        await tester.initialize()
        
        # Run all test suites
        await tester.test_auth_endpoints()
        await tester.test_search_endpoints()
        await tester.test_products_endpoints()
        await tester.test_shopping_lists_endpoints()
        await tester.test_performance()
        
        print("\n" + "=" * 50)
        print("🎉 ALL API TESTS PASSED!")
        print("✅ Authentication endpoints working")
        print("✅ Search endpoints working")
        print("✅ Products endpoints working")
        print("✅ Shopping lists endpoints working")
        print("✅ Performance within acceptable limits")
        print("=" * 50)
        
        return True
        
    except Exception as e:
        print(f"\n❌ API tests failed: {e}")
        return False
        
    finally:
        await tester.close()


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)

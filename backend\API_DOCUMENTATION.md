# ProfiDent API Documentation

## Overview

The ProfiDent API is a comprehensive dental product search and management system built with FastAPI. It provides full-text search capabilities across 339,078+ dental products, user authentication, and shopping list management.

## Base URL

```
http://localhost:8000/api/v1
```

## Authentication

All protected endpoints require a Bearer token in the Authorization header:

```
Authorization: Bearer <access_token>
```

### Authentication Endpoints

#### POST /auth/register
Register a new user account.

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "password": "SecurePassword123!",
  "full_name": "<PERSON>"
}
```

**Response (201):**
```json
{
  "user": {
    "id": "uuid",
    "email": "<EMAIL>",
    "full_name": "<PERSON>",
    "is_active": true,
    "created_at": "2025-01-01T00:00:00Z"
  },
  "token": {
    "access_token": "jwt_token",
    "refresh_token": "refresh_token",
    "token_type": "bearer",
    "expires_in": 3600
  }
}
```

#### POST /auth/login
Authenticate user and get access tokens.

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "password": "SecurePassword123!"
}
```

**Response (200):** Same as register response.

#### GET /auth/me
Get current user information (requires authentication).

**Response (200):**
```json
{
  "id": "uuid",
  "email": "<EMAIL>",
  "full_name": "John Doe",
  "is_active": true,
  "created_at": "2025-01-01T00:00:00Z"
}
```

#### POST /auth/refresh
Refresh access token using refresh token.

**Request Body:**
```json
{
  "refresh_token": "refresh_token"
}
```

**Response (200):**
```json
{
  "access_token": "new_jwt_token",
  "refresh_token": "new_refresh_token",
  "token_type": "bearer",
  "expires_in": 3600
}
```

## Search Endpoints

### GET /search/
Full-text search across all products.

**Query Parameters:**
- `q` (required): Search query
- `limit` (optional, default=20): Maximum results (1-100)
- `offset` (optional, default=0): Results offset
- `search_type` (optional, default="fulltext"): Search type (fulltext|similarity|hybrid)
- `category` (optional): Filter by category
- `brand` (optional): Filter by brand
- `seller` (optional): Filter by seller
- `manufactured_by` (optional): Filter by manufacturer

**Response (200):**
```json
{
  "success": true,
  "message": "Search completed successfully",
  "query": "dental equipment",
  "search_type": "fulltext",
  "total": 1234,
  "results": [
    {
      "id": 1,
      "name": "Product Name",
      "mfr": "MFR123",
      "brand": "Brand Name",
      "seller": "Seller Name",
      "maincat": "Category",
      "price": "123.45",
      "url": "https://example.com/product",
      "rank": 0.95,
      "similarity_score": 0.87
    }
  ],
  "pagination": {
    "limit": 20,
    "offset": 0,
    "has_next": true,
    "has_prev": false
  },
  "search_time_ms": 45.2
}
```

### GET /search/suggestions
Get search suggestions for partial queries.

**Query Parameters:**
- `q` (required): Partial search query
- `limit` (optional, default=10): Maximum suggestions (1-20)

**Response (200):**
```json
{
  "success": true,
  "message": "Suggestions retrieved successfully",
  "query": "dent",
  "suggestions": [
    "dental equipment",
    "dental instruments",
    "dental implants"
  ]
}
```

### GET /search/popular
Get popular search terms.

**Query Parameters:**
- `limit` (optional, default=10): Maximum popular searches (1-50)

**Response (200):**
```json
{
  "success": true,
  "message": "Popular searches retrieved successfully",
  "popular_searches": [
    {"term": "dental equipment", "count": 1234},
    {"term": "implants", "count": 987}
  ]
}
```

## Products Endpoints

### GET /products/{product_id}
Get detailed product information.

**Path Parameters:**
- `product_id` (required): Product ID

**Response (200):**
```json
{
  "id": 1,
  "name": "Product Name",
  "mfr": "MFR123",
  "brand": "Brand Name",
  "seller": "Seller Name",
  "maincat": "Category",
  "category": "Subcategory",
  "manufactured_by": "manufacturer-slug",
  "price": "123.45",
  "url": "https://example.com/product",
  "search_text": "searchable text",
  "created_at": "2025-01-01T00:00:00Z",
  "updated_at": "2025-01-01T00:00:00Z"
}
```

### GET /products/categories/
Get all product categories.

**Query Parameters:**
- `limit` (optional, default=100): Maximum categories (1-500)

**Response (200):**
```json
{
  "success": true,
  "message": "Categories retrieved successfully",
  "categories": [
    {"name": "Instruments", "count": 36000},
    {"name": "Endodontics", "count": 22000}
  ]
}
```

### GET /products/brands/
Get all product brands.

**Query Parameters:**
- `limit` (optional, default=100): Maximum brands (1-500)

**Response (200):**
```json
{
  "success": true,
  "message": "Brands retrieved successfully",
  "brands": [
    {"name": "Brand Name", "count": 1234},
    {"name": "Another Brand", "count": 987}
  ]
}
```

### GET /products/sellers/
Get all product sellers.

**Query Parameters:**
- `limit` (optional, default=50): Maximum sellers (1-200)

**Response (200):**
```json
{
  "success": true,
  "message": "Sellers retrieved successfully",
  "sellers": [
    {"name": "Benco", "count": 82000},
    {"name": "Henry", "count": 77000}
  ]
}
```

## Shopping Lists Endpoints

### POST /shopping-lists/
Create a new shopping list.

**Request Body:**
```json
{
  "name": "My Shopping List",
  "description": "Optional description"
}
```

**Response (201):**
```json
{
  "id": 1,
  "user_id": "uuid",
  "name": "My Shopping List",
  "description": "Optional description",
  "created_at": "2025-01-01T00:00:00Z",
  "updated_at": "2025-01-01T00:00:00Z"
}
```

### GET /shopping-lists/
Get user's shopping lists with pagination.

**Query Parameters:**
- `page` (optional, default=1): Page number
- `per_page` (optional, default=20): Items per page (1-100)

**Response (200):**
```json
{
  "success": true,
  "message": "Shopping lists retrieved successfully",
  "data": {
    "items": [
      {
        "id": 1,
        "name": "My Shopping List",
        "description": "Optional description",
        "created_at": "2025-01-01T00:00:00Z"
      }
    ],
    "pagination": {
      "total": 5,
      "page": 1,
      "per_page": 20,
      "total_pages": 1,
      "has_next": false,
      "has_prev": false
    }
  }
}
```

### GET /shopping-lists/{list_id}
Get detailed shopping list with items.

**Path Parameters:**
- `list_id` (required): Shopping list ID

**Query Parameters:**
- `include_purchased` (optional, default=true): Include purchased items

**Response (200):**
```json
{
  "id": 1,
  "name": "My Shopping List",
  "description": "Optional description",
  "items": [
    {
      "id": 1,
      "product_id": 123,
      "quantity": 2,
      "notes": "Optional notes",
      "added_at": "2025-01-01T00:00:00Z",
      "product": {
        "name": "Product Name",
        "brand": "Brand Name",
        "price": "123.45"
      }
    }
  ],
  "summary": {
    "total_items": 5,
    "remaining_items": 3,
    "purchased_items": 2,
    "total_quantity": 10,
    "completion_percentage": 40.0
  }
}
```

### POST /shopping-lists/{list_id}/items
Add item to shopping list.

**Path Parameters:**
- `list_id` (required): Shopping list ID

**Request Body:**
```json
{
  "product_id": 123,
  "quantity": 2,
  "notes": "Optional notes"
}
```

**Response (201):**
```json
{
  "id": 1,
  "shopping_list_id": 1,
  "product_id": 123,
  "quantity": 2,
  "notes": "Optional notes",
  "added_at": "2025-01-01T00:00:00Z"
}
```

### GET /shopping-lists/{list_id}/analysis
Get price analysis for shopping list.

**Path Parameters:**
- `list_id` (required): Shopping list ID

**Response (200):**
```json
{
  "success": true,
  "message": "Shopping list analysis completed",
  "shopping_list_id": 1,
  "analysis": {
    "total_estimated_cost": 1234.56,
    "items_with_prices": 8,
    "items_without_prices": 2,
    "seller_breakdown": [
      {"seller": "Benco", "items": 5, "total_cost": 789.12},
      {"seller": "Henry", "items": 3, "total_cost": 445.44}
    ]
  }
}
```

## Error Responses

All endpoints may return the following error responses:

### 400 Bad Request
```json
{
  "detail": "Invalid request parameters"
}
```

### 401 Unauthorized
```json
{
  "detail": "Not authenticated"
}
```

### 403 Forbidden
```json
{
  "detail": "Not enough permissions"
}
```

### 404 Not Found
```json
{
  "detail": "Resource not found"
}
```

### 422 Validation Error
```json
{
  "detail": [
    {
      "loc": ["body", "email"],
      "msg": "field required",
      "type": "value_error.missing"
    }
  ]
}
```

### 429 Too Many Requests
```json
{
  "detail": "Rate limit exceeded"
}
```

### 500 Internal Server Error
```json
{
  "detail": "Internal server error"
}
```

## Rate Limiting

- Authentication endpoints: 10 requests per minute per IP
- Search endpoints: 100 requests per minute per user
- Other endpoints: 60 requests per minute per user

## Performance

- Search queries typically complete in 50-200ms
- Database contains 339,078+ products
- Full-text search with GIN indexes for optimal performance
- Redis caching for frequently accessed data

## Health Checks

Each service provides a health check endpoint:

- GET /auth/health
- GET /search/health  
- GET /products/health/
- GET /shopping-lists/health/

These endpoints return service status and can be used for monitoring.

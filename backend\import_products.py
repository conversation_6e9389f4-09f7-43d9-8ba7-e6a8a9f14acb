#!/usr/bin/env python3
"""
Product Data Import Script
Imports 339K products from allproducts.json into PostgreSQL database
"""

import asyncio
import json
import sys
import time
from pathlib import Path
from typing import List, Dict, Any

# Add the app directory to Python path
sys.path.insert(0, str(Path(__file__).parent / "app"))

from app.database import DatabaseManager
from app.config import settings


class ProductImporter:
    """Handles importing products from JSON to PostgreSQL."""
    
    def __init__(self, json_file_path: str):
        self.json_file_path = json_file_path
        self.db = None
        self.batch_size = 1000  # Process in batches for memory efficiency
        self.test_mode = False  # Set to True for testing with small sample
        self.total_imported = 0
        self.total_errors = 0
        
    async def initialize(self):
        """Initialize database connection."""
        self.db = DatabaseManager()
        await self.db.initialize()
        print(f"✅ Database connection initialized")
    
    async def close(self):
        """Close database connection."""
        if self.db:
            await self.db.close()
            print(f"✅ Database connection closed")
    
    def load_json_data(self) -> List[Dict[str, Any]]:
        """Load and validate JSON data."""
        print(f"📂 Loading JSON data from {self.json_file_path}...")
        
        try:
            with open(self.json_file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)

            # If in test mode, only use first 100 products
            if self.test_mode:
                data = data[:100]
                print(f"✅ Test mode: Using first {len(data)} products")
            else:
                print(f"✅ Loaded {len(data):,} products from JSON")

            return data
            
        except FileNotFoundError:
            print(f"❌ JSON file not found: {self.json_file_path}")
            raise
        except json.JSONDecodeError as e:
            print(f"❌ Invalid JSON format: {e}")
            raise
        except Exception as e:
            print(f"❌ Error loading JSON: {e}")
            raise
    
    def validate_product(self, product: Dict[str, Any], index: int) -> bool:
        """Validate a single product record."""
        # Only require name, url, and seller - MFR can be null/empty
        required_fields = ['name', 'url', 'seller']

        for field in required_fields:
            if not product.get(field):
                print(f"⚠️ Product {index}: Missing required field '{field}'")
                return False

        # Validate data types and lengths (allow empty MFR)
        mfr = product.get('mfr', '')
        if mfr and len(mfr) > 255:
            print(f"⚠️ Product {index}: MFR code too long")
            return False

        if len(product.get('seller', '')) > 50:
            print(f"⚠️ Product {index}: Seller name too long")
            return False

        return True
    
    def prepare_product_data(self, product: Dict[str, Any]) -> Dict[str, Any]:
        """Prepare product data for database insertion."""
        # Create focused search_text with ONLY MFR and name for optimal search performance
        # Other fields (category, brand, seller) are handled by dedicated filter dropdowns
        parts = [
            product.get('mfr', ''),   # Primary product identifier
            product.get('name', '')   # Product name/title
        ]
        search_text = ' | '.join(filter(None, parts))

        # Handle MFR - can be null/empty, convert empty string to None for database
        mfr = product.get('mfr', '')
        if not mfr or mfr.strip() == '':
            mfr = None

        return {
            'mfr': mfr,  # Can be None
            'name': product.get('name', ''),
            'url': product.get('url', ''),
            'maincat': product.get('maincat'),
            'brand': product.get('brand'),
            'manufactured_by': product.get('manufactured_by'),
            'category': product.get('category'),
            'seller': product.get('seller', ''),
            'price': product.get('price'),
            'search_text': search_text
        }
    
    async def clear_existing_data(self):
        """Clear existing product data (optional)."""
        print("🗑️ Clearing existing product data...")
        
        async with self.db.get_connection() as conn:
            # Get current count
            count = await conn.fetchval("SELECT COUNT(*) FROM products")
            print(f"📊 Current products in database: {count:,}")
            
            if count > 0:
                response = input("⚠️ Delete all existing products? (y/N): ")
                if response.lower() == 'y':
                    await conn.execute("TRUNCATE TABLE products RESTART IDENTITY CASCADE")
                    print("✅ Existing product data cleared")
                else:
                    print("ℹ️ Keeping existing data - new products will be added")
    
    async def import_batch(self, products: List[Dict[str, Any]], batch_num: int) -> int:
        """Import a batch of products."""
        if not products:
            return 0
        
        async with self.db.get_connection() as conn:
            # Prepare the INSERT query
            query = """
                INSERT INTO products (
                    mfr, name, url, maincat, brand, manufactured_by, 
                    category, seller, price, search_text
                ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)
            """
            
            imported_count = 0
            error_count = 0
            
            for product in products:
                try:
                    prepared_data = self.prepare_product_data(product)
                    
                    await conn.execute(
                        query,
                        prepared_data['mfr'],
                        prepared_data['name'],
                        prepared_data['url'],
                        prepared_data['maincat'],
                        prepared_data['brand'],
                        prepared_data['manufactured_by'],
                        prepared_data['category'],
                        prepared_data['seller'],
                        prepared_data['price'],
                        prepared_data['search_text']
                    )
                    imported_count += 1
                    
                except Exception as e:
                    error_count += 1
                    if error_count <= 5:  # Show first 5 errors
                        print(f"❌ Error importing product {product.get('name', 'Unknown')}: {e}")
            
            print(f"✅ Batch {batch_num}: {imported_count:,} imported, {error_count} errors")
            return imported_count
    
    async def import_products(self, clear_existing: bool = False):
        """Main import process."""
        print("🚀 Starting product import process...")
        start_time = time.time()
        
        # Load JSON data
        products_data = self.load_json_data()
        total_products = len(products_data)
        
        # Clear existing data if requested
        if clear_existing:
            await self.clear_existing_data()
        
        # Validate and filter products - but keep ALL products, just log issues
        print("🔍 Validating product data...")
        valid_products = []
        validation_warnings = 0

        for i, product in enumerate(products_data):
            if self.validate_product(product, i):
                valid_products.append(product)
            else:
                # Still include the product but count as warning
                valid_products.append(product)
                validation_warnings += 1
                if validation_warnings <= 10:  # Show first 10 warnings
                    print(f"⚠️ Product {i} has validation issues but will be imported")

        print(f"✅ Validation complete: {len(valid_products):,} products to import, {validation_warnings} with warnings")
        
        # Import in batches
        print(f"📦 Importing products in batches of {self.batch_size:,}...")
        
        batch_num = 1
        for i in range(0, len(valid_products), self.batch_size):
            batch = valid_products[i:i + self.batch_size]
            
            try:
                imported = await self.import_batch(batch, batch_num)
                self.total_imported += imported
                
                # Progress update
                progress = (i + len(batch)) / len(valid_products) * 100
                print(f"📊 Progress: {progress:.1f}% ({self.total_imported:,}/{len(valid_products):,})")
                
            except Exception as e:
                print(f"❌ Batch {batch_num} failed: {e}")
                self.total_errors += len(batch)
            
            batch_num += 1
        
        # Final statistics
        end_time = time.time()
        duration = end_time - start_time
        
        print("\n" + "=" * 60)
        print("📊 IMPORT SUMMARY")
        print("=" * 60)
        print(f"Total products in JSON: {total_products:,}")
        print(f"Products processed: {len(valid_products):,}")
        print(f"Successfully imported: {self.total_imported:,}")
        print(f"Import errors: {self.total_errors:,}")
        print(f"Duration: {duration:.2f} seconds")
        print(f"Rate: {self.total_imported / duration:.0f} products/second")

        # Verify database count
        async with self.db.get_connection() as conn:
            db_count = await conn.fetchval("SELECT COUNT(*) FROM products")
            print(f"Products in database: {db_count:,}")

            # Check for products with null MFR
            null_mfr_count = await conn.fetchval("SELECT COUNT(*) FROM products WHERE mfr IS NULL")
            print(f"Products with null MFR: {null_mfr_count:,}")

        # Success if we imported all products from JSON (exact match)
        if db_count == total_products:
            print("🎉 Import completed successfully! All products imported.")
            return True
        else:
            print(f"⚠️ Import completed but database count ({db_count:,}) doesn't match JSON count ({total_products:,})")
            return False
    
    async def create_search_indexes(self):
        """Create or recreate search indexes for optimal performance."""
        print("🔧 Creating search indexes...")
        
        async with self.db.get_connection() as conn:
            # The indexes should already exist from migrations, but let's verify
            indexes = [
                "idx_products_search_text_gin",
                "idx_products_search_text_trgm",
                "idx_products_mfr",
                "idx_products_brand",
                "idx_products_seller"
            ]
            
            for index_name in indexes:
                exists = await conn.fetchval("""
                    SELECT EXISTS (
                        SELECT 1 FROM pg_indexes 
                        WHERE indexname = $1 AND tablename = 'products'
                    )
                """, index_name)
                
                if exists:
                    print(f"✅ Index {index_name} exists")
                else:
                    print(f"⚠️ Index {index_name} missing")
        
        print("✅ Search indexes verified")


async def main():
    """Main function to run the import process."""
    print("🚀 ProfiDent Product Data Import")
    print("=" * 50)
    
    # Configuration
    json_file = "../allproducts.json"  # JSON file is in parent directory
    clear_existing = False
    
    # Check if JSON file exists
    if not Path(json_file).exists():
        print(f"❌ JSON file not found: {json_file}")
        print("Please ensure allproducts.json is in the current directory")
        return False
    
    # Initialize importer
    importer = ProductImporter(json_file)

    # Ask about test mode
    test_mode = input("Run in test mode with 100 products? (y/N): ").strip().lower()
    if test_mode == 'y':
        importer.test_mode = True
        print("🧪 Test mode enabled - importing first 100 products only")
    else:
        print("🚀 Production mode - importing ALL 339K products")

        # Ask about clearing existing data
        clear_existing = input("Clear existing products before import? (y/N): ").strip().lower()
        if clear_existing == 'y':
            clear_existing = True
            print("⚠️ Will clear existing products before import")
        else:
            clear_existing = False
            print("ℹ️ Will add to existing products")
    
    try:
        # Initialize database connection
        await importer.initialize()
        
        # Run import process
        if not importer.test_mode:
            success = await importer.import_products(clear_existing=clear_existing)
        else:
            success = await importer.import_products(clear_existing=False)
        
        # Create/verify search indexes
        await importer.create_search_indexes()
        
        return success
        
    except Exception as e:
        print(f"❌ Import failed: {e}")
        return False
        
    finally:
        # Clean up
        await importer.close()


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)

#!/usr/bin/env python3
"""
CSRF (Cross-Site Request Forgery) protection utilities.

Provides CSRF token generation, validation, and middleware for protecting
sensitive authentication operations.
"""

import secrets
import time
import hmac
import hashlib
import base64
from typing import Op<PERSON>, Tuple
from fastapi import HTT<PERSON>Exception, status, Request
from ..config import settings


class CSRFProtection:
    """CSRF protection utility class."""
    
    def __init__(self, secret_key: str = None):
        """Initialize CSRF protection with secret key."""
        self.secret_key = secret_key or settings.SECRET_KEY
        self.token_lifetime = 3600  # 1 hour in seconds
    
    def generate_csrf_token(self, user_id: str = None) -> str:
        """
        Generate a CSRF token.
        
        Args:
            user_id: Optional user ID to bind token to specific user
            
        Returns:
            Base64-encoded CSRF token
        """
        # Generate random token data
        random_data = secrets.token_bytes(32)
        timestamp = int(time.time())
        
        # Create payload: timestamp + user_id + random_data
        payload_parts = [
            str(timestamp).encode('utf-8'),
            (user_id or '').encode('utf-8'),
            random_data
        ]
        payload = b'|'.join(payload_parts)
        
        # Create HMAC signature
        signature = hmac.new(
            self.secret_key.encode('utf-8'),
            payload,
            hashlib.sha256
        ).digest()
        
        # Combine payload and signature
        token_data = payload + b'|' + signature
        
        # Return base64-encoded token
        return base64.urlsafe_b64encode(token_data).decode('utf-8')
    
    def validate_csrf_token(self, token: str, user_id: str = None) -> bool:
        """
        Validate a CSRF token.
        
        Args:
            token: Base64-encoded CSRF token
            user_id: Optional user ID to validate token binding
            
        Returns:
            True if token is valid, False otherwise
        """
        try:
            # Decode token
            token_data = base64.urlsafe_b64decode(token.encode('utf-8'))
            
            # Split token data
            parts = token_data.split(b'|')
            if len(parts) != 4:
                return False
            
            timestamp_bytes, token_user_id_bytes, random_data, signature = parts
            
            # Reconstruct payload
            payload = b'|'.join([timestamp_bytes, token_user_id_bytes, random_data])
            
            # Verify signature
            expected_signature = hmac.new(
                self.secret_key.encode('utf-8'),
                payload,
                hashlib.sha256
            ).digest()
            
            if not hmac.compare_digest(signature, expected_signature):
                return False
            
            # Check timestamp (token expiration)
            timestamp = int(timestamp_bytes.decode('utf-8'))
            current_time = int(time.time())
            
            if current_time - timestamp > self.token_lifetime:
                return False
            
            # Check user ID binding if provided
            if user_id is not None:
                token_user_id = token_user_id_bytes.decode('utf-8')
                if token_user_id != user_id:
                    return False
            
            return True
            
        except Exception:
            return False
    
    def get_csrf_token_from_request(self, request: Request) -> Optional[str]:
        """
        Extract CSRF token from request headers or form data.
        
        Args:
            request: FastAPI request object
            
        Returns:
            CSRF token if found, None otherwise
        """
        # Check X-CSRF-Token header (preferred)
        csrf_token = request.headers.get('X-CSRF-Token')
        if csrf_token:
            return csrf_token
        
        # Check X-CSRFToken header (alternative)
        csrf_token = request.headers.get('X-CSRFToken')
        if csrf_token:
            return csrf_token
        
        return None


# Global CSRF protection instance
csrf_protection = CSRFProtection()


def generate_csrf_token(user_id: str = None) -> str:
    """Generate a CSRF token."""
    return csrf_protection.generate_csrf_token(user_id)


def validate_csrf_token(token: str, user_id: str = None) -> bool:
    """Validate a CSRF token."""
    return csrf_protection.validate_csrf_token(token, user_id)


def get_csrf_token_from_request(request: Request) -> Optional[str]:
    """Extract CSRF token from request."""
    return csrf_protection.get_csrf_token_from_request(request)


async def verify_csrf_token(request: Request, user_id: str = None) -> None:
    """
    Verify CSRF token from request and raise HTTPException if invalid.
    
    Args:
        request: FastAPI request object
        user_id: Optional user ID for token binding validation
        
    Raises:
        HTTPException: If CSRF token is missing or invalid
    """
    # Skip CSRF validation for GET requests (they should be safe)
    if request.method == "GET":
        return
    
    # Get CSRF token from request
    csrf_token = get_csrf_token_from_request(request)
    
    if not csrf_token:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail={
                "error": "CSRF token missing",
                "message": "CSRF token is required for this operation",
                "error_code": "CSRF_TOKEN_MISSING"
            }
        )
    
    # Validate CSRF token
    if not validate_csrf_token(csrf_token, user_id):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail={
                "error": "CSRF token invalid",
                "message": "Invalid or expired CSRF token",
                "error_code": "CSRF_TOKEN_INVALID"
            }
        )


class CSRFTokenResponse:
    """Response model for CSRF token."""
    
    def __init__(self, token: str):
        self.csrf_token = token
        self.expires_in = csrf_protection.token_lifetime
    
    def to_dict(self) -> dict:
        """Convert to dictionary for JSON response."""
        return {
            "csrf_token": self.csrf_token,
            "expires_in": self.expires_in,
            "token_type": "csrf"
        }

#!/usr/bin/env python3
"""
Comprehensive audit logging for authentication and authorization events.

Provides structured logging for security events with proper categorization,
severity levels, and detailed context information.
"""

import json
import time
import logging
from typing import Optional, Dict, Any, List
from enum import Enum
from dataclasses import dataclass, asdict
from datetime import datetime
import redis


class AuditEventType(Enum):
    """Types of audit events."""
    # Authentication events
    LOGIN_SUCCESS = "login_success"
    LOGIN_FAILURE = "login_failure"
    LOGOUT = "logout"
    REGISTRATION = "registration"
    PASSWORD_CHANGE = "password_change"
    PASSWORD_RESET_REQUEST = "password_reset_request"
    PASSWORD_RESET_COMPLETE = "password_reset_complete"
    
    # Authorization events
    ACCESS_GRANTED = "access_granted"
    ACCESS_DENIED = "access_denied"
    PERMISSION_CHECK = "permission_check"
    ROLE_ASSIGNMENT = "role_assignment"
    ROLE_REMOVAL = "role_removal"
    
    # Security events
    ACCOUNT_LOCKED = "account_locked"
    ACCOUNT_UNLOCKED = "account_unlocked"
    RATE_LIMIT_EXCEEDED = "rate_limit_exceeded"
    SUSPICIOUS_ACTIVITY = "suspicious_activity"
    CSRF_TOKEN_INVALID = "csrf_token_invalid"
    
    # Administrative events
    USER_CREATED = "user_created"
    USER_UPDATED = "user_updated"
    USER_DELETED = "user_deleted"
    ROLE_CREATED = "role_created"
    PERMISSION_GRANTED = "permission_granted"
    PERMISSION_REVOKED = "permission_revoked"


class AuditSeverity(Enum):
    """Severity levels for audit events."""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


@dataclass
class AuditEvent:
    """Structured audit event."""
    event_type: AuditEventType
    severity: AuditSeverity
    user_id: Optional[str]
    user_email: Optional[str]
    ip_address: Optional[str]
    user_agent: Optional[str]
    resource: Optional[str]
    action: Optional[str]
    success: bool
    message: str
    details: Dict[str, Any]
    timestamp: float
    session_id: Optional[str] = None
    request_id: Optional[str] = None


class AuditLogger:
    """Comprehensive audit logging system."""
    
    def __init__(self, redis_client: Optional[redis.Redis] = None):
        """
        Initialize audit logger.
        
        Args:
            redis_client: Optional Redis client for storing audit events
        """
        self.redis = redis_client
        self.logger = logging.getLogger("audit")
        self.redis_key_prefix = "audit:"
        
        # Configure audit logger
        if not self.logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - AUDIT - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            self.logger.addHandler(handler)
            self.logger.setLevel(logging.INFO)
    
    def _get_severity_level(self, severity: AuditSeverity) -> int:
        """Convert audit severity to logging level."""
        severity_map = {
            AuditSeverity.LOW: logging.INFO,
            AuditSeverity.MEDIUM: logging.WARNING,
            AuditSeverity.HIGH: logging.ERROR,
            AuditSeverity.CRITICAL: logging.CRITICAL
        }
        return severity_map.get(severity, logging.INFO)
    
    async def log_event(self, event: AuditEvent) -> bool:
        """
        Log an audit event.
        
        Args:
            event: Audit event to log
            
        Returns:
            True if event was logged successfully, False otherwise
        """
        try:
            # Convert event to dictionary
            event_dict = asdict(event)
            event_dict["event_type"] = event.event_type.value
            event_dict["severity"] = event.severity.value
            
            # Log to standard logger
            log_level = self._get_severity_level(event.severity)
            log_message = f"{event.event_type.value.upper()}: {event.message}"
            
            if event.details:
                log_message += f" | Details: {json.dumps(event.details, default=str)}"
            
            self.logger.log(log_level, log_message)
            
            # Store in Redis if available
            if self.redis:
                try:
                    redis_key = f"{self.redis_key_prefix}{event.event_type.value}:{int(event.timestamp)}"
                    await self.redis.setex(
                        redis_key,
                        86400 * 30,  # Keep for 30 days
                        json.dumps(event_dict, default=str)
                    )
                except Exception as e:
                    self.logger.error(f"Failed to store audit event in Redis: {e}")
            
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to log audit event: {e}")
            return False
    
    async def log_authentication_event(
        self,
        event_type: AuditEventType,
        user_id: Optional[str] = None,
        user_email: Optional[str] = None,
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None,
        success: bool = True,
        message: str = "",
        details: Dict[str, Any] = None,
        severity: AuditSeverity = AuditSeverity.MEDIUM
    ) -> bool:
        """
        Log an authentication-related event.
        
        Args:
            event_type: Type of authentication event
            user_id: User ID involved in the event
            user_email: User email involved in the event
            ip_address: IP address of the request
            user_agent: User agent string
            success: Whether the event was successful
            message: Human-readable message
            details: Additional event details
            severity: Event severity level
            
        Returns:
            True if event was logged successfully, False otherwise
        """
        event = AuditEvent(
            event_type=event_type,
            severity=severity,
            user_id=user_id,
            user_email=user_email,
            ip_address=ip_address,
            user_agent=user_agent,
            resource="authentication",
            action=event_type.value,
            success=success,
            message=message,
            details=details or {},
            timestamp=time.time()
        )
        
        return await self.log_event(event)
    
    async def log_authorization_event(
        self,
        event_type: AuditEventType,
        user_id: Optional[str] = None,
        user_email: Optional[str] = None,
        resource: Optional[str] = None,
        action: Optional[str] = None,
        success: bool = True,
        message: str = "",
        details: Dict[str, Any] = None,
        severity: AuditSeverity = AuditSeverity.LOW
    ) -> bool:
        """
        Log an authorization-related event.
        
        Args:
            event_type: Type of authorization event
            user_id: User ID involved in the event
            user_email: User email involved in the event
            resource: Resource being accessed
            action: Action being performed
            success: Whether the event was successful
            message: Human-readable message
            details: Additional event details
            severity: Event severity level
            
        Returns:
            True if event was logged successfully, False otherwise
        """
        event = AuditEvent(
            event_type=event_type,
            severity=severity,
            user_id=user_id,
            user_email=user_email,
            ip_address=None,
            user_agent=None,
            resource=resource,
            action=action,
            success=success,
            message=message,
            details=details or {},
            timestamp=time.time()
        )
        
        return await self.log_event(event)
    
    async def log_security_event(
        self,
        event_type: AuditEventType,
        user_id: Optional[str] = None,
        user_email: Optional[str] = None,
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None,
        message: str = "",
        details: Dict[str, Any] = None,
        severity: AuditSeverity = AuditSeverity.HIGH
    ) -> bool:
        """
        Log a security-related event.
        
        Args:
            event_type: Type of security event
            user_id: User ID involved in the event
            user_email: User email involved in the event
            ip_address: IP address of the request
            user_agent: User agent string
            message: Human-readable message
            details: Additional event details
            severity: Event severity level
            
        Returns:
            True if event was logged successfully, False otherwise
        """
        event = AuditEvent(
            event_type=event_type,
            severity=severity,
            user_id=user_id,
            user_email=user_email,
            ip_address=ip_address,
            user_agent=user_agent,
            resource="security",
            action=event_type.value,
            success=False,  # Security events are typically failures
            message=message,
            details=details or {},
            timestamp=time.time()
        )
        
        return await self.log_event(event)
    
    async def get_audit_events(
        self,
        event_type: Optional[AuditEventType] = None,
        user_id: Optional[str] = None,
        start_time: Optional[float] = None,
        end_time: Optional[float] = None,
        limit: int = 100
    ) -> List[Dict[str, Any]]:
        """
        Retrieve audit events from Redis.
        
        Args:
            event_type: Filter by event type
            user_id: Filter by user ID
            start_time: Start timestamp filter
            end_time: End timestamp filter
            limit: Maximum number of events to return
            
        Returns:
            List of audit events
        """
        if not self.redis:
            return []
        
        try:
            # Build search pattern
            if event_type:
                pattern = f"{self.redis_key_prefix}{event_type.value}:*"
            else:
                pattern = f"{self.redis_key_prefix}*"
            
            # Get matching keys
            keys = await self.redis.keys(pattern)
            
            # Sort keys by timestamp (newest first)
            keys.sort(reverse=True)
            
            # Limit results
            keys = keys[:limit]
            
            # Retrieve events
            events = []
            for key in keys:
                try:
                    event_data = await self.redis.get(key)
                    if event_data:
                        event = json.loads(event_data)
                        
                        # Apply filters
                        if user_id and event.get("user_id") != user_id:
                            continue
                        
                        if start_time and event.get("timestamp", 0) < start_time:
                            continue
                        
                        if end_time and event.get("timestamp", 0) > end_time:
                            continue
                        
                        events.append(event)
                        
                except Exception as e:
                    self.logger.error(f"Failed to parse audit event {key}: {e}")
                    continue
            
            return events
            
        except Exception as e:
            self.logger.error(f"Failed to retrieve audit events: {e}")
            return []


# Global audit logger instance
audit_logger: Optional[AuditLogger] = None


def initialize_audit_logger(redis_client: Optional[redis.Redis] = None):
    """Initialize the global audit logger."""
    global audit_logger
    audit_logger = AuditLogger(redis_client)


def get_audit_logger() -> AuditLogger:
    """Get the global audit logger instance."""
    if audit_logger is None:
        raise RuntimeError("Audit logger not initialized")
    return audit_logger

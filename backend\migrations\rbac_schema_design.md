# RBAC Database Schema Design

## Overview
This document outlines the comprehensive Role-Based Access Control (RBAC) database schema design for the ProfiDent application. All changes are **additive only** and preserve existing functionality.

## Current Database Structure (Preserved)

### Existing Users Table
```sql
-- Current users table structure (DO NOT MODIFY)
users (
    id UUID PRIMARY KEY,
    email VARCHAR UNIQUE NOT NULL,
    hashed_password VARCHAR NOT NULL,
    full_name VA<PERSON><PERSON><PERSON>,
    is_active BOOLEAN DEFAULT TRUE,
    is_superuser BOOLEAN DEFAULT FALSE,  -- Keep for backward compatibility
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
)
```

## New RBAC Tables (Additive Only)

### 1. Roles Table
```sql
CREATE TABLE roles (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(50) UNIQUE NOT NULL,
    display_name VARCHAR(100) NOT NULL,
    description TEXT,
    is_system_role BOOLEAN DEFAULT FALSE,  -- Prevents deletion of core roles
    is_active BOOLEAN DEFAULT TRUE,
    metadata JSONB DEFAULT '{}',  -- For extensible role properties
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);
```

### 2. Permissions Table
```sql
CREATE TABLE permissions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(100) UNIQUE NOT NULL,
    resource VARCHAR(50) NOT NULL,  -- e.g., 'users', 'products', 'shopping_lists'
    action VARCHAR(50) NOT NULL,    -- e.g., 'create', 'read', 'update', 'delete'
    description TEXT,
    is_system_permission BOOLEAN DEFAULT FALSE,  -- Prevents deletion
    created_at TIMESTAMPTZ DEFAULT NOW()
);
```

### 3. User Roles Junction Table
```sql
CREATE TABLE user_roles (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    role_id UUID NOT NULL REFERENCES roles(id) ON DELETE CASCADE,
    assigned_by UUID REFERENCES users(id),  -- Who assigned this role
    assigned_at TIMESTAMPTZ DEFAULT NOW(),
    expires_at TIMESTAMPTZ,  -- Optional role expiration
    is_active BOOLEAN DEFAULT TRUE,
    UNIQUE(user_id, role_id)  -- Prevent duplicate role assignments
);
```

### 4. Role Permissions Junction Table
```sql
CREATE TABLE role_permissions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    role_id UUID NOT NULL REFERENCES roles(id) ON DELETE CASCADE,
    permission_id UUID NOT NULL REFERENCES permissions(id) ON DELETE CASCADE,
    granted_by UUID REFERENCES users(id),
    granted_at TIMESTAMPTZ DEFAULT NOW(),
    UNIQUE(role_id, permission_id)  -- Prevent duplicate permissions
);
```

## Indexes for Performance

### Role-related Indexes
```sql
-- User roles lookup (most common query)
CREATE INDEX idx_user_roles_user_id ON user_roles(user_id) WHERE is_active = TRUE;
CREATE INDEX idx_user_roles_role_id ON user_roles(role_id) WHERE is_active = TRUE;

-- Role permissions lookup
CREATE INDEX idx_role_permissions_role_id ON role_permissions(role_id);
CREATE INDEX idx_role_permissions_permission_id ON role_permissions(permission_id);

-- Permission resource/action lookup
CREATE INDEX idx_permissions_resource_action ON permissions(resource, action);

-- Role name lookup
CREATE INDEX idx_roles_name ON roles(name) WHERE is_active = TRUE;
```

## Initial Data Seeding

### Core Roles
1. **superadmin** - Full system access
2. **user** - Standard user access

### Core Permissions
- **users.create** - Create new users
- **users.read** - View user information
- **users.update** - Update user information
- **users.delete** - Delete/deactivate users
- **users.manage_roles** - Assign/remove user roles
- **products.read** - Search and view products
- **shopping_lists.create** - Create shopping lists
- **shopping_lists.read** - View own shopping lists
- **shopping_lists.update** - Update own shopping lists
- **shopping_lists.delete** - Delete own shopping lists
- **shopping_lists.manage_all** - Manage all users' shopping lists
- **system.admin** - System administration access

### Role-Permission Mappings
**Superadmin Role:**
- All permissions

**User Role:**
- products.read
- shopping_lists.create
- shopping_lists.read
- shopping_lists.update
- shopping_lists.delete

## Migration Strategy

1. **Phase 1**: Create new tables (roles, permissions, user_roles, role_permissions)
2. **Phase 2**: Seed initial roles and permissions
3. **Phase 3**: Assign default roles to existing users
4. **Phase 4**: Add indexes for performance
5. **Phase 5**: Verify data integrity and test

## Backward Compatibility

- Existing `is_superuser` field in users table is preserved
- All existing authentication flows continue to work
- New RBAC system supplements existing authorization
- Gradual migration path for role-based permissions

## Security Considerations

- All role assignments are auditable (assigned_by, assigned_at)
- System roles and permissions cannot be deleted
- Role expiration support for temporary access
- Unique constraints prevent duplicate assignments
- Cascade deletes maintain referential integrity

## Performance Considerations

- Optimized indexes for common queries
- JSONB metadata for extensible properties
- Efficient user permission lookup patterns
- Minimal impact on existing search functionality

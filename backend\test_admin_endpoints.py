#!/usr/bin/env python3
"""
Test script for admin-only endpoints.
"""

import asyncio
import aiohttp
import json
from app.config import settings


async def test_admin_endpoints():
    """Test admin-only endpoints with superadmin authentication."""
    print("🔐 Testing Admin-Only Endpoints")
    print("=" * 50)
    
    base_url = "http://localhost:8000"  # Default FastAPI port
    
    # First, authenticate as superadmin
    print("\n🔍 Step 1: Authenticate as superadmin...")
    
    async with aiohttp.ClientSession() as session:
        # Login as superyzn superadmin
        login_data = {
            "email": "<EMAIL>",
            "password": "SuperYzn123!"
        }
        
        async with session.post(f"{base_url}/api/v1/auth/login", json=login_data) as response:
            if response.status != 200:
                print(f"❌ Login failed: {response.status}")
                text = await response.text()
                print(f"Response: {text}")
                return False
            
            auth_data = await response.json()
            access_token = auth_data["token"]["access_token"]
            print("✅ Superadmin authentication successful")
        
        # Set authorization header
        headers = {"Authorization": f"Bearer {access_token}"}
        
        # Test 1: List all users
        print("\n🔍 Test 1: List all users...")
        async with session.get(f"{base_url}/api/v1/admin/users", headers=headers) as response:
            if response.status == 200:
                users = await response.json()
                print(f"✅ Listed {len(users)} users")
                for user in users[:3]:  # Show first 3 users
                    print(f"   - {user['email']} ({'Active' if user['is_active'] else 'Inactive'})")
            else:
                print(f"❌ Failed to list users: {response.status}")
                text = await response.text()
                print(f"Response: {text}")
        
        # Test 2: Get user statistics
        print("\n🔍 Test 2: Get user statistics...")
        async with session.get(f"{base_url}/api/v1/admin/stats/users", headers=headers) as response:
            if response.status == 200:
                stats = await response.json()
                print("✅ User statistics retrieved:")
                print(f"   - Total users: {stats.get('total_users', 0)}")
                print(f"   - Active users: {stats.get('active_users', 0)}")
                print(f"   - Superusers: {stats.get('superuser_count', 0)}")
                print(f"   - New users (last 7 days): {stats.get('new_users_last_7_days', 0)}")
            else:
                print(f"❌ Failed to get user statistics: {response.status}")
        
        # Test 3: Get role statistics
        print("\n🔍 Test 3: Get role statistics...")
        async with session.get(f"{base_url}/api/v1/admin/stats/roles", headers=headers) as response:
            if response.status == 200:
                stats = await response.json()
                print("✅ Role statistics retrieved:")
                print(f"   - Total roles: {stats.get('total_roles', 0)}")
                print(f"   - Active roles: {stats.get('active_roles', 0)}")
                print(f"   - System roles: {stats.get('system_roles', 0)}")
                print(f"   - Custom roles: {stats.get('custom_roles', 0)}")
            else:
                print(f"❌ Failed to get role statistics: {response.status}")
        
        # Test 4: Get permission statistics
        print("\n🔍 Test 4: Get permission statistics...")
        async with session.get(f"{base_url}/api/v1/admin/stats/permissions", headers=headers) as response:
            if response.status == 200:
                stats = await response.json()
                print("✅ Permission statistics retrieved:")
                print(f"   - Total permissions: {stats.get('total_permissions', 0)}")
                print(f"   - Unique resources: {stats.get('unique_resources', 0)}")
                print(f"   - System permissions: {stats.get('system_permissions', 0)}")
                print(f"   - Custom permissions: {stats.get('custom_permissions', 0)}")
            else:
                print(f"❌ Failed to get permission statistics: {response.status}")
        
        # Test 5: Check database health
        print("\n🔍 Test 5: Check database health...")
        async with session.get(f"{base_url}/api/v1/admin/health/database", headers=headers) as response:
            if response.status == 200:
                health = await response.json()
                print(f"✅ Database health: {health.get('status', 'unknown')}")
                if 'database_stats' in health:
                    stats = health['database_stats']
                    print(f"   - Total users: {stats.get('total_users', 0)}")
                    print(f"   - Total roles: {stats.get('total_roles', 0)}")
                    print(f"   - Total permissions: {stats.get('total_permissions', 0)}")
            else:
                print(f"❌ Failed to check database health: {response.status}")
        
        # Test 6: Search users with filters
        print("\n🔍 Test 6: Search users with filters...")
        params = {"search": "profident", "limit": 5}
        async with session.get(f"{base_url}/api/v1/admin/users", headers=headers, params=params) as response:
            if response.status == 200:
                users = await response.json()
                print(f"✅ Found {len(users)} users matching 'profident'")
                for user in users:
                    print(f"   - {user['email']}")
            else:
                print(f"❌ Failed to search users: {response.status}")
        
        # Test 7: Test unauthorized access (without superadmin)
        print("\n🔍 Test 7: Test unauthorized access...")
        
        # Try to access admin endpoint without proper authorization
        async with session.get(f"{base_url}/api/v1/admin/users") as response:
            if response.status == 401:
                print("✅ Unauthorized access properly blocked (no token)")
            else:
                print(f"⚠️  Expected 401, got {response.status}")
        
        # Test with regular user token (if we had one)
        # For now, we'll skip this test since we'd need to create a regular user
        
        print("\n🎉 Admin Endpoints Test Completed!")
        print("=" * 50)
        print("✅ User listing working")
        print("✅ User statistics working")
        print("✅ Role statistics working")
        print("✅ Permission statistics working")
        print("✅ Database health check working")
        print("✅ User search with filters working")
        print("✅ Authorization protection working")
        
        return True


async def test_admin_user_management():
    """Test admin user management operations."""
    print("\n🔧 Testing Admin User Management Operations")
    print("=" * 50)
    
    base_url = "http://localhost:8000"  # Default FastAPI port
    
    async with aiohttp.ClientSession() as session:
        # Login as superadmin
        login_data = {
            "email": "<EMAIL>",
            "password": "SuperYzn123!"
        }
        
        async with session.post(f"{base_url}/api/v1/auth/login", json=login_data) as response:
            if response.status != 200:
                print(f"❌ Login failed: {response.status}")
                return False
            
            auth_data = await response.json()
            access_token = auth_data["token"]["access_token"]
        
        headers = {"Authorization": f"Bearer {access_token}"}
        
        # Create a test user for management operations
        print("\n🔍 Creating test user for management operations...")
        
        test_user_data = {
            "email": "<EMAIL>",
            "password": "TestUser123!",
            "full_name": "Admin Test User"
        }
        
        async with session.post(f"{base_url}/api/v1/auth/register", json=test_user_data) as response:
            if response.status == 201:
                user_data = await response.json()
                test_user_id = user_data["user"]["id"]
                print(f"✅ Test user created: {test_user_id}")
            else:
                print(f"❌ Failed to create test user: {response.status}")
                return False
        
        # Test user details retrieval
        print(f"\n🔍 Getting user details for {test_user_id}...")
        async with session.get(f"{base_url}/api/v1/admin/users/{test_user_id}", headers=headers) as response:
            if response.status == 200:
                user_details = await response.json()
                print(f"✅ User details retrieved: {user_details['email']}")
                print(f"   - Roles: {user_details.get('roles', [])}")
                print(f"   - Permissions: {len(user_details.get('permissions', []))} permissions")
            else:
                print(f"❌ Failed to get user details: {response.status}")
        
        # Test user update
        print(f"\n🔍 Updating user information...")
        update_data = {
            "full_name": "Updated Admin Test User"
        }
        async with session.put(f"{base_url}/api/v1/admin/users/{test_user_id}", headers=headers, json=update_data) as response:
            if response.status == 200:
                updated_user = await response.json()
                print(f"✅ User updated: {updated_user['full_name']}")
            else:
                print(f"❌ Failed to update user: {response.status}")
        
        print("\n🎉 Admin User Management Test Completed!")
        return True


if __name__ == "__main__":
    print("🚀 Starting Admin Endpoints Tests...")
    print("Make sure the FastAPI server is running on the configured port.")
    print()
    
    success1 = asyncio.run(test_admin_endpoints())
    success2 = asyncio.run(test_admin_user_management())
    
    if success1 and success2:
        print("\n🎉 All admin endpoint tests passed!")
        exit(0)
    else:
        print("\n❌ Some tests failed!")
        exit(1)

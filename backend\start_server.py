#!/usr/bin/env python3
"""
Simple server startup script for ProfiDent API
Handles import issues and starts the FastAPI server
"""

import sys
import os
from pathlib import Path

# Add current directory to Python path
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))
sys.path.insert(0, str(current_dir / "app"))

# Set environment variables
os.environ.setdefault("PYTHONPATH", str(current_dir))

try:
    import uvicorn
    from fastapi import FastAPI, HTTPException, Depends
    from fastapi.middleware.cors import CORSMiddleware
    from fastapi.security import HTTP<PERSON>earer, HTTPAuthorizationCredentials
    from pydantic import BaseModel
    from typing import List, Optional
    import json
    import time

    # Create a comprehensive FastAPI app for testing
    app = FastAPI(
        title="ProfiDent API",
        description="Dental Product Price Comparison Platform",
        version="1.0.0"
    )

    # Add CORS middleware
    app.add_middleware(
        CORSMiddleware,
        allow_origins=["http://localhost:3000", "http://localhost:5173", "http://127.0.0.1:3000", "http://127.0.0.1:5173"],
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )

    # Security
    security = HTTPBearer(auto_error=False)

    # Mock data and models
    class User(BaseModel):
        id: int
        email: str
        full_name: str
        created_at: str

    class LoginRequest(BaseModel):
        email: str
        password: str

    class RegisterRequest(BaseModel):
        email: str
        password: str
        full_name: str

    class AuthResponse(BaseModel):
        user: User
        token: dict

    class Product(BaseModel):
        id: int
        name: str
        seller: str
        price: Optional[str] = None
        maincat: Optional[str] = None
        brand: Optional[str] = None
        description: Optional[str] = None

    class SearchResponse(BaseModel):
        results: List[Product]
        total: int
        page: int
        limit: int
        search_time_ms: float

    # Mock users database
    mock_users = {}
    mock_user_counter = 1

    # Mock products (sample data)
    mock_products = [
        {"id": 1, "name": "Dental Implant System", "seller": "Nobel Biocare", "price": "$299.99", "maincat": "Implants", "brand": "Nobel"},
        {"id": 2, "name": "Composite Filling Material", "seller": "3M ESPE", "price": "$45.50", "maincat": "Restorative", "brand": "3M"},
        {"id": 3, "name": "Orthodontic Brackets", "seller": "Ormco", "price": "$12.99", "maincat": "Orthodontics", "brand": "Ormco"},
        {"id": 4, "name": "Endodontic Files", "seller": "Dentsply", "price": "$89.00", "maincat": "Endodontics", "brand": "Dentsply"},
        {"id": 5, "name": "Dental Crown", "seller": "Ivoclar", "price": "$156.75", "maincat": "Prosthetics", "brand": "Ivoclar"},
    ]

    def get_current_user(credentials: HTTPAuthorizationCredentials = Depends(security)):
        if not credentials:
            raise HTTPException(status_code=401, detail="Not authenticated")
        # Mock authentication - in real app, verify JWT token
        return {"id": 1, "email": "<EMAIL>", "full_name": "Test User"}

    # Health endpoints
    @app.get("/")
    async def root():
        return {"message": "ProfiDent API is running!", "status": "healthy"}

    @app.get("/health")
    async def health():
        return {"status": "healthy", "message": "API server is running"}

    @app.get("/api/v1/health")
    async def api_health():
        return {"status": "healthy", "message": "API v1 is running"}

    # Auth endpoints
    @app.post("/api/v1/auth/register", response_model=AuthResponse)
    async def register(request: RegisterRequest):
        global mock_user_counter

        # Check if user already exists
        if request.email in mock_users:
            raise HTTPException(status_code=400, detail="Email already registered")

        # Create new user
        user = User(
            id=mock_user_counter,
            email=request.email,
            full_name=request.full_name,
            created_at=time.strftime("%Y-%m-%dT%H:%M:%SZ")
        )

        mock_users[request.email] = user
        mock_user_counter += 1

        # Mock token
        token = {
            "access_token": f"mock_token_{user.id}",
            "token_type": "bearer",
            "expires_in": 3600
        }

        return AuthResponse(user=user, token=token)

    @app.post("/api/v1/auth/login", response_model=AuthResponse)
    async def login(request: LoginRequest):
        # Mock login - accept any registered email
        if request.email not in mock_users:
            raise HTTPException(status_code=401, detail="Invalid credentials")

        user = mock_users[request.email]

        # Mock token
        token = {
            "access_token": f"mock_token_{user.id}",
            "token_type": "bearer",
            "expires_in": 3600
        }

        return AuthResponse(user=user, token=token)

    @app.get("/api/v1/auth/me", response_model=User)
    async def get_current_user_info(current_user: dict = Depends(get_current_user)):
        return User(
            id=current_user["id"],
            email=current_user["email"],
            full_name=current_user["full_name"],
            created_at=time.strftime("%Y-%m-%dT%H:%M:%SZ")
        )

    # Search endpoints
    @app.get("/api/v1/search/", response_model=SearchResponse)
    async def search_products(
        q: str = "",
        limit: int = 20,
        offset: int = 0,
        current_user: dict = Depends(get_current_user)
    ):
        start_time = time.time()

        # Mock search - filter products by query
        filtered_products = []
        if q:
            query_lower = q.lower()
            filtered_products = [
                Product(**product) for product in mock_products
                if query_lower in product["name"].lower() or
                   query_lower in product.get("maincat", "").lower() or
                   query_lower in product.get("brand", "").lower()
            ]
        else:
            filtered_products = [Product(**product) for product in mock_products]

        # Apply pagination
        total = len(filtered_products)
        paginated_results = filtered_products[offset:offset + limit]

        search_time = (time.time() - start_time) * 1000

        return SearchResponse(
            results=paginated_results,
            total=total,
            page=(offset // limit) + 1,
            limit=limit,
            search_time_ms=round(search_time, 2)
        )

    @app.get("/api/v1/search/suggestions")
    async def get_search_suggestions(
        q: str = "",
        current_user: dict = Depends(get_current_user)
    ):
        # Mock suggestions
        suggestions = []
        if q:
            query_lower = q.lower()
            all_terms = ["dental", "implant", "crown", "filling", "orthodontic", "endodontic", "composite", "brackets"]
            suggestions = [term for term in all_terms if term.startswith(query_lower)][:5]

        return {"suggestions": suggestions}

    @app.get("/api/v1/search/popular")
    async def get_popular_searches(current_user: dict = Depends(get_current_user)):
        # Mock popular searches
        return {
            "popular_searches": [
                "dental implants",
                "composite filling",
                "orthodontic brackets",
                "endodontic files",
                "dental crown"
            ]
        }

    # Product endpoints
    @app.get("/api/v1/products/{product_id}", response_model=Product)
    async def get_product(product_id: int, current_user: dict = Depends(get_current_user)):
        product = next((p for p in mock_products if p["id"] == product_id), None)
        if not product:
            raise HTTPException(status_code=404, detail="Product not found")
        return Product(**product)

    @app.get("/api/v1/products/categories/")
    async def get_categories(current_user: dict = Depends(get_current_user)):
        # Mock categories
        categories = [
            {"name": "Implants", "count": 150},
            {"name": "Restorative", "count": 200},
            {"name": "Orthodontics", "count": 100},
            {"name": "Endodontics", "count": 80},
            {"name": "Prosthetics", "count": 120},
        ]
        return {"categories": categories}

    @app.get("/api/v1/products/brands/")
    async def get_brands(current_user: dict = Depends(get_current_user)):
        # Mock brands
        brands = [
            {"name": "Nobel Biocare", "count": 50},
            {"name": "3M ESPE", "count": 75},
            {"name": "Ormco", "count": 30},
            {"name": "Dentsply", "count": 60},
            {"name": "Ivoclar", "count": 40},
        ]
        return {"brands": brands}

    # Shopping list endpoints
    @app.get("/api/v1/shopping-lists/")
    async def get_shopping_lists(current_user: dict = Depends(get_current_user)):
        # Mock shopping lists
        return {
            "data": {
                "items": [
                    {
                        "id": 1,
                        "name": "Dental Supplies Order",
                        "description": "Monthly supply order",
                        "created_at": time.strftime("%Y-%m-%dT%H:%M:%SZ"),
                        "item_count": 3,
                        "total_value": "$455.48"
                    }
                ],
                "total": 1,
                "page": 1,
                "limit": 20
            }
        }

    @app.post("/api/v1/shopping-lists/")
    async def create_shopping_list(
        request: dict,
        current_user: dict = Depends(get_current_user)
    ):
        # Mock create shopping list
        return {
            "id": 2,
            "name": request.get("name", "New List"),
            "description": request.get("description", ""),
            "created_at": time.strftime("%Y-%m-%dT%H:%M:%SZ"),
            "item_count": 0,
            "total_value": "$0.00"
        }
    
    if __name__ == "__main__":
        print("🚀 Starting ProfiDent API Server...")
        print("📍 Server will be available at: http://localhost:8000")
        print("📖 API Documentation: http://localhost:8000/docs")
        print("🔧 Health Check: http://localhost:8000/health")
        
        uvicorn.run(
            app,
            host="0.0.0.0",
            port=8000,
            reload=False,
            log_level="info"
        )

except ImportError as e:
    print(f"❌ Import error: {e}")
    print("Installing required packages...")
    
    import subprocess
    packages = [
        "fastapi",
        "uvicorn[standard]",
        "asyncpg",
        "redis",
        "passlib[bcrypt]",
        "python-jose[cryptography]",
        "python-multipart",
        "pydantic-settings"
    ]
    
    for package in packages:
        try:
            subprocess.check_call([sys.executable, "-m", "pip", "install", package])
        except subprocess.CalledProcessError:
            print(f"Failed to install {package}")
    
    print("Please run the script again after installing packages.")
    sys.exit(1)

except Exception as e:
    print(f"❌ Error starting server: {e}")
    sys.exit(1)

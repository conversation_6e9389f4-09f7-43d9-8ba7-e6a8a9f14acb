#!/usr/bin/env python3
"""
Comprehensive Playwright Test for Filter Functionality
Tests the corrected filter implementation with manufacturers, categories, and filtered suggestions
"""

import asyncio
import sys
from playwright.async_api import async_playwright
import time

class FilterFunctionalityTester:
    def __init__(self):
        self.browser = None
        self.page = None
        self.test_results = []

    async def setup(self):
        """Setup browser and page"""
        playwright = await async_playwright().start()
        self.browser = await playwright.chromium.launch(headless=False)  # Visible for manual testing
        self.page = await self.browser.new_page()
        
        # Set viewport
        await self.page.set_viewport_size({"width": 1280, "height": 720})
        
        print("🚀 Browser launched and ready for testing")

    async def log_test(self, test_name, success, details=""):
        """Log test results"""
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{status} {test_name}")
        if details:
            print(f"   {details}")
        
        self.test_results.append({
            "test": test_name,
            "success": success,
            "details": details
        })

    async def test_homepage_loads(self):
        """Test that homepage loads successfully"""
        try:
            await self.page.goto("http://localhost:3000")
            await self.page.wait_for_load_state("networkidle")
            
            # Check if page loaded
            title = await self.page.title()
            success = "ProfiDent" in title or len(title) > 0
            
            await self.log_test("Homepage loads", success, f"Title: {title}")
            return success
        except Exception as e:
            await self.log_test("Homepage loads", False, f"Error: {e}")
            return False

    async def test_manufacturers_filter_display(self):
        """Test that manufacturers filter is displayed (not brands)"""
        try:
            # Look for manufacturers filter
            manufacturers_filter = await self.page.locator("text=Manufacturers").first
            is_visible = await manufacturers_filter.is_visible()
            
            # Check that "Brands" is not displayed
            brands_elements = await self.page.locator("text=Brands").count()
            no_brands = brands_elements == 0
            
            success = is_visible and no_brands
            details = f"Manufacturers visible: {is_visible}, Brands count: {brands_elements}"
            
            await self.log_test("Manufacturers filter displayed (not brands)", success, details)
            return success
        except Exception as e:
            await self.log_test("Manufacturers filter displayed", False, f"Error: {e}")
            return False

    async def test_filter_dropdowns_load_data(self):
        """Test that filter dropdowns load data from correct API endpoints"""
        try:
            # Wait for filters to load
            await self.page.wait_for_timeout(2000)
            
            # Click on manufacturers dropdown
            manufacturers_dropdown = await self.page.locator("text=Manufacturers").first
            await manufacturers_dropdown.click()
            await self.page.wait_for_timeout(1000)
            
            # Check if manufacturer options are loaded
            manufacturer_options = await self.page.locator("[role='button']:has-text('hu-friedy'), [role='button']:has-text('Hu-Friedy'), text*='hu-friedy', text*='Hu-Friedy'").count()
            manufacturers_loaded = manufacturer_options > 0
            
            # Close dropdown
            await self.page.keyboard.press("Escape")
            
            # Click on categories dropdown
            categories_dropdown = await self.page.locator("text=Categories").first
            await categories_dropdown.click()
            await self.page.wait_for_timeout(1000)
            
            # Check if category options are loaded
            category_options = await self.page.locator("text*='Instruments', text*='Rotary'").count()
            categories_loaded = category_options > 0
            
            # Close dropdown
            await self.page.keyboard.press("Escape")
            
            success = manufacturers_loaded and categories_loaded
            details = f"Manufacturers loaded: {manufacturers_loaded}, Categories loaded: {categories_loaded}"
            
            await self.log_test("Filter dropdowns load data", success, details)
            return success
        except Exception as e:
            await self.log_test("Filter dropdowns load data", False, f"Error: {e}")
            return False

    async def test_search_with_filters(self):
        """Test search functionality with manufacturer and category filters"""
        try:
            # Navigate to search page or use search input
            search_input = await self.page.locator("input[placeholder*='Search'], input[type='search']").first
            
            if await search_input.is_visible():
                # Type search query
                await search_input.fill("composite")
                await search_input.press("Enter")
                await self.page.wait_for_timeout(2000)
                
                # Check if search results are displayed
                results_found = await self.page.locator("text*='results', text*='found', [data-testid*='result'], .search-result").count() > 0
                
                success = results_found
                details = f"Search results displayed: {results_found}"
            else:
                success = False
                details = "Search input not found"
            
            await self.log_test("Search with filters", success, details)
            return success
        except Exception as e:
            await self.log_test("Search with filters", False, f"Error: {e}")
            return False

    async def test_filtered_suggestions(self):
        """Test that search suggestions respect active filters"""
        try:
            # Go back to homepage
            await self.page.goto("http://localhost:3000")
            await self.page.wait_for_load_state("networkidle")
            
            # Find and interact with search input
            search_input = await self.page.locator("input[placeholder*='Search'], input[type='search']").first
            
            if await search_input.is_visible():
                # Type partial query to trigger suggestions
                await search_input.fill("comp")
                await self.page.wait_for_timeout(1500)
                
                # Check if suggestions appear
                suggestions = await self.page.locator(".suggestion, [data-testid*='suggestion'], text*='composite'").count()
                suggestions_shown = suggestions > 0
                
                success = suggestions_shown
                details = f"Suggestions count: {suggestions}"
            else:
                success = False
                details = "Search input not found"
            
            await self.log_test("Filtered suggestions", success, details)
            return success
        except Exception as e:
            await self.log_test("Filtered suggestions", False, f"Error: {e}")
            return False

    async def test_api_endpoints(self):
        """Test that API endpoints return correct data"""
        try:
            # Test manufacturers endpoint
            response = await self.page.request.get("http://localhost:8000/api/v1/products/manufacturers/?limit=5")
            manufacturers_ok = response.status == 200
            
            if manufacturers_ok:
                manufacturers_data = await response.json()
                has_manufacturers = "manufacturers" in manufacturers_data and len(manufacturers_data["manufacturers"]) > 0
            else:
                has_manufacturers = False
            
            # Test categories endpoint
            response = await self.page.request.get("http://localhost:8000/api/v1/products/categories/?limit=5")
            categories_ok = response.status == 200
            
            if categories_ok:
                categories_data = await response.json()
                has_categories = "categories" in categories_data and len(categories_data["categories"]) > 0
            else:
                has_categories = False
            
            # Test filtered suggestions
            response = await self.page.request.get("http://localhost:8000/api/v1/search/suggestions?q=comp&manufactured_by=Hu-Friedy")
            suggestions_ok = response.status == 200
            
            success = manufacturers_ok and categories_ok and suggestions_ok and has_manufacturers and has_categories
            details = f"Manufacturers: {manufacturers_ok}, Categories: {categories_ok}, Suggestions: {suggestions_ok}"
            
            await self.log_test("API endpoints working", success, details)
            return success
        except Exception as e:
            await self.log_test("API endpoints working", False, f"Error: {e}")
            return False

    async def run_all_tests(self):
        """Run all tests"""
        print("🧪 Starting comprehensive filter functionality tests...")
        print("=" * 60)
        
        await self.setup()
        
        tests = [
            self.test_homepage_loads,
            self.test_manufacturers_filter_display,
            self.test_filter_dropdowns_load_data,
            self.test_search_with_filters,
            self.test_filtered_suggestions,
            self.test_api_endpoints,
        ]
        
        passed = 0
        total = len(tests)
        
        for test in tests:
            try:
                result = await test()
                if result:
                    passed += 1
                await asyncio.sleep(1)  # Brief pause between tests
            except Exception as e:
                print(f"❌ Test failed with exception: {e}")
        
        print("=" * 60)
        print(f"🎯 Test Results: {passed}/{total} tests passed")
        
        if passed == total:
            print("🎉 ALL TESTS PASSED! Filter functionality is working correctly.")
        else:
            print("⚠️  Some tests failed. Check the details above.")
        
        print("\n📋 Manual Testing Instructions:")
        print("1. The browser window is open for manual testing")
        print("2. Navigate to http://localhost:3000")
        print("3. Test the following manually:")
        print("   - Select manufacturers (not brands) from dropdown")
        print("   - Select categories from dropdown")
        print("   - Type in search and verify suggestions are filtered")
        print("   - Perform search and verify results match filters")
        print("4. Press Enter when done to close the browser")
        
        # Keep browser open for manual testing
        input("Press Enter to close browser and finish testing...")
        
        await self.browser.close()
        return passed == total

async def main():
    """Main test runner"""
    tester = FilterFunctionalityTester()
    success = await tester.run_all_tests()
    return 0 if success else 1

if __name__ == "__main__":
    try:
        exit_code = asyncio.run(main())
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n🛑 Tests interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"❌ Test runner failed: {e}")
        sys.exit(1)

"""
Shopping List API routes for CRUD operations
"""

from fastapi import APIRouter, Depends, HTTPException, status, Query, Path
from asyncpg import Connection
import redis.asyncio as redis

try:
    from ..database import get_db_connection, get_redis_client
    from ..services.shopping_list_service import ShoppingListService
    from ..schemas.shopping_list import (
        ShoppingListCreate, ShoppingListUpdate, ShoppingListResponse,
        ShoppingListDetailResponse, ShoppingListsResponse,
        ShoppingListItemCreate, ShoppingListItemUpdate, ShoppingListItemResponse,
        ShoppingListAnalysisResponse, APIResponse
    )
    from ..dependencies import get_current_active_user, api_rate_limiter, create_paginated_response
    from ..models.user import User
    from ..middleware.authorization import require_permission
except ImportError:
    from database import get_db_connection, get_redis_client
    from services.shopping_list_service import ShoppingListService
    from schemas.shopping_list import (
        ShoppingListCreate, ShoppingListUpdate, ShoppingListResponse,
        ShoppingListDetailResponse, ShoppingListsResponse,
        ShoppingListItemCreate, ShoppingListItemUpdate, ShoppingListItemResponse,
        ShoppingListAnalysisResponse, APIResponse
    )
    from dependencies import get_current_active_user, api_rate_limiter, create_paginated_response
    from models.user import User
    from middleware.authorization import require_permission

# Create router
router = APIRouter(prefix="/shopping-lists", tags=["shopping-lists"])


# Custom dependencies for shopping list operations
async def require_shopping_list_read(current_user: User = Depends(get_current_active_user)) -> User:
    """Dependency to require shopping list read permissions."""
    if not current_user.has_permission("shopping_lists.read") and not current_user.is_superuser:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Insufficient permissions. Shopping list access requires 'shopping_lists.read' permission."
        )
    return current_user


async def require_shopping_list_create(current_user: User = Depends(get_current_active_user)) -> User:
    """Dependency to require shopping list create permissions."""
    if not current_user.has_permission("shopping_lists.create") and not current_user.is_superuser:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Insufficient permissions. Creating shopping lists requires 'shopping_lists.create' permission."
        )
    return current_user


async def require_shopping_list_update(current_user: User = Depends(get_current_active_user)) -> User:
    """Dependency to require shopping list update permissions."""
    if not current_user.has_permission("shopping_lists.update") and not current_user.is_superuser:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Insufficient permissions. Updating shopping lists requires 'shopping_lists.update' permission."
        )
    return current_user


async def require_shopping_list_delete(current_user: User = Depends(get_current_active_user)) -> User:
    """Dependency to require shopping list delete permissions."""
    if not current_user.has_permission("shopping_lists.delete") and not current_user.is_superuser:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Insufficient permissions. Deleting shopping lists requires 'shopping_lists.delete' permission."
        )
    return current_user


@router.get("/test", response_model=dict)
async def test_endpoint(
    current_user: User = Depends(get_current_active_user)
):
    """Test endpoint to check if authentication and basic response work."""
    return {
        "success": True,
        "message": "Test endpoint working",
        "user_id": current_user.id,
        "user_email": current_user.email
    }


@router.post("/", response_model=ShoppingListResponse, status_code=status.HTTP_201_CREATED)
async def create_shopping_list(
    list_data: ShoppingListCreate,
    current_user: User = Depends(require_shopping_list_create),
    conn: Connection = Depends(get_db_connection),
    redis_client: redis.Redis = Depends(get_redis_client)
):
    """
    Create a new shopping list.
    
    Creates a new shopping list for the authenticated user with the provided
    name and optional description.
    """
    try:
        # Apply rate limiting
        await api_rate_limiter(current_user.id, redis_client)
        
        shopping_list = await ShoppingListService.create_shopping_list(
            conn, current_user.id, list_data
        )
        
        return shopping_list
    
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to create shopping list: {str(e)}"
        )


@router.get("/", response_model=dict)
async def get_shopping_lists(
    page: int = Query(1, ge=1, description="Page number"),
    per_page: int = Query(20, ge=1, le=100, description="Items per page"),
    current_user: User = Depends(require_shopping_list_read),
    conn: Connection = Depends(get_db_connection),
    redis_client: redis.Redis = Depends(get_redis_client)
):
    """
    Get user's shopping lists with pagination.

    Returns all shopping lists belonging to the authenticated user,
    with optional filtering for active lists only.
    """
    try:
        offset = (page - 1) * per_page

        # Temporarily bypass Redis caching to fix hanging issue
        from ..models.shopping_list import ShoppingListRepository, ShoppingListItemRepository
        from ..schemas.shopping_list import ShoppingListResponse

        # Get from database directly
        shopping_lists, total = await ShoppingListRepository.get_user_shopping_lists(
            conn, current_user.id, per_page, offset
        )

        # Convert to response format with summary data
        list_responses = []
        for shopping_list in shopping_lists:
            # Get summary data for each shopping list
            summary = await ShoppingListItemRepository.get_list_summary(conn, shopping_list.id)

            # Create response with summary included
            response_data = {
                "id": shopping_list.id,
                "user_id": shopping_list.user_id,
                "name": shopping_list.name,
                "description": shopping_list.description,
                "is_default": shopping_list.is_default,
                "created_at": shopping_list.created_at,
                "updated_at": shopping_list.updated_at,
                "summary": summary
            }
            list_responses.append(response_data)

        return create_paginated_response(
            items=list_responses,
            total=total,
            page=page,
            per_page=per_page,
            message="Shopping lists retrieved successfully"
        )

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get shopping lists: {str(e)}"
        )


@router.get("/{list_id}", response_model=ShoppingListDetailResponse)
async def get_shopping_list(
    list_id: int = Path(..., description="Shopping list ID"),
    include_purchased: bool = Query(True, description="Include purchased items"),
    current_user: User = Depends(require_shopping_list_read),
    conn: Connection = Depends(get_db_connection),
    redis_client: redis.Redis = Depends(get_redis_client)
):
    """
    Get detailed shopping list with items.
    
    Returns a shopping list with all its items and summary statistics.
    Optionally excludes purchased items from the response.
    """
    try:
        shopping_list = await ShoppingListService.get_shopping_list_detail(
            conn, redis_client, list_id, current_user.id, include_purchased
        )
        
        if not shopping_list:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Shopping list not found"
            )
        
        return shopping_list
    
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get shopping list: {str(e)}"
        )


@router.put("/{list_id}", response_model=ShoppingListResponse)
async def update_shopping_list(
    list_id: int = Path(..., description="Shopping list ID"),
    update_data: ShoppingListUpdate = ...,
    current_user: User = Depends(require_shopping_list_update),
    conn: Connection = Depends(get_db_connection),
    redis_client: redis.Redis = Depends(get_redis_client)
):
    """
    Update shopping list.
    
    Updates the name, description, or active status of a shopping list.
    Only the list owner can update the list.
    """
    try:
        shopping_list = await ShoppingListService.update_shopping_list(
            conn, redis_client, list_id, current_user.id, update_data
        )
        
        if not shopping_list:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Shopping list not found"
            )
        
        return shopping_list
    
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to update shopping list: {str(e)}"
        )


@router.delete("/{list_id}", response_model=APIResponse)
async def delete_shopping_list(
    list_id: int = Path(..., description="Shopping list ID"),
    current_user: User = Depends(require_shopping_list_delete),
    conn: Connection = Depends(get_db_connection),
    redis_client: redis.Redis = Depends(get_redis_client)
):
    """
    Delete shopping list.
    
    Soft deletes a shopping list by setting is_active to False.
    Only the list owner can delete the list.
    """
    try:
        success = await ShoppingListService.delete_shopping_list(
            conn, redis_client, list_id, current_user.id
        )
        
        if not success:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Shopping list not found"
            )
        
        return APIResponse(
            success=True,
            message="Shopping list deleted successfully"
        )
    
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to delete shopping list: {str(e)}"
        )


@router.post("/{list_id}/items", response_model=ShoppingListItemResponse, status_code=status.HTTP_201_CREATED)
async def add_item_to_list(
    list_id: int = Path(..., description="Shopping list ID"),
    item_data: ShoppingListItemCreate = ...,
    current_user: User = Depends(require_shopping_list_update),
    conn: Connection = Depends(get_db_connection),
    redis_client: redis.Redis = Depends(get_redis_client)
):
    """
    Add item to shopping list.
    
    Adds a product to the shopping list with specified quantity and optional notes.
    The product must exist in the database.
    """
    try:
        item = await ShoppingListService.add_item_to_list(
            conn, redis_client, list_id, current_user.id, item_data
        )
        
        if not item:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Shopping list not found or product does not exist"
            )
        
        return item
    
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to add item to list: {str(e)}"
        )


@router.put("/items/{item_id}", response_model=ShoppingListItemResponse)
async def update_list_item(
    item_id: int = Path(..., description="Shopping list item ID"),
    update_data: ShoppingListItemUpdate = ...,
    current_user: User = Depends(require_shopping_list_update),
    conn: Connection = Depends(get_db_connection),
    redis_client: redis.Redis = Depends(get_redis_client)
):
    """
    Update shopping list item.
    
    Updates the quantity, notes, or purchased status of a shopping list item.
    """
    try:
        item = await ShoppingListService.update_list_item(
            conn, redis_client, item_id, update_data
        )
        
        if not item:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Shopping list item not found"
            )
        
        return item
    
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to update list item: {str(e)}"
        )


@router.delete("/{list_id}/items/{item_id}", response_model=APIResponse)
async def remove_item_from_list(
    list_id: int = Path(..., description="Shopping list ID"),
    item_id: int = Path(..., description="Shopping list item ID"),
    current_user: User = Depends(require_shopping_list_update),
    conn: Connection = Depends(get_db_connection),
    redis_client: redis.Redis = Depends(get_redis_client)
):
    """
    Remove item from shopping list.
    
    Permanently removes an item from the shopping list.
    """
    try:
        success = await ShoppingListService.remove_item_from_list(
            conn, redis_client, item_id, list_id
        )
        
        if not success:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Shopping list item not found"
            )
        
        return APIResponse(
            success=True,
            message="Item removed from shopping list successfully"
        )
    
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to remove item from list: {str(e)}"
        )


@router.get("/{list_id}/analysis", response_model=ShoppingListAnalysisResponse)
async def analyze_shopping_list(
    list_id: int = Path(..., description="Shopping list ID"),
    current_user: User = Depends(require_shopping_list_read),
    conn: Connection = Depends(get_db_connection),
    redis_client: redis.Redis = Depends(get_redis_client)
):
    """
    Analyze shopping list prices and costs.
    
    Provides detailed price analysis including total estimated cost,
    breakdown by seller, and potential savings opportunities.
    """
    try:
        analysis = await ShoppingListService.analyze_shopping_list_prices(
            conn, redis_client, list_id, current_user.id
        )
        
        if not analysis:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Shopping list not found"
            )
        
        return ShoppingListAnalysisResponse(
            shopping_list_id=list_id,
            analysis=analysis
        )
    
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to analyze shopping list: {str(e)}"
        )


@router.post("/quick-add", response_model=ShoppingListItemResponse, status_code=status.HTTP_201_CREATED)
async def quick_add_product_to_list(
    product_id: int,
    quantity: int = 1,
    current_user: User = Depends(get_current_active_user),
    conn: Connection = Depends(get_db_connection),
    redis_client: redis.Redis = Depends(get_redis_client)
):
    """
    Quick-add a product to the user's default shopping list.

    This endpoint provides a streamlined way to add products from search suggestions
    directly to a shopping list. If the user doesn't have a default shopping list,
    one will be created automatically.

    **Features:**
    - Automatically creates a default shopping list if none exists
    - Adds product with specified quantity (default: 1)
    - Returns the created shopping list item with product details
    - Optimized for quick product addition from search suggestions
    """
    try:
        # Apply rate limiting
        await api_rate_limiter(current_user.id, redis_client)

        # Quick-add product to default shopping list
        item = await ShoppingListService.quick_add_product_to_default_list(
            conn, redis_client, current_user.id, product_id, quantity
        )

        if not item:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Product not found"
            )

        return item

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to add product to shopping list: {str(e)}"
        )


# Health check for shopping lists service
@router.get("/health/", response_model=APIResponse)
async def shopping_lists_health_check(
    conn: Connection = Depends(get_db_connection),
    redis_client: redis.Redis = Depends(get_redis_client)
):
    """Shopping lists service health check."""
    try:
        # Test database connection
        list_count = await conn.fetchval("SELECT COUNT(*) FROM shopping_lists LIMIT 1")
        
        # Test Redis connection
        await redis_client.ping()
        
        return APIResponse(
            success=True,
            message="Shopping lists service is healthy",
            data={
                "database_connection": "healthy",
                "redis_connection": "healthy",
                "sample_list_count": list_count or 0
            }
        )
    
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail=f"Shopping lists service unhealthy: {str(e)}"
        )

"""
UserRole database models and operations for RBAC system
Manages many-to-many relationships between users and roles
"""

import uuid
from datetime import datetime
from typing import Optional, List, Dict, Any
from asyncpg import Connection


class UserRole:
    """UserRole model for managing user-role relationships."""
    
    def __init__(
        self,
        id: str = None,
        user_id: str = None,
        role_id: str = None,
        assigned_by: str = None,
        assigned_at: datetime = None,
        expires_at: datetime = None,
        is_active: bool = True,
        notes: str = None,
        created_at: datetime = None,
        updated_at: datetime = None
    ):
        self.id = id or str(uuid.uuid4())
        self.user_id = user_id
        self.role_id = role_id
        self.assigned_by = assigned_by
        self.assigned_at = assigned_at
        self.expires_at = expires_at
        self.is_active = is_active
        self.notes = notes
        self.created_at = created_at
        self.updated_at = updated_at
    
    @classmethod
    def from_record(cls, record) -> "UserRole":
        """Create UserRole instance from database record."""
        if not record:
            return None
        
        return cls(
            id=str(record["id"]),
            user_id=str(record["user_id"]),
            role_id=str(record["role_id"]),
            assigned_by=str(record["assigned_by"]) if record["assigned_by"] else None,
            assigned_at=record["assigned_at"],
            expires_at=record["expires_at"],
            is_active=record["is_active"],
            notes=record["notes"],
            created_at=record["created_at"],
            updated_at=record["updated_at"]
        )
    
    def to_dict(self) -> dict:
        """Convert user role to dictionary."""
        return {
            "id": self.id,
            "user_id": self.user_id,
            "role_id": self.role_id,
            "assigned_by": self.assigned_by,
            "assigned_at": self.assigned_at.isoformat() if self.assigned_at else None,
            "expires_at": self.expires_at.isoformat() if self.expires_at else None,
            "is_active": self.is_active,
            "notes": self.notes,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None,
        }
    
    def is_expired(self) -> bool:
        """Check if the role assignment has expired."""
        if not self.expires_at:
            return False
        return datetime.utcnow() > self.expires_at


class UserRoleRepository:
    """UserRole database operations."""
    
    @staticmethod
    async def assign_role(
        conn: Connection,
        user_id: str,
        role_id: str,
        assigned_by: str = None,
        expires_at: datetime = None,
        notes: str = None
    ) -> UserRole:
        """Assign a role to a user."""
        user_role_id = str(uuid.uuid4())
        
        query = """
            INSERT INTO user_roles (id, user_id, role_id, assigned_by, expires_at, notes)
            VALUES ($1, $2, $3, $4, $5, $6)
            ON CONFLICT (user_id, role_id) DO UPDATE SET
                is_active = TRUE,
                assigned_by = EXCLUDED.assigned_by,
                assigned_at = NOW(),
                expires_at = EXCLUDED.expires_at,
                notes = EXCLUDED.notes,
                updated_at = NOW()
            RETURNING *
        """
        
        record = await conn.fetchrow(
            query, 
            user_role_id, 
            user_id, 
            role_id, 
            assigned_by, 
            expires_at, 
            notes
        )
        return UserRole.from_record(record)
    
    @staticmethod
    async def remove_role(conn: Connection, user_id: str, role_id: str) -> bool:
        """Remove a role from a user (soft delete)."""
        query = """
            UPDATE user_roles 
            SET is_active = FALSE, updated_at = NOW()
            WHERE user_id = $1 AND role_id = $2
        """
        
        result = await conn.execute(query, user_id, role_id)
        return "UPDATE 1" in result
    
    @staticmethod
    async def get_user_role(
        conn: Connection, 
        user_id: str, 
        role_id: str
    ) -> Optional[UserRole]:
        """Get specific user-role relationship."""
        query = "SELECT * FROM user_roles WHERE user_id = $1 AND role_id = $2"
        record = await conn.fetchrow(query, user_id, role_id)
        return UserRole.from_record(record)
    
    @staticmethod
    async def get_user_roles(
        conn: Connection, 
        user_id: str, 
        active_only: bool = True
    ) -> List[UserRole]:
        """Get all role assignments for a user."""
        where_clause = "WHERE user_id = $1"
        if active_only:
            where_clause += " AND is_active = TRUE"
        
        query = f"""
            SELECT * FROM user_roles 
            {where_clause}
            ORDER BY assigned_at DESC
        """
        
        records = await conn.fetch(query, user_id)
        return [UserRole.from_record(record) for record in records]
    
    @staticmethod
    async def get_role_users(
        conn: Connection, 
        role_id: str, 
        active_only: bool = True
    ) -> List[UserRole]:
        """Get all user assignments for a role."""
        where_clause = "WHERE role_id = $1"
        if active_only:
            where_clause += " AND is_active = TRUE"
        
        query = f"""
            SELECT * FROM user_roles 
            {where_clause}
            ORDER BY assigned_at DESC
        """
        
        records = await conn.fetch(query, role_id)
        return [UserRole.from_record(record) for record in records]
    
    @staticmethod
    async def update_role_assignment(
        conn: Connection,
        user_id: str,
        role_id: str,
        expires_at: datetime = None,
        notes: str = None,
        is_active: bool = None
    ) -> Optional[UserRole]:
        """Update role assignment details."""
        # Build dynamic update query
        updates = []
        params = []
        param_count = 1
        
        if expires_at is not None:
            updates.append(f"expires_at = ${param_count}")
            params.append(expires_at)
            param_count += 1
        
        if notes is not None:
            updates.append(f"notes = ${param_count}")
            params.append(notes)
            param_count += 1
        
        if is_active is not None:
            updates.append(f"is_active = ${param_count}")
            params.append(is_active)
            param_count += 1
        
        if not updates:
            return await UserRoleRepository.get_user_role(conn, user_id, role_id)
        
        updates.append("updated_at = NOW()")
        params.extend([user_id, role_id])
        
        query = f"""
            UPDATE user_roles 
            SET {', '.join(updates)}
            WHERE user_id = ${param_count} AND role_id = ${param_count + 1}
            RETURNING *
        """
        
        record = await conn.fetchrow(query, *params)
        return UserRole.from_record(record)
    
    @staticmethod
    async def get_expired_assignments(conn: Connection) -> List[UserRole]:
        """Get all expired role assignments."""
        query = """
            SELECT * FROM user_roles 
            WHERE expires_at IS NOT NULL 
            AND expires_at < NOW() 
            AND is_active = TRUE
            ORDER BY expires_at
        """
        
        records = await conn.fetch(query)
        return [UserRole.from_record(record) for record in records]
    
    @staticmethod
    async def deactivate_expired_assignments(conn: Connection) -> int:
        """Deactivate all expired role assignments."""
        query = """
            UPDATE user_roles 
            SET is_active = FALSE, updated_at = NOW()
            WHERE expires_at IS NOT NULL 
            AND expires_at < NOW() 
            AND is_active = TRUE
        """
        
        result = await conn.execute(query)
        # Extract number from result like "UPDATE 5"
        return int(result.split()[-1]) if result.startswith("UPDATE") else 0
    
    @staticmethod
    async def get_user_roles_with_details(
        conn: Connection, 
        user_id: str, 
        active_only: bool = True
    ) -> List[Dict[str, Any]]:
        """Get user roles with role details."""
        where_clause = "WHERE ur.user_id = $1"
        if active_only:
            where_clause += " AND ur.is_active = TRUE AND r.is_active = TRUE"
        
        query = f"""
            SELECT 
                ur.*,
                r.name as role_name,
                r.display_name as role_display_name,
                r.description as role_description
            FROM user_roles ur
            JOIN roles r ON ur.role_id = r.id
            {where_clause}
            ORDER BY ur.assigned_at DESC
        """
        
        records = await conn.fetch(query, user_id)
        return [dict(record) for record in records]
    
    @staticmethod
    async def get_role_users_with_details(
        conn: Connection, 
        role_id: str, 
        active_only: bool = True
    ) -> List[Dict[str, Any]]:
        """Get role users with user details."""
        where_clause = "WHERE ur.role_id = $1"
        if active_only:
            where_clause += " AND ur.is_active = TRUE AND u.is_active = TRUE"
        
        query = f"""
            SELECT 
                ur.*,
                u.email as user_email,
                u.full_name as user_full_name,
                u.is_superuser as user_is_superuser
            FROM user_roles ur
            JOIN users u ON ur.user_id = u.id
            {where_clause}
            ORDER BY ur.assigned_at DESC
        """
        
        records = await conn.fetch(query, role_id)
        return [dict(record) for record in records]

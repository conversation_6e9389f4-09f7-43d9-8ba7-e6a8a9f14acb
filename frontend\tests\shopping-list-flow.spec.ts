import { test, expect } from '@playwright/test';

test.describe('Shopping List Complete User Flow', () => {
  test.beforeEach(async ({ page }) => {
    // Navigate to the application
    await page.goto('/');
    
    // Wait for the page to load
    await page.waitForLoadState('networkidle');
  });

  test('Complete shopping list flow: search → suggestion → add to list → UI updates', async ({ page }) => {
    // Step 1: Verify initial state
    console.log('🧪 Step 1: Verifying initial state...');
    
    // Check that shopping list icon is present in header
    const shoppingListIcon = page.locator('[data-testid="shopping-list-icon"]').or(
      page.locator('button').filter({ hasText: /shopping/i }).first()
    ).or(
      page.locator('svg[data-lucide="shopping-cart"]').locator('..')
    );
    
    await expect(shoppingListIcon).toBeVisible();
    
    // Check initial item count (should be 0 or hidden)
    const itemBadge = page.locator('.bg-primary-600').filter({ hasText: /^\d+$/ });
    const badgeCount = await itemBadge.count();
    console.log(`Initial shopping list items: ${badgeCount > 0 ? await itemBadge.textContent() : '0'}`);
    
    // Step 2: Perform search
    console.log('🧪 Step 2: Performing search...');
    
    // Find search input (could be in header or main search interface)
    const searchInput = page.locator('input[type="text"]').filter({ hasText: '' }).first().or(
      page.locator('input[placeholder*="search" i]')
    ).or(
      page.locator('input[placeholder*="Search" i]')
    );
    
    await expect(searchInput).toBeVisible();
    
    // Type search query
    await searchInput.fill('dental');
    await page.waitForTimeout(500); // Wait for debounced search
    
    // Step 3: Wait for and verify suggestions
    console.log('🧪 Step 3: Waiting for search suggestions...');
    
    // Wait for suggestions to appear
    const suggestionsContainer = page.locator('[data-testid="suggestions"]').or(
      page.locator('.suggestions').or(
        page.locator('div').filter({ hasText: /suggestions/i }).first()
      )
    );
    
    // Wait for suggestions with timeout
    await page.waitForTimeout(1000);
    
    // Look for suggestion items (they should show product names only)
    const suggestionItems = page.locator('button').filter({ hasText: /dental/i });
    const suggestionCount = await suggestionItems.count();
    
    console.log(`Found ${suggestionCount} suggestions`);
    
    if (suggestionCount === 0) {
      console.log('⚠️ No suggestions found, checking for alternative selectors...');
      
      // Try alternative selectors
      const altSuggestions = page.locator('div').filter({ hasText: /dental/i });
      const altCount = await altSuggestions.count();
      console.log(`Alternative suggestions found: ${altCount}`);
      
      // Take screenshot for debugging
      await page.screenshot({ path: 'test-results/no-suggestions-debug.png', fullPage: true });
    }
    
    // Step 4: Add product to shopping list
    console.log('🧪 Step 4: Adding product to shopping list...');
    
    if (suggestionCount > 0) {
      // Look for the "+" button on the first suggestion
      const firstSuggestion = suggestionItems.first();
      const addButton = firstSuggestion.locator('..').locator('button').filter({ hasText: '+' }).or(
        firstSuggestion.locator('..').locator('[data-testid="add-to-list"]')
      );
      
      // Get initial shopping list count
      const initialBadgeVisible = await itemBadge.isVisible();
      const initialCount = initialBadgeVisible ? parseInt(await itemBadge.textContent() || '0') : 0;
      
      console.log(`Initial count: ${initialCount}`);
      
      // Click add button
      await addButton.click();
      
      // Step 5: Verify UI updates
      console.log('🧪 Step 5: Verifying UI updates...');
      
      // Wait for animation and UI updates
      await page.waitForTimeout(1000);
      
      // Check if shopping list icon shows animation or updated count
      const updatedBadge = page.locator('.bg-primary-600').filter({ hasText: /^\d+$/ });
      const updatedBadgeVisible = await updatedBadge.isVisible();
      
      if (updatedBadgeVisible) {
        const updatedCount = parseInt(await updatedBadge.textContent() || '0');
        console.log(`Updated count: ${updatedCount}`);
        
        // Verify count increased
        expect(updatedCount).toBeGreaterThan(initialCount);
      }
      
      // Check for success notification
      const notification = page.locator('.fixed').filter({ hasText: /added/i }).or(
        page.locator('[data-testid="notification"]')
      );
      
      if (await notification.isVisible()) {
        console.log('✅ Success notification appeared');
      }
      
      // Verify search input was cleared
      const searchValue = await searchInput.inputValue();
      expect(searchValue).toBe('');
      console.log('✅ Search input cleared after addition');
      
      // Step 6: Test shopping list dropdown
      console.log('🧪 Step 6: Testing shopping list dropdown...');
      
      // Click shopping list icon to open dropdown
      await shoppingListIcon.click();
      
      // Wait for dropdown to appear
      await page.waitForTimeout(500);
      
      // Look for dropdown content
      const dropdown = page.locator('.absolute').filter({ hasText: /shopping list/i }).or(
        page.locator('[data-testid="shopping-list-dropdown"]')
      );
      
      if (await dropdown.isVisible()) {
        console.log('✅ Shopping list dropdown opened');
        
        // Check for product in dropdown (should show only product name)
        const productInDropdown = dropdown.locator('div').filter({ hasText: /dental/i });
        await expect(productInDropdown).toBeVisible();
        console.log('✅ Product visible in dropdown');
        
        // Take screenshot of dropdown
        await page.screenshot({ path: 'test-results/shopping-list-dropdown.png', fullPage: true });
      }
      
      console.log('🎉 Complete user flow test passed!');
      
    } else {
      console.log('⚠️ Skipping add-to-list test due to no suggestions');
    }
    
    // Final screenshot
    await page.screenshot({ path: 'test-results/final-state.png', fullPage: true });
  });

  test('Performance validation', async ({ page }) => {
    console.log('⚡ Testing performance...');
    
    // Navigate and measure load time
    const startTime = Date.now();
    await page.goto('/');
    await page.waitForLoadState('networkidle');
    const loadTime = Date.now() - startTime;
    
    console.log(`Page load time: ${loadTime}ms`);
    expect(loadTime).toBeLessThan(5000); // 5 second max load time
    
    // Test search performance
    const searchInput = page.locator('input[type="text"]').first();
    await searchInput.fill('dental');
    
    const searchStart = Date.now();
    await page.waitForTimeout(1000); // Wait for search results
    const searchTime = Date.now() - searchStart;
    
    console.log(`Search response time: ${searchTime}ms`);
    expect(searchTime).toBeLessThan(2000); // 2 second max search time
    
    console.log('✅ Performance validation passed');
  });

  test('UI responsiveness', async ({ page }) => {
    console.log('📱 Testing UI responsiveness...');
    
    // Test mobile viewport
    await page.setViewportSize({ width: 375, height: 667 });
    await page.goto('/');
    await page.waitForLoadState('networkidle');
    
    // Verify shopping list icon is visible on mobile
    const shoppingListIcon = page.locator('svg[data-lucide="shopping-cart"]').locator('..');
    await expect(shoppingListIcon).toBeVisible();
    
    // Test tablet viewport
    await page.setViewportSize({ width: 768, height: 1024 });
    await page.reload();
    await page.waitForLoadState('networkidle');
    
    await expect(shoppingListIcon).toBeVisible();
    
    // Test desktop viewport
    await page.setViewportSize({ width: 1920, height: 1080 });
    await page.reload();
    await page.waitForLoadState('networkidle');
    
    await expect(shoppingListIcon).toBeVisible();
    
    console.log('✅ UI responsiveness test passed');
  });
});

"""
Product schemas for request/response validation
"""

from datetime import datetime
from typing import Optional, List, Dict, Any
from pydantic import BaseModel, Field, validator


class ProductBase(BaseModel):
    """Base product schema."""
    mfr: str = Field(..., description="Manufacturer code")
    name: str = Field(..., description="Product name")
    url: str = Field(..., description="Product URL")
    maincat: Optional[str] = Field(None, description="Main category")
    brand: Optional[str] = Field(None, description="Brand name")
    manufactured_by: Optional[str] = Field(None, description="Manufacturer name")
    category: Optional[str] = Field(None, description="Product category")
    seller: str = Field(..., description="Seller name")
    price: Optional[str] = Field(None, description="Product price")


class ProductCreate(ProductBase):
    """Schema for product creation."""
    search_text: str = Field(..., description="Searchable text content")


class ProductUpdate(BaseModel):
    """Schema for product updates."""
    name: Optional[str] = None
    url: Optional[str] = None
    maincat: Optional[str] = None
    brand: Optional[str] = None
    manufactured_by: Optional[str] = None
    category: Optional[str] = None
    price: Optional[str] = None
    search_text: Optional[str] = None


class ProductResponse(ProductBase):
    """Schema for product response."""
    id: int
    search_text: str
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
    
    class Config:
        from_attributes = True


class ProductSearchRequest(BaseModel):
    """Schema for product search request."""
    q: str = Field(..., min_length=1, max_length=200, description="Search query")
    limit: int = Field(20, ge=1, le=100, description="Maximum results")
    offset: int = Field(0, ge=0, description="Results offset")
    search_type: str = Field("fulltext", pattern="^(fulltext|similarity|hybrid)$", description="Search type")
    
    # Filters
    category: Optional[str] = Field(None, description="Filter by category")
    brand: Optional[str] = Field(None, description="Filter by brand")
    seller: Optional[str] = Field(None, description="Filter by seller")
    manufactured_by: Optional[str] = Field(None, description="Filter by manufacturer")


class ProductSearchFilters(BaseModel):
    """Schema for search filters."""
    category: Optional[str] = None
    brand: Optional[str] = None
    seller: Optional[str] = None
    manufactured_by: Optional[str] = None


class ProductSearchResult(BaseModel):
    """Schema for individual search result."""
    id: int
    mfr: str
    name: str
    url: str
    maincat: Optional[str] = None
    brand: Optional[str] = None
    manufactured_by: Optional[str] = None
    category: Optional[str] = None
    seller: str
    price: Optional[str] = None
    search_text: str
    
    # Search-specific fields
    rank: Optional[float] = Field(None, description="Search relevance rank")
    similarity_score: Optional[float] = Field(None, description="Similarity score")


class ProductSearchResponse(BaseModel):
    """Schema for product search response."""
    success: bool = True
    message: str = "Search completed successfully"
    query: str
    search_type: str
    total: int
    results: List[ProductSearchResult]
    pagination: Dict[str, Any]
    filters_applied: Optional[Dict[str, Any]] = None
    search_time_ms: Optional[float] = None


class ProductSuggestion(BaseModel):
    """Schema for individual product suggestion."""
    id: int
    mfr: str
    name: str
    seller: str
    price: Optional[str] = None
    url: str
    maincat: Optional[str] = None
    brand: Optional[str] = None
    manufactured_by: Optional[str] = None
    category: Optional[str] = None


class ProductSuggestionResponse(BaseModel):
    """Schema for search suggestions response."""
    success: bool = True
    message: str = "Suggestions retrieved successfully"
    query: str
    suggestions: List[ProductSuggestion]


class PopularSearchResponse(BaseModel):
    """Schema for popular searches response."""
    success: bool = True
    message: str = "Popular searches retrieved successfully"
    popular_searches: List[Dict[str, Any]]


class ProductMatchRequest(BaseModel):
    """Schema for product matching request."""
    mfr: str = Field(..., description="Manufacturer code to find matches for")
    limit: int = Field(20, ge=1, le=100, description="Maximum results")
    offset: int = Field(0, ge=0, description="Results offset")


class ProductMatchResponse(BaseModel):
    """Schema for product matching response."""
    success: bool = True
    message: str = "Product matches found successfully"
    mfr: str
    total: int
    products: List[ProductResponse]
    pagination: Dict[str, Any]


class SearchStatsResponse(BaseModel):
    """Schema for search statistics response."""
    success: bool = True
    message: str = "Search statistics retrieved successfully"
    stats: Dict[str, Any]


class ProductBulkImportRequest(BaseModel):
    """Schema for bulk product import."""
    products: List[ProductCreate]
    batch_size: int = Field(1000, ge=1, le=5000, description="Batch size for import")
    update_existing: bool = Field(False, description="Update existing products")


class ProductBulkImportResponse(BaseModel):
    """Schema for bulk import response."""
    success: bool = True
    message: str = "Bulk import completed successfully"
    imported: int
    updated: int
    errors: int
    error_details: Optional[List[str]] = None


class ProductCategoryResponse(BaseModel):
    """Schema for product categories response."""
    success: bool = True
    message: str = "Categories retrieved successfully"
    categories: List[Dict[str, Any]]


class ProductBrandResponse(BaseModel):
    """Schema for product brands response. DEPRECATED: Use ProductManufacturerResponse instead."""
    success: bool = True
    message: str = "Brands retrieved successfully"
    brands: List[Dict[str, Any]]


class ProductManufacturerResponse(BaseModel):
    """Schema for product manufacturers response."""
    success: bool = True
    message: str = "Manufacturers retrieved successfully"
    manufacturers: List[Dict[str, Any]]


class ProductSellerResponse(BaseModel):
    """Schema for product sellers response."""
    success: bool = True
    message: str = "Sellers retrieved successfully"
    sellers: List[Dict[str, Any]]

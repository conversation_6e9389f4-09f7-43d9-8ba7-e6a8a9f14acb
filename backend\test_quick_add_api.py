#!/usr/bin/env python3
"""
Test the quick-add shopping list API endpoint.
Validates that products can be quickly added to default shopping lists.
"""

import asyncio
import asyncpg
import os
import sys
import time
import json
from typing import List, Dict, Any

# Add the app directory to the path to import config
sys.path.append(os.path.join(os.path.dirname(__file__), 'app'))

try:
    from config import settings, get_database_url_sync
    from services.shopping_list_service import ShoppingListService
    from schemas.shopping_list import ShoppingListItemCreate
    from models.product import ProductRepository
    from database import DatabaseManager
    import redis.asyncio as redis
except ImportError:
    settings = None

class QuickAddAPITester:
    """Tests the quick-add shopping list API functionality."""
    
    def __init__(self):
        if settings:
            self.db_url = get_database_url_sync()
        else:
            self.db_url = "postgresql://profident_user:profident_dev_password@localhost:5433/profident_dev"
        
        print(f"🔗 Using database URL: {self.db_url[:50]}...")
    
    async def get_connection(self):
        """Get database connection."""
        return await asyncpg.connect(self.db_url)
    
    async def get_redis_client(self):
        """Get Redis client."""
        return redis.Redis(host='localhost', port=6380, db=0, decode_responses=True)
    
    async def test_quick_add_functionality(self):
        """Test the quick-add functionality."""
        print("🚀 Testing quick-add shopping list functionality...")
        
        conn = await self.get_connection()
        redis_client = await self.get_redis_client()
        
        try:
            # Get a sample product to add
            sample_product = await conn.fetchrow("""
                SELECT id, mfr, name, seller, price
                FROM products
                WHERE mfr IS NOT NULL AND mfr != ''
                LIMIT 1
            """)
            
            if not sample_product:
                print("❌ No products found in database")
                return
            
            print(f"📦 Testing with product:")
            print(f"   ID: {sample_product['id']}")
            print(f"   MFR: {sample_product['mfr']}")
            print(f"   Name: {sample_product['name'][:50]}...")
            print(f"   Seller: {sample_product['seller']}")
            
            # Test user ID (in real implementation, this would come from authentication)
            test_user_id = "test-user-123"
            
            # Test quick-add functionality
            print(f"\n🧪 Testing quick-add for user: {test_user_id}")
            
            start_time = time.time()
            
            # Call the quick-add service method
            result = await ShoppingListService.quick_add_product_to_default_list(
                conn, redis_client, test_user_id, sample_product['id'], quantity=2
            )
            
            add_time = (time.time() - start_time) * 1000
            
            if result:
                print(f"✅ Product added successfully in {add_time:.1f}ms")
                print(f"   Shopping List Item ID: {result.id}")
                print(f"   Shopping List ID: {result.shopping_list_id}")
                print(f"   Product ID: {result.product_id}")
                print(f"   Quantity: {result.quantity}")
                print(f"   Added at: {result.added_at}")
                
                if result.product:
                    print(f"   Product Details:")
                    print(f"     - Name: {result.product.name}")
                    print(f"     - MFR: {result.product.mfr}")
                    print(f"     - Seller: {result.product.seller}")
                    print(f"     - Price: {result.product.price}")
            else:
                print("❌ Failed to add product")
                return
            
            # Verify the shopping list was created/updated
            print(f"\n🔍 Verifying shopping list creation...")
            
            lists = await conn.fetch("""
                SELECT id, name, description, created_at
                FROM shopping_lists
                WHERE user_id = $1
                ORDER BY created_at DESC
            """, test_user_id)
            
            print(f"📊 User has {len(lists)} shopping list(s)")
            
            for i, shopping_list in enumerate(lists):
                print(f"   {i+1}. ID: {shopping_list['id']}")
                print(f"      Name: {shopping_list['name']}")
                print(f"      Description: {shopping_list['description']}")
                print(f"      Created: {shopping_list['created_at']}")
                
                # Get items in this list
                items = await conn.fetch("""
                    SELECT sli.*, p.name, p.mfr, p.seller
                    FROM shopping_list_items sli
                    JOIN products p ON sli.product_id = p.id
                    WHERE sli.shopping_list_id = $1
                """, shopping_list['id'])
                
                print(f"      Items: {len(items)}")
                for item in items:
                    print(f"        - {item['name']} (MFR: {item['mfr']}) x{item['quantity']}")
                print()
            
            # Test adding another product to the same list
            print(f"🧪 Testing second product addition...")
            
            second_product = await conn.fetchrow("""
                SELECT id, mfr, name, seller, price
                FROM products
                WHERE mfr IS NOT NULL AND mfr != '' AND id != $1
                LIMIT 1
            """, sample_product['id'])
            
            if second_product:
                result2 = await ShoppingListService.quick_add_product_to_default_list(
                    conn, redis_client, test_user_id, second_product['id'], quantity=1
                )
                
                if result2:
                    print(f"✅ Second product added successfully")
                    print(f"   Same shopping list: {result.shopping_list_id == result2.shopping_list_id}")
                else:
                    print("❌ Failed to add second product")
            
            # Clean up test data
            print(f"\n🧹 Cleaning up test data...")
            await conn.execute("DELETE FROM shopping_list_items WHERE shopping_list_id IN (SELECT id FROM shopping_lists WHERE user_id = $1)", test_user_id)
            await conn.execute("DELETE FROM shopping_lists WHERE user_id = $1", test_user_id)
            print("✅ Test data cleaned up")
        
        finally:
            await conn.close()
            await redis_client.close()

async def main():
    """Main function to test quick-add API."""
    tester = QuickAddAPITester()
    
    try:
        print("🎯 Testing Quick-Add Shopping List API")
        print("=" * 50)
        
        await tester.test_quick_add_functionality()
        
        print("\n🎉 Quick-add API testing completed successfully!")
        
    except Exception as e:
        print(f"❌ Error testing quick-add API: {e}")
        import traceback
        traceback.print_exc()
        raise

if __name__ == "__main__":
    asyncio.run(main())

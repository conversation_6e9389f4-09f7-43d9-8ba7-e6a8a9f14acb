#!/usr/bin/env python3
"""
Performance comparison: HTTP Endpoint Calls vs Direct Service Calls
"""

import asyncio
import httpx
import time
import statistics
import sys
from pathlib import Path

# Add the app directory to Python path
sys.path.insert(0, str(Path(__file__).parent / "app"))

from app.database import get_database
from app.services.search_service import SearchService
from app.services.auth_service import AuthService
from app.schemas.product import ProductSearchRequest


async def test_direct_service_calls():
    """Test direct service method calls."""
    print("🔧 Testing Direct Service Calls")
    print("=" * 50)
    
    try:
        # Get database connection
        db = await get_database()
        
        # Test 1: Direct Search Service Call
        print("1. Testing Direct Search Service Call...")
        search_times = []
        
        for i in range(10):
            async with db.get_connection() as conn:
                start_time = time.time()
                
                # Direct service call
                search_request = ProductSearchRequest(
                    q="dental",
                    limit=5,
                    page=1,
                    search_type="fulltext"
                )
                
                result = await SearchService.search_products(
                    conn, db.redis_client, search_request
                )
                
                end_time = time.time()
                response_time = (end_time - start_time) * 1000
                search_times.append(response_time)
                
                print(f"   Iteration {i+1}: {response_time:.2f}ms ({len(result.results)} results)")
        
        avg_direct_search = statistics.mean(search_times)
        print(f"   Average direct search time: {avg_direct_search:.2f}ms")
        
        # Test 2: Direct Auth Service Call (token verification)
        print("\n2. Testing Direct Auth Service Call...")
        auth_times = []
        
        # Create a test token first
        from app.utils.security import create_access_token
        test_token = create_access_token(data={"sub": "test-user", "email": "<EMAIL>"})
        
        for i in range(10):
            async with db.get_connection() as conn:
                start_time = time.time()
                
                try:
                    # Direct auth service call (will fail but we measure the time)
                    await AuthService.get_current_user(conn, test_token)
                except:
                    pass  # Expected to fail, we're measuring performance
                
                end_time = time.time()
                response_time = (end_time - start_time) * 1000
                auth_times.append(response_time)
                
                print(f"   Iteration {i+1}: {response_time:.2f}ms")
        
        avg_direct_auth = statistics.mean(auth_times)
        print(f"   Average direct auth time: {avg_direct_auth:.2f}ms")
        
        return {
            "search_avg": avg_direct_search,
            "auth_avg": avg_direct_auth,
            "search_times": search_times,
            "auth_times": auth_times
        }
        
    except Exception as e:
        print(f"❌ Direct service call test failed: {e}")
        return None


async def test_http_endpoint_calls():
    """Test HTTP endpoint calls."""
    print("\n🌐 Testing HTTP Endpoint Calls")
    print("=" * 50)
    
    base_url = "http://localhost:8000"
    
    try:
        async with httpx.AsyncClient(timeout=30.0) as client:
            
            # Test 1: HTTP Search Endpoint Call
            print("1. Testing HTTP Search Endpoint Call...")
            search_times = []
            
            for i in range(10):
                start_time = time.time()
                
                response = await client.get(f"{base_url}/api/v1/search/suggestions?q=dental&limit=5")
                
                end_time = time.time()
                response_time = (end_time - start_time) * 1000
                search_times.append(response_time)
                
                if response.status_code == 200:
                    data = response.json()
                    suggestions = data.get('suggestions', [])
                    print(f"   Iteration {i+1}: {response_time:.2f}ms ({len(suggestions)} results)")
                else:
                    print(f"   Iteration {i+1}: {response_time:.2f}ms (ERROR: {response.status_code})")
            
            avg_http_search = statistics.mean(search_times)
            print(f"   Average HTTP search time: {avg_http_search:.2f}ms")
            
            # Test 2: HTTP Auth Endpoint Call
            print("\n2. Testing HTTP Auth Endpoint Call...")
            auth_times = []
            
            for i in range(10):
                start_time = time.time()
                
                # Test CSRF token generation (lightweight auth operation)
                response = await client.get(f"{base_url}/api/v1/auth/csrf-token")
                
                end_time = time.time()
                response_time = (end_time - start_time) * 1000
                auth_times.append(response_time)
                
                if response.status_code == 200:
                    print(f"   Iteration {i+1}: {response_time:.2f}ms (SUCCESS)")
                else:
                    print(f"   Iteration {i+1}: {response_time:.2f}ms (ERROR: {response.status_code})")
            
            avg_http_auth = statistics.mean(auth_times)
            print(f"   Average HTTP auth time: {avg_http_auth:.2f}ms")
            
            return {
                "search_avg": avg_http_search,
                "auth_avg": avg_http_auth,
                "search_times": search_times,
                "auth_times": auth_times
            }
            
    except Exception as e:
        print(f"❌ HTTP endpoint call test failed: {e}")
        return None


async def analyze_performance_comparison():
    """Analyze and compare performance between both approaches."""
    print("\n📊 Performance Analysis")
    print("=" * 70)
    
    # Run tests
    direct_results = await test_direct_service_calls()
    http_results = await test_http_endpoint_calls()
    
    if not direct_results or not http_results:
        print("❌ Could not complete performance comparison")
        return
    
    print("\n🎯 Performance Comparison Results")
    print("=" * 70)
    
    # Search Performance Comparison
    search_overhead = http_results["search_avg"] - direct_results["search_avg"]
    search_overhead_percent = (search_overhead / direct_results["search_avg"]) * 100
    
    print(f"📈 Search Performance:")
    print(f"   Direct Service Call:  {direct_results['search_avg']:.2f}ms")
    print(f"   HTTP Endpoint Call:   {http_results['search_avg']:.2f}ms")
    print(f"   HTTP Overhead:        +{search_overhead:.2f}ms ({search_overhead_percent:.1f}%)")
    
    # Auth Performance Comparison
    auth_overhead = http_results["auth_avg"] - direct_results["auth_avg"]
    auth_overhead_percent = (auth_overhead / direct_results["auth_avg"]) * 100
    
    print(f"\n🔐 Auth Performance:")
    print(f"   Direct Service Call:  {direct_results['auth_avg']:.2f}ms")
    print(f"   HTTP Endpoint Call:   {http_results['auth_avg']:.2f}ms")
    print(f"   HTTP Overhead:        +{auth_overhead:.2f}ms ({auth_overhead_percent:.1f}%)")
    
    # Overall Analysis
    print(f"\n🔍 Analysis:")
    
    if search_overhead_percent < 50:
        print(f"   ✅ Search HTTP overhead is LOW ({search_overhead_percent:.1f}%)")
    elif search_overhead_percent < 100:
        print(f"   ⚠️  Search HTTP overhead is MODERATE ({search_overhead_percent:.1f}%)")
    else:
        print(f"   ❌ Search HTTP overhead is HIGH ({search_overhead_percent:.1f}%)")
    
    if auth_overhead_percent < 50:
        print(f"   ✅ Auth HTTP overhead is LOW ({auth_overhead_percent:.1f}%)")
    elif auth_overhead_percent < 100:
        print(f"   ⚠️  Auth HTTP overhead is MODERATE ({auth_overhead_percent:.1f}%)")
    else:
        print(f"   ❌ Auth HTTP overhead is HIGH ({auth_overhead_percent:.1f}%)")
    
    return {
        "direct": direct_results,
        "http": http_results,
        "search_overhead_percent": search_overhead_percent,
        "auth_overhead_percent": auth_overhead_percent
    }


async def main():
    """Run communication pattern performance tests."""
    print("🔬 Communication Patterns Performance Analysis")
    print("=" * 70)
    print("Comparing HTTP Endpoint Calls vs Direct Service Calls")
    
    results = await analyze_performance_comparison()
    
    if results:
        print("\n🎉 Performance Analysis Complete!")
        print("=" * 70)
        
        # Recommendations based on results
        print("\n💡 Recommendations:")
        
        if results["search_overhead_percent"] > 100:
            print("   🔧 Consider using direct service calls for internal search operations")
        else:
            print("   ✅ HTTP overhead for search is acceptable for most use cases")
        
        if results["auth_overhead_percent"] > 100:
            print("   🔧 Consider using direct service calls for internal auth operations")
        else:
            print("   ✅ HTTP overhead for auth is acceptable for most use cases")
        
        print("\n📋 Current ProfiDent Architecture:")
        print("   ✅ Already uses direct service calls internally")
        print("   ✅ HTTP endpoints are used only for external API access")
        print("   ✅ Optimal architecture pattern is already implemented")
    
    return results is not None


if __name__ == "__main__":
    success = asyncio.run(main())
    exit(0 if success else 1)

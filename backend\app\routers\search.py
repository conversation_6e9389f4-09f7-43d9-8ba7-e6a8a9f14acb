"""
Search API routes for fast full-text product search
Optimized for instant search performance
"""

import time
from fastapi import APIRouter, Depends, HTTPException, status, Query
from asyncpg import Connection
import redis.asyncio as redis

try:
    from ..database import get_db_connection, get_redis_client
    from ..services.search_service import SearchService
    from ..schemas.product import (
        ProductSearchRequest, ProductSearchResponse,
        ProductSuggestionResponse, PopularSearchResponse,
        SearchStatsResponse
    )
    from ..dependencies import search_rate_limiter, get_current_user
    from ..models.user import User
    from ..cache import cache_manager
    from ..middleware.authorization import require_permission
except ImportError:
    from database import get_db_connection, get_redis_client
    from services.search_service import SearchService
    from schemas.product import (
        ProductSearchRequest, ProductSearchResponse,
        ProductSuggestionResponse, PopularSearchResponse,
        SearchStatsResponse
    )
    from dependencies import search_rate_limiter, get_current_user
    from models.user import User
    from cache import cache_manager

# Create router
router = APIRouter(prefix="/search", tags=["search"])


async def get_current_user_with_search_permission(current_user: User = Depends(get_current_user)) -> User:
    """
    Dependency for search endpoints that require search permissions when user is authenticated.

    This maintains backward compatibility:
    - If no user is authenticated (current_user is None), allow access
    - If user is authenticated, they must have products.search permission
    """
    if current_user is None:
        return None

    # Check if authenticated user has search permission
    if not current_user.has_permission("products.search") and not current_user.is_superuser:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Insufficient permissions. Search access requires 'products.search' permission."
        )

    return current_user


@router.get("/", response_model=ProductSearchResponse)
async def search_products(
    q: str = Query(..., min_length=1, max_length=200, description="Search query"),
    limit: int = Query(20, ge=1, le=100, description="Maximum results"),
    offset: int = Query(0, ge=0, description="Results offset"),
    search_type: str = Query("fulltext", pattern="^(fulltext|similarity|hybrid)$", description="Search type"),
    category: str = Query(None, description="Filter by category"),
    brand: str = Query(None, description="Filter by brand"),
    seller: str = Query(None, description="Filter by seller"),
    manufactured_by: str = Query(None, description="Filter by manufacturer"),
    conn: Connection = Depends(get_db_connection),
    redis_client: redis.Redis = Depends(get_redis_client),
    current_user: User = Depends(get_current_user_with_search_permission)
):
    """
    Fast full-text product search with instant results.
    
    This endpoint provides high-performance search using PostgreSQL's full-text search
    capabilities with GIN indexes for instant results on large datasets.
    
    **Search Types:**
    - `fulltext`: PostgreSQL to_tsvector/to_tsquery (fastest, exact matches)
    - `similarity`: pg_trgm similarity (good for typos, fuzzy matching)
    - `hybrid`: Try fulltext first, fallback to similarity if no results
    
    **Features:**
    - Instant search performance with GIN indexes
    - Redis caching for frequently searched terms
    - Advanced filtering by category, brand, seller, manufacturer
    - Pagination support
    - Search ranking and relevance scoring
    """
    try:
        # Apply rate limiting
        if current_user:
            await search_rate_limiter(current_user.id, redis_client)
        
        # Create search request
        search_request = ProductSearchRequest(
            q=q,
            limit=limit,
            offset=offset,
            search_type=search_type,
            category=category,
            brand=brand,
            seller=seller,
            manufactured_by=manufactured_by
        )
        
        # Perform search
        result = await SearchService.search_products(conn, redis_client, search_request)
        
        return result
    
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Search failed: {str(e)}"
        )


@router.get("/suggestions", response_model=ProductSuggestionResponse)
async def get_search_suggestions(
    q: str = Query(..., min_length=1, max_length=100, description="Partial search query"),
    limit: int = Query(10, ge=1, le=100, description="Maximum suggestions"),
    category: str = Query(None, description="Filter suggestions by category"),
    brand: str = Query(None, description="Filter suggestions by brand (deprecated, use manufactured_by)"),
    manufactured_by: str = Query(None, description="Filter suggestions by manufacturer"),
    seller: str = Query(None, description="Filter suggestions by seller"),
    conn: Connection = Depends(get_db_connection),
    redis_client: redis.Redis = Depends(get_redis_client),
    current_user: User = Depends(get_current_user_with_search_permission)
):
    """
    Get search suggestions for autocomplete functionality with optional filtering.

    Returns relevant search suggestions based on existing product data
    including product names, manufacturers, categories, and sellers.
    When filters are provided, only suggestions from products matching
    those filters will be returned.
    """
    try:
        # Build filters dictionary
        filters = {}
        if category:
            filters["category"] = category
        if manufactured_by:
            filters["manufactured_by"] = manufactured_by
        elif brand:  # Backward compatibility
            filters["brand"] = brand
        if seller:
            filters["seller"] = seller

        result = await SearchService.get_search_suggestions(
            conn, redis_client, q, limit, filters if filters else None
        )
        return result

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get suggestions: {str(e)}"
        )


@router.get("/popular", response_model=PopularSearchResponse)
async def get_popular_searches(
    limit: int = Query(10, ge=1, le=50, description="Maximum popular searches"),
    conn: Connection = Depends(get_db_connection),
    redis_client: redis.Redis = Depends(get_redis_client)
):
    """
    Get popular search terms based on product categories and brands.
    
    Returns the most common categories and brands that users might search for,
    useful for displaying trending searches or search suggestions.
    """
    try:
        result = await SearchService.get_popular_searches(conn, redis_client, limit)
        return result
    
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get popular searches: {str(e)}"
        )


@router.get("/stats", response_model=SearchStatsResponse)
async def get_search_stats(
    conn: Connection = Depends(get_db_connection),
    redis_client: redis.Redis = Depends(get_redis_client),
    current_user: User = Depends(get_current_user_with_search_permission)
):
    """
    Get search performance statistics and database information.
    
    Returns statistics about the search system including total products,
    unique categories/brands/sellers, cache performance, and available search types.
    """
    try:
        stats = await SearchService.get_search_stats(conn, redis_client)
        
        return SearchStatsResponse(
            stats=stats
        )
    
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get search stats: {str(e)}"
        )


@router.delete("/cache", response_model=dict)
async def clear_search_cache(
    pattern: str = Query("search:*", description="Cache key pattern to clear"),
    redis_client: redis.Redis = Depends(get_redis_client),
    current_user: User = Depends(get_current_user)
):
    """
    Clear search cache entries.
    
    Clears cached search results matching the specified pattern.
    Useful for cache management and ensuring fresh results after data updates.
    
    **Note:** This endpoint requires authentication.
    """
    try:
        if not current_user:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Authentication required"
            )
        
        cleared_count = await SearchService.clear_search_cache(redis_client, pattern)
        
        return {
            "success": True,
            "message": f"Cleared {cleared_count} cache entries",
            "pattern": pattern,
            "cleared_count": cleared_count
        }
    
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to clear cache: {str(e)}"
        )


@router.get("/test", response_model=dict)
async def test_search_performance(
    test_queries: str = Query("dental,implant,crown,filling,orthodontic", description="Comma-separated test queries"),
    conn: Connection = Depends(get_db_connection),
    redis_client: redis.Redis = Depends(get_redis_client)
):
    """
    Test search performance with multiple queries.
    
    Runs performance tests on the search system using provided test queries
    and returns timing information for each search type.
    """
    try:
        queries = [q.strip() for q in test_queries.split(",") if q.strip()]
        results = []
        
        for query in queries[:5]:  # Limit to 5 test queries
            query_results = {}
            
            # Test each search type
            for search_type in ["fulltext", "similarity"]:
                start_time = time.time()
                
                search_request = ProductSearchRequest(
                    q=query,
                    limit=10,
                    offset=0,
                    search_type=search_type
                )
                
                search_result = await SearchService.search_products(conn, redis_client, search_request)
                
                query_results[search_type] = {
                    "query": query,
                    "search_time_ms": (time.time() - start_time) * 1000,
                    "total_results": search_result.total,
                    "returned_results": len(search_result.results)
                }
            
            results.append(query_results)
        
        return {
            "success": True,
            "message": "Search performance test completed",
            "test_results": results
        }
    
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Performance test failed: {str(e)}"
        )


# Health check for search service
@router.get("/health", response_model=dict)
async def search_health_check(
    conn: Connection = Depends(get_db_connection),
    redis_client: redis.Redis = Depends(get_redis_client)
):
    """Search service health check."""
    try:
        # Test database connection
        product_count = await conn.fetchval("SELECT COUNT(*) FROM products LIMIT 1")
        
        # Test Redis connection
        await redis_client.ping()
        
        return {
            "success": True,
            "message": "Search service is healthy",
            "database_connection": "healthy",
            "redis_connection": "healthy",
            "sample_product_count": product_count or 0
        }
    
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail=f"Search service unhealthy: {str(e)}"
        )

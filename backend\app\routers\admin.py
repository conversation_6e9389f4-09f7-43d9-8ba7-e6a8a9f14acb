"""
Admin-only endpoints for user management and system administration.
Requires superadmin role for access.
"""

from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, status, Query
from asyncpg import Connection

from ..dependencies import get_db_connection, get_current_active_user
from ..models.user import User, UserRepository
from ..utils.account_lockout import get_lockout_manager
from ..utils.audit_logger import get_audit_logger, AuditEventType
from ..models.role import RoleRepository
from ..models.permission import PermissionRepository
from ..schemas.auth import UserResponse, UserUpdate
from ..schemas.rbac import (
    RoleResponse, RoleCreate, RoleUpdate,
    PermissionResponse, PermissionCreate, PermissionUpdate,
    UserRoleAssignmentResponse, UserRoleAssignmentCreate
)
from ..middleware.authorization import require_superadmin


router = APIRouter(prefix="/admin", tags=["admin"])


# User Management Endpoints

@router.get("/users", response_model=List[UserResponse])
async def list_all_users(
    skip: int = Query(0, ge=0, description="Number of users to skip"),
    limit: int = Query(100, ge=1, le=1000, description="Number of users to return"),
    search: Optional[str] = Query(None, description="Search by email or full name"),
    is_active: Optional[bool] = Query(None, description="Filter by active status"),
    is_superuser: Optional[bool] = Query(None, description="Filter by superuser status"),
    current_user: User = Depends(require_superadmin),
    conn: Connection = Depends(get_db_connection)
):
    """
    List all users in the system (superadmin only).
    Supports pagination, search, and filtering.
    """
    try:
        users = await UserRepository.list_users_with_filters(
            conn=conn,
            skip=skip,
            limit=limit,
            search=search,
            is_active=is_active,
            is_superuser=is_superuser
        )
        
        return [UserResponse(**user.to_dict()) for user in users]
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to list users: {str(e)}"
        )


@router.get("/users/{user_id}", response_model=UserResponse)
async def get_user_details(
    user_id: str,
    current_user: User = Depends(require_superadmin),
    conn: Connection = Depends(get_db_connection)
):
    """
    Get detailed information about a specific user (superadmin only).
    """
    try:
        user = await UserRepository.get_user_with_roles_and_permissions(conn, user_id)
        if not user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="User not found"
            )
        
        return UserResponse(**user.to_dict())
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get user details: {str(e)}"
        )


@router.put("/users/{user_id}", response_model=UserResponse)
async def update_user(
    user_id: str,
    user_update: UserUpdate,
    current_user: User = Depends(require_superadmin),
    conn: Connection = Depends(get_db_connection)
):
    """
    Update user information (superadmin only).
    """
    try:
        # Check if user exists
        existing_user = await UserRepository.get_user_by_id(conn, user_id)
        if not existing_user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="User not found"
            )
        
        # Update user
        updated_user = await UserRepository.update_user(
            conn=conn,
            user_id=user_id,
            update_data=user_update
        )
        
        return UserResponse(**updated_user.to_dict())
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to update user: {str(e)}"
        )


@router.post("/users/{user_id}/activate")
async def activate_user(
    user_id: str,
    current_user: User = Depends(require_superadmin),
    conn: Connection = Depends(get_db_connection)
):
    """
    Activate a user account (superadmin only).
    """
    try:
        success = await UserRepository.activate_user(conn, user_id)
        if not success:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="User not found"
            )
        
        return {"message": "User activated successfully"}
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to activate user: {str(e)}"
        )


@router.post("/users/{user_id}/deactivate")
async def deactivate_user(
    user_id: str,
    current_user: User = Depends(require_superadmin),
    conn: Connection = Depends(get_db_connection)
):
    """
    Deactivate a user account (superadmin only).
    """
    try:
        # Prevent deactivating self
        if user_id == current_user.id:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Cannot deactivate your own account"
            )
        
        success = await UserRepository.delete_user(conn, user_id)
        if not success:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="User not found"
            )
        
        return {"message": "User deactivated successfully"}
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to deactivate user: {str(e)}"
        )


@router.post("/users/{user_id}/make-superuser")
async def make_user_superuser(
    user_id: str,
    current_user: User = Depends(require_superadmin),
    conn: Connection = Depends(get_db_connection)
):
    """
    Grant superuser privileges to a user (superadmin only).
    """
    try:
        success = await UserRepository.make_superuser(conn, user_id)
        if not success:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="User not found"
            )
        
        # Also assign superadmin role
        await UserRepository.assign_role_to_user_by_name(
            conn=conn,
            user_id=user_id,
            role_name="superadmin",
            assigned_by=current_user.id
        )
        
        return {"message": "User granted superuser privileges successfully"}
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to grant superuser privileges: {str(e)}"
        )


@router.post("/users/{user_id}/remove-superuser")
async def remove_user_superuser(
    user_id: str,
    current_user: User = Depends(require_superadmin),
    conn: Connection = Depends(get_db_connection)
):
    """
    Remove superuser privileges from a user (superadmin only).
    """
    try:
        # Prevent removing superuser from self
        if user_id == current_user.id:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Cannot remove superuser privileges from your own account"
            )
        
        success = await UserRepository.remove_superuser(conn, user_id)
        if not success:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="User not found"
            )
        
        return {"message": "Superuser privileges removed successfully"}
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to remove superuser privileges: {str(e)}"
        )


# System Statistics Endpoints

@router.get("/stats/users")
async def get_user_statistics(
    current_user: User = Depends(require_superadmin),
    conn: Connection = Depends(get_db_connection)
):
    """
    Get user statistics for the admin dashboard (superadmin only).
    """
    try:
        stats = await UserRepository.get_user_statistics(conn)
        return stats
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get user statistics: {str(e)}"
        )


@router.get("/stats/roles")
async def get_role_statistics(
    current_user: User = Depends(require_superadmin),
    conn: Connection = Depends(get_db_connection)
):
    """
    Get role statistics for the admin dashboard (superadmin only).
    """
    try:
        stats = await RoleRepository.get_role_statistics(conn)
        return stats
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get role statistics: {str(e)}"
        )


@router.get("/stats/permissions")
async def get_permission_statistics(
    current_user: User = Depends(require_superadmin),
    conn: Connection = Depends(get_db_connection)
):
    """
    Get permission statistics for the admin dashboard (superadmin only).
    """
    try:
        stats = await PermissionRepository.get_permission_statistics(conn)
        return stats
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get permission statistics: {str(e)}"
        )


# System Health Endpoints

@router.get("/health/database")
async def check_database_health(
    current_user: User = Depends(require_superadmin),
    conn: Connection = Depends(get_db_connection)
):
    """
    Check database health and connectivity (superadmin only).
    """
    try:
        # Simple database connectivity test
        result = await conn.fetchval("SELECT 1")
        
        # Get database statistics
        db_stats = await conn.fetchrow("""
            SELECT 
                (SELECT COUNT(*) FROM users) as total_users,
                (SELECT COUNT(*) FROM roles) as total_roles,
                (SELECT COUNT(*) FROM permissions) as total_permissions,
                (SELECT COUNT(*) FROM user_roles) as total_user_roles,
                (SELECT COUNT(*) FROM role_permissions) as total_role_permissions
        """)
        
        return {
            "status": "healthy" if result == 1 else "unhealthy",
            "database_stats": dict(db_stats) if db_stats else {},
            "timestamp": "NOW()"
        }
        
    except Exception as e:
        return {
            "status": "unhealthy",
            "error": str(e),
            "timestamp": "NOW()"
        }


@router.get("/lockout-status/{identifier}")
async def get_lockout_status(
    identifier: str,
    current_user: User = Depends(require_superadmin)
):
    """
    Get account lockout status for a user (superadmin only).

    Args:
        identifier: User email or identifier to check
        current_user: Current authenticated superadmin user

    Returns:
        Account lockout status information
    """
    try:
        lockout_manager = get_lockout_manager()
        lockout_status = await lockout_manager.check_lockout_status(identifier)

        return {
            "success": True,
            "data": {
                "identifier": identifier,
                "is_locked": lockout_status.is_locked,
                "attempts_count": lockout_status.attempts_count,
                "lockout_expires_at": lockout_status.lockout_expires_at,
                "next_attempt_allowed_at": lockout_status.next_attempt_allowed_at,
                "remaining_attempts": lockout_status.remaining_attempts
            },
            "message": "Lockout status retrieved successfully"
        }

    except RuntimeError as e:
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail=f"Account lockout service unavailable: {str(e)}"
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get lockout status: {str(e)}"
        )


@router.get("/audit-logs")
async def get_audit_logs(
    event_type: Optional[str] = None,
    user_id: Optional[str] = None,
    start_time: Optional[float] = None,
    end_time: Optional[float] = None,
    limit: int = 100,
    current_user: User = Depends(require_superadmin)
):
    """Get audit logs for security monitoring (superadmin only)."""
    try:
        audit_logger = get_audit_logger()

        # Convert event_type string to enum if provided
        event_type_enum = None
        if event_type:
            try:
                event_type_enum = AuditEventType(event_type)
            except ValueError:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=f"Invalid event type: {event_type}"
                )

        # Get audit events
        events = await audit_logger.get_audit_events(
            event_type=event_type_enum,
            user_id=user_id,
            start_time=start_time,
            end_time=end_time,
            limit=min(limit, 1000)  # Cap at 1000 events
        )

        return {
            "success": True,
            "data": {
                "events": events,
                "total_returned": len(events),
                "filters": {
                    "event_type": event_type,
                    "user_id": user_id,
                    "start_time": start_time,
                    "end_time": end_time,
                    "limit": limit
                }
            },
            "message": f"Retrieved {len(events)} audit events"
        }

    except RuntimeError as e:
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail=f"Audit logging service unavailable: {str(e)}"
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to retrieve audit logs: {str(e)}"
        )


@router.get("/audit-events/types")
async def get_audit_event_types(
    current_user: User = Depends(require_superadmin)
):
    """Get available audit event types (superadmin only)."""
    try:
        event_types = [event_type.value for event_type in AuditEventType]

        return {
            "success": True,
            "data": {
                "event_types": event_types,
                "total_types": len(event_types)
            },
            "message": "Audit event types retrieved successfully"
        }

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get audit event types: {str(e)}"
        )


@router.post("/clear-lockout/{identifier}")
async def clear_account_lockout(
    identifier: str,
    current_user: User = Depends(require_superadmin)
):
    """
    Clear account lockout for a user (superadmin only).

    Args:
        identifier: User email or identifier to clear lockout for
        current_user: Current authenticated superadmin user

    Returns:
        Success message
    """
    try:
        lockout_manager = get_lockout_manager()
        success = await lockout_manager.clear_lockout(identifier)

        if success:
            return {
                "success": True,
                "message": f"Account lockout cleared for {identifier}"
            }
        else:
            return {
                "success": True,
                "message": f"No active lockout found for {identifier}"
            }

    except RuntimeError as e:
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail=f"Account lockout service unavailable: {str(e)}"
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to clear lockout: {str(e)}"
        )

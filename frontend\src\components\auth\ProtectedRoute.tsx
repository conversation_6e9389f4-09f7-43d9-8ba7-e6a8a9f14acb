import React from "react";
import { Navigate, useLocation, useNavigate } from "react-router-dom";
import { useAuthStore } from "../../stores/authStore";
import { useHasAccess } from "../../hooks/useRoles";

interface ProtectedRouteProps {
  children: React.ReactNode;
  roles?: string[];
  permissions?: string[];
  requireAll?: boolean;
  allowSuperAdmin?: boolean;
  requireAuth?: boolean;
  redirectTo?: string;
  fallbackComponent?: React.ComponentType;
}

/**
 * ProtectedRoute component for route-level access control
 *
 * @param children - Component to render when access is granted
 * @param roles - Array of required roles
 * @param permissions - Array of required permissions
 * @param requireAll - Whether all roles/permissions are required (default: false)
 * @param allowSuperAdmin - Whether superadmin bypasses checks (default: true)
 * @param requireAuth - Whether authentication is required (default: true)
 * @param redirectTo - Where to redirect unauthorized users (default: "/login")
 * @param fallbackComponent - Component to render instead of redirecting
 */
export const ProtectedRoute: React.FC<ProtectedRouteProps> = ({
  children,
  roles = [],
  permissions = [],
  requireAll = false,
  allowSuperAdmin = true,
  requireAuth = true,
  redirectTo = "/login",
  fallbackComponent: FallbackComponent,
}) => {
  const { user, isAuthenticated } = useAuthStore();
  const location = useLocation();

  // Check if user is authenticated when required
  if (requireAuth && !isAuthenticated) {
    return <Navigate to={redirectTo} state={{ from: location }} replace />;
  }

  // If no specific roles or permissions required, just check auth
  if (roles.length === 0 && permissions.length === 0) {
    return <>{children}</>;
  }

  // Check role/permission access
  const hasAccess = useHasAccess({
    roles,
    permissions,
    requireAll,
    allowSuperAdmin,
  });

  if (!hasAccess) {
    if (FallbackComponent) {
      return <FallbackComponent />;
    }

    // Redirect to appropriate page based on auth status
    const redirectPath = isAuthenticated ? "/" : redirectTo;
    return <Navigate to={redirectPath} state={{ from: location }} replace />;
  }

  return <>{children}</>;
};

/**
 * Component for unauthorized access fallback
 */
export const UnauthorizedAccess: React.FC<{
  message?: string;
  showBackButton?: boolean;
}> = ({
  message = "Access Denied: You don't have permission to view this page.",
  showBackButton = true,
}) => {
  const navigate = useNavigate();

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8 text-center">
        <div>
          <div className="mx-auto h-12 w-12 text-red-600">
            <svg
              className="h-12 w-12"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z"
              />
            </svg>
          </div>
          <h2 className="mt-6 text-3xl font-extrabold text-gray-900">
            Access Denied
          </h2>
          <p className="mt-2 text-sm text-gray-600">{message}</p>
        </div>

        {showBackButton && (
          <div className="mt-6">
            <button
              onClick={() => navigate(-1)}
              className="group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
            >
              Go Back
            </button>
          </div>
        )}
      </div>
    </div>
  );
};

/**
 * Convenience component for admin-only routes
 */
export const AdminRoute: React.FC<{
  children: React.ReactNode;
  fallbackComponent?: React.ComponentType;
}> = ({ children, fallbackComponent }) => (
  <ProtectedRoute
    permissions={["user:manage", "role:manage", "system:access"]}
    requireAll={false}
    allowSuperAdmin={true}
    fallbackComponent={fallbackComponent || UnauthorizedAccess}
  >
    {children}
  </ProtectedRoute>
);

/**
 * Convenience component for superadmin-only routes
 */
export const SuperAdminRoute: React.FC<{
  children: React.ReactNode;
  fallbackComponent?: React.ComponentType;
}> = ({ children, fallbackComponent }) => (
  <ProtectedRoute
    permissions={["superadmin"]}
    allowSuperAdmin={true}
    fallbackComponent={fallbackComponent || UnauthorizedAccess}
  >
    {children}
  </ProtectedRoute>
);

/**
 * Convenience component for user management routes
 */
export const UserManagementRoute: React.FC<{
  children: React.ReactNode;
  fallbackComponent?: React.ComponentType;
}> = ({ children, fallbackComponent }) => (
  <ProtectedRoute
    permissions={["user:manage"]}
    allowSuperAdmin={true}
    fallbackComponent={fallbackComponent || UnauthorizedAccess}
  >
    {children}
  </ProtectedRoute>
);

/**
 * Convenience component for role management routes
 */
export const RoleManagementRoute: React.FC<{
  children: React.ReactNode;
  fallbackComponent?: React.ComponentType;
}> = ({ children, fallbackComponent }) => (
  <ProtectedRoute
    permissions={["role:manage"]}
    allowSuperAdmin={true}
    fallbackComponent={fallbackComponent || UnauthorizedAccess}
  >
    {children}
  </ProtectedRoute>
);

export default ProtectedRoute;

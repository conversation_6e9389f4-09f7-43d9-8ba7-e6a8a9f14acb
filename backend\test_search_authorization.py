#!/usr/bin/env python3
"""
Critical verification test for search API endpoints with authorization system.
Ensures search functionality remains intact with new RBAC implementation.
"""

import asyncio
import aiohttp
import json
import time
from app.config import settings


async def test_search_without_authentication():
    """Test search endpoints without authentication (should work due to optional auth)."""
    print("🔍 Testing Search Without Authentication")
    print("=" * 50)

    base_url = "http://localhost:8000"

    async with aiohttp.ClientSession() as session:
        # Test 1: Search suggestions without auth
        print("\n🔍 Test 1: Search suggestions without authentication...")
        
        async with session.get(f"{base_url}/api/v1/search/suggestions?q=dental") as response:
            if response.status == 200:
                suggestions = await response.json()
                print(f"✅ Search suggestions working without auth: {len(suggestions)} results")
                if suggestions:
                    print(f"   - First result: {suggestions[0].get('name', 'N/A')}")
            else:
                print(f"❌ Search suggestions failed without auth: {response.status}")
                text = await response.text()
                print(f"Response: {text}")
        
        # Test 2: Main search without auth
        print("\n🔍 Test 2: Main search without authentication...")

        async with session.get(f"{base_url}/api/v1/search/?q=dental&limit=5") as response:
            if response.status == 200:
                search_results = await response.json()
                results = search_results.get('results', [])
                print(f"✅ Main search working without auth: {len(results)} results")
                if results:
                    print(f"   - First result: {results[0].get('name', 'N/A')}")
            else:
                print(f"❌ Main search failed without auth: {response.status}")
                text = await response.text()
                print(f"Response: {text}")
                return False
        
        # Test 3: Search filters without auth
        print("\n🔍 Test 3: Search filters without authentication...")
        
        async with session.get(f"{base_url}/api/v1/search/filters/categories") as response:
            if response.status == 200:
                categories = await response.json()
                print(f"✅ Category filters working without auth: {len(categories)} categories")
            else:
                print(f"❌ Category filters failed without auth: {response.status}")
        
        async with session.get(f"{base_url}/api/v1/search/filters/brands") as response:
            if response.status == 200:
                brands = await response.json()
                print(f"✅ Brand filters working without auth: {len(brands)} brands")
            else:
                print(f"❌ Brand filters failed without auth: {response.status}")
        
        async with session.get(f"{base_url}/api/v1/search/filters/sellers") as response:
            if response.status == 200:
                sellers = await response.json()
                print(f"✅ Seller filters working without auth: {len(sellers)} sellers")
            else:
                print(f"❌ Seller filters failed without auth: {response.status}")
                return False

        return True


async def test_search_with_regular_user():
    """Test search endpoints with regular user authentication."""
    print("\n🔍 Testing Search With Regular User Authentication")
    print("=" * 50)
    
    base_url = "http://localhost:8000"
    
    async with aiohttp.ClientSession() as session:
        # Register and login as regular user
        print("\n🔍 Step 1: Register and login as regular user...")
        
        timestamp = int(time.time())
        user_data = {
            "email": f"search_test_{timestamp}@profident.com",
            "password": "SearchTest123!",
            "full_name": "Search Test User"
        }
        
        # Register user
        async with session.post(f"{base_url}/api/v1/auth/register", json=user_data) as response:
            if response.status == 201:
                reg_data = await response.json()
                access_token = reg_data["token"]["access_token"]
                print(f"✅ Regular user registered and authenticated")
            else:
                print(f"❌ Failed to register user: {response.status}")
                return False
        
        headers = {"Authorization": f"Bearer {access_token}"}
        
        # Test 1: Search suggestions with auth
        print("\n🔍 Test 1: Search suggestions with authentication...")
        
        async with session.get(f"{base_url}/api/v1/search/suggestions?q=dental", headers=headers) as response:
            if response.status == 200:
                suggestions = await response.json()
                print(f"✅ Search suggestions working with auth: {len(suggestions)} results")
            else:
                print(f"❌ Search suggestions failed with auth: {response.status}")
        
        # Test 2: Main search with auth
        print("\n🔍 Test 2: Main search with authentication...")
        
        async with session.get(f"{base_url}/api/v1/search/?q=dental&limit=5", headers=headers) as response:
            if response.status == 200:
                search_results = await response.json()
                results = search_results.get('results', [])
                print(f"✅ Main search working with auth: {len(results)} results")
            else:
                print(f"❌ Main search failed with auth: {response.status}")
        
        # Test 3: Advanced search with filters
        print("\n🔍 Test 3: Advanced search with filters...")
        
        params = {
            "q": "dental",
            "category": "Dental Instruments",
            "limit": 3
        }
        
        async with session.get(f"{base_url}/api/v1/search/", headers=headers, params=params) as response:
            if response.status == 200:
                search_results = await response.json()
                results = search_results.get('results', [])
                print(f"✅ Advanced search with filters working: {len(results)} results")
            else:
                print(f"❌ Advanced search with filters failed: {response.status}")
        
        return True


async def test_search_with_superadmin():
    """Test search endpoints with superadmin authentication."""
    print("\n🔍 Testing Search With Superadmin Authentication")
    print("=" * 50)
    
    base_url = "http://localhost:8000"
    
    async with aiohttp.ClientSession() as session:
        # Login as superadmin
        print("\n🔍 Step 1: Login as superadmin...")
        
        superadmin_login = {
            "email": "<EMAIL>",
            "password": "SuperYzn123!"
        }
        
        async with session.post(f"{base_url}/api/v1/auth/login", json=superadmin_login) as response:
            if response.status == 200:
                login_data = await response.json()
                access_token = login_data["token"]["access_token"]
                print("✅ Superadmin authenticated")
            else:
                print(f"❌ Superadmin login failed: {response.status}")
                return False
        
        headers = {"Authorization": f"Bearer {access_token}"}
        
        # Test 1: All search endpoints with superadmin
        print("\n🔍 Test 1: Search suggestions with superadmin...")
        
        async with session.get(f"{base_url}/api/v1/search/suggestions?q=dental", headers=headers) as response:
            if response.status == 200:
                suggestions = await response.json()
                print(f"✅ Search suggestions working with superadmin: {len(suggestions)} results")
            else:
                print(f"❌ Search suggestions failed with superadmin: {response.status}")
        
        print("\n🔍 Test 2: Main search with superadmin...")
        
        async with session.get(f"{base_url}/api/v1/search/?q=dental&limit=5", headers=headers) as response:
            if response.status == 200:
                search_results = await response.json()
                results = search_results.get('results', [])
                print(f"✅ Main search working with superadmin: {len(results)} results")
            else:
                print(f"❌ Main search failed with superadmin: {response.status}")
        
        return True


async def test_search_performance_with_auth():
    """Test search performance with authentication overhead."""
    print("\n🔍 Testing Search Performance With Authentication")
    print("=" * 50)
    
    base_url = "http://localhost:8000"
    
    async with aiohttp.ClientSession() as session:
        # Login as regular user
        timestamp = int(time.time())
        user_data = {
            "email": f"perf_test_{timestamp}@profident.com",
            "password": "PerfTest123!",
            "full_name": "Performance Test User"
        }
        
        async with session.post(f"{base_url}/api/v1/auth/register", json=user_data) as response:
            if response.status == 201:
                reg_data = await response.json()
                access_token = reg_data["token"]["access_token"]
            else:
                print(f"❌ Failed to register performance test user: {response.status}")
                return
        
        headers = {"Authorization": f"Bearer {access_token}"}
        
        # Test multiple search requests to check performance
        print("\n🔍 Testing search performance (10 requests)...")
        
        start_time = time.time()
        successful_requests = 0
        
        for i in range(10):
            async with session.get(f"{base_url}/api/v1/search/suggestions?q=test{i}", headers=headers) as response:
                if response.status == 200:
                    successful_requests += 1
        
        end_time = time.time()
        total_time = end_time - start_time
        avg_time = total_time / 10
        
        print(f"✅ Performance test completed:")
        print(f"   - Successful requests: {successful_requests}/10")
        print(f"   - Total time: {total_time:.2f}s")
        print(f"   - Average time per request: {avg_time:.3f}s")
        
        if avg_time < 0.5:  # Target: under 500ms per request
            print("✅ Performance target met (< 500ms per request)")
        else:
            print("⚠️  Performance target not met (> 500ms per request)")


async def test_search_edge_cases():
    """Test search endpoints with edge cases and error conditions."""
    print("\n🔍 Testing Search Edge Cases")
    print("=" * 50)
    
    base_url = "http://localhost:8000"
    
    async with aiohttp.ClientSession() as session:
        # Test 1: Empty search query
        print("\n🔍 Test 1: Empty search query...")
        
        async with session.get(f"{base_url}/api/v1/search/suggestions?q=") as response:
            print(f"✅ Empty query handled: {response.status}")
        
        # Test 2: Very long search query
        print("\n🔍 Test 2: Very long search query...")
        
        long_query = "a" * 1000
        async with session.get(f"{base_url}/api/v1/search/suggestions?q={long_query}") as response:
            print(f"✅ Long query handled: {response.status}")
        
        # Test 3: Special characters in search
        print("\n🔍 Test 3: Special characters in search...")
        
        special_query = "test@#$%^&*()"
        async with session.get(f"{base_url}/api/v1/search/suggestions", params={"q": special_query}) as response:
            print(f"✅ Special characters handled: {response.status}")
        
        # Test 4: Search with invalid parameters
        print("\n🔍 Test 4: Search with invalid parameters...")
        
        async with session.get(f"{base_url}/api/v1/search/?q=test&limit=invalid") as response:
            print(f"✅ Invalid parameters handled: {response.status}")


async def main():
    """Run all search authorization tests."""
    print("🚀 Starting Search Authorization Verification Tests")
    print("=" * 60)
    print("Critical verification that search functionality remains intact with RBAC.")
    print()
    
    try:
        # Test 1: Search without authentication
        success = await test_search_without_authentication()
        if not success:
            print("❌ Search without authentication test failed")
            return False

        # Test 2: Search with regular user
        success = await test_search_with_regular_user()
        if not success:
            print("❌ Regular user search test failed")
            return False
        
        # Test 3: Search with superadmin
        success = await test_search_with_superadmin()
        if not success:
            print("❌ Superadmin search test failed")
            return False
        
        # Test 4: Search performance
        await test_search_performance_with_auth()
        
        # Test 5: Search edge cases
        await test_search_edge_cases()
        
        print("\n🎉 Search Authorization Verification Completed!")
        print("=" * 60)
        print("✅ Search without authentication working")
        print("✅ Search with regular user authentication working")
        print("✅ Search with superadmin authentication working")
        print("✅ Search performance acceptable")
        print("✅ Search edge cases handled properly")
        print("✅ CRITICAL: All search functionality preserved with RBAC")
        
        return True
        
    except Exception as e:
        print(f"\n❌ Search authorization test failed: {str(e)}")
        return False


if __name__ == "__main__":
    success = asyncio.run(main())
    
    if success:
        print("\n🎉 All search authorization tests passed!")
        print("🔒 Search functionality fully compatible with RBAC system!")
        exit(0)
    else:
        print("\n❌ Search authorization tests failed!")
        print("🚨 CRITICAL: Search functionality may be compromised!")
        exit(1)

#!/usr/bin/env python3
"""
Comprehensive test for the complete shopping list flow.
Tests the entire user journey from search to shopping list addition.
"""

import asyncio
import asyncpg
import os
import sys
import time
import json
from typing import List, Dict, Any

# Add the app directory to the path to import config
sys.path.append(os.path.join(os.path.dirname(__file__), 'app'))

try:
    from config import settings, get_database_url_sync
    from services.search_service import SearchService
    from services.shopping_list_service import ShoppingListService
    from schemas.shopping_list import ShoppingListItemCreate
    from models.product import ProductRepository
    from database import DatabaseManager
    import redis.asyncio as redis
except ImportError:
    settings = None

class CompleteFlowTester:
    """Tests the complete shopping list flow end-to-end."""
    
    def __init__(self):
        if settings:
            self.db_url = get_database_url_sync()
        else:
            self.db_url = "postgresql://profident_user:profident_dev_password@localhost:5433/profident_dev"
        
        print(f"🔗 Using database URL: {self.db_url[:50]}...")
    
    async def get_connection(self):
        """Get database connection."""
        return await asyncpg.connect(self.db_url)
    
    async def get_redis_client(self):
        """Get Redis client."""
        return redis.Redis(host='localhost', port=6380, db=0, decode_responses=True)
    
    async def test_complete_user_flow(self):
        """Test the complete user flow from search to shopping list."""
        print("🎯 Testing Complete Shopping List User Flow")
        print("=" * 60)
        
        conn = await self.get_connection()
        redis_client = await self.get_redis_client()
        test_user_id = "test-flow-user-123"
        
        try:
            # Clean up any existing test data
            await self.cleanup_test_data(conn, test_user_id)
            
            # Step 1: Test focused search functionality
            print("\n📍 Step 1: Testing Focused Search")
            search_results = await self.test_focused_search(conn, redis_client)
            
            # Step 2: Test suggestions API with product objects
            print("\n📍 Step 2: Testing Enhanced Suggestions API")
            suggestions = await self.test_enhanced_suggestions(conn, redis_client)
            
            # Step 3: Test quick-add functionality
            print("\n📍 Step 3: Testing Quick-Add Functionality")
            if suggestions:
                added_item = await self.test_quick_add(conn, redis_client, test_user_id, suggestions[0])
            else:
                print("❌ No suggestions available for quick-add test")
                return
            
            # Step 4: Test default shopping list management
            print("\n📍 Step 4: Testing Default Shopping List Management")
            await self.test_default_list_management(conn, test_user_id)
            
            # Step 5: Test shopping list retrieval
            print("\n📍 Step 5: Testing Shopping List Retrieval")
            await self.test_shopping_list_retrieval(conn, redis_client, test_user_id)
            
            # Step 6: Performance validation
            print("\n📍 Step 6: Performance Validation")
            await self.test_performance_metrics(conn, redis_client)
            
            print("\n🎉 Complete Flow Test Successful!")
            print("✅ All components working together correctly")
            
        except Exception as e:
            print(f"❌ Complete flow test failed: {e}")
            import traceback
            traceback.print_exc()
            raise
        
        finally:
            # Clean up test data
            await self.cleanup_test_data(conn, test_user_id)
            await conn.close()
            await redis_client.close()
    
    async def test_focused_search(self, conn, redis_client):
        """Test the focused search functionality."""
        print("🔍 Testing focused search on MFR and name fields...")
        
        test_queries = ["dental", "implant", "110402"]
        
        for query in test_queries:
            start_time = time.time()
            
            # Test search service
            results = await SearchService.search_products(
                conn, redis_client, query, limit=10
            )
            
            search_time = (time.time() - start_time) * 1000
            
            print(f"  Query '{query}': {len(results.products)} results in {search_time:.1f}ms")
            
            if search_time > 500:
                print(f"  ⚠️ Performance warning: {search_time:.1f}ms > 500ms target")
            else:
                print(f"  ✅ Performance good: {search_time:.1f}ms < 500ms target")
        
        return results.products if results else []
    
    async def test_enhanced_suggestions(self, conn, redis_client):
        """Test the enhanced suggestions API."""
        print("💡 Testing enhanced suggestions with product objects...")
        
        test_queries = ["dental", "crown"]
        suggestions_list = []
        
        for query in test_queries:
            start_time = time.time()
            
            result = await SearchService.get_search_suggestions(
                conn, redis_client, query, limit=5
            )
            
            suggestion_time = (time.time() - start_time) * 1000
            
            print(f"  Query '{query}': {len(result.suggestions)} suggestions in {suggestion_time:.1f}ms")
            
            for i, suggestion in enumerate(result.suggestions[:2]):
                print(f"    {i+1}. ID: {suggestion.id}, MFR: {suggestion.mfr}")
                print(f"       Name: {suggestion.name[:50]}...")
                print(f"       Seller: {suggestion.seller}")
                
                # Validate required fields for shopping list
                assert suggestion.id is not None, "Suggestion missing ID"
                assert suggestion.mfr is not None, "Suggestion missing MFR"
                assert suggestion.name is not None, "Suggestion missing name"
                
            suggestions_list.extend(result.suggestions)
        
        print(f"  ✅ All suggestions have required fields for shopping list integration")
        return suggestions_list
    
    async def test_quick_add(self, conn, redis_client, user_id, product_suggestion):
        """Test the quick-add functionality."""
        print(f"⚡ Testing quick-add with product: {product_suggestion.name}")
        
        start_time = time.time()
        
        # Test quick-add service
        result = await ShoppingListService.quick_add_product_to_default_list(
            conn, redis_client, user_id, product_suggestion.id, quantity=2
        )
        
        add_time = (time.time() - start_time) * 1000
        
        if result:
            print(f"  ✅ Product added successfully in {add_time:.1f}ms")
            print(f"     Item ID: {result.id}")
            print(f"     Shopping List ID: {result.shopping_list_id}")
            print(f"     Quantity: {result.quantity}")
            
            if result.product:
                print(f"     Product: {result.product.name}")
                print(f"     MFR: {result.product.mfr}")
        else:
            print(f"  ❌ Failed to add product")
            
        return result
    
    async def test_default_list_management(self, conn, user_id):
        """Test default shopping list management."""
        print("🎯 Testing default shopping list management...")
        
        # Check that a default list was created
        lists = await conn.fetch("""
            SELECT id, name, is_default, created_at
            FROM shopping_lists
            WHERE user_id = $1
            ORDER BY created_at DESC
        """, user_id)
        
        print(f"  📊 User has {len(lists)} shopping list(s)")
        
        default_lists = [l for l in lists if l['is_default']]
        print(f"  📊 {len(default_lists)} default list(s)")
        
        if len(default_lists) == 1:
            print(f"  ✅ Exactly one default list: {default_lists[0]['name']}")
        else:
            print(f"  ❌ Expected 1 default list, found {len(default_lists)}")
        
        # Test items in the default list
        if lists:
            items = await conn.fetch("""
                SELECT sli.*, p.name, p.mfr
                FROM shopping_list_items sli
                JOIN products p ON sli.product_id = p.id
                WHERE sli.shopping_list_id = $1
            """, lists[0]['id'])
            
            print(f"  📦 Default list has {len(items)} item(s)")
            for item in items:
                print(f"     - {item['name']} (MFR: {item['mfr']}) x{item['quantity']}")
    
    async def test_shopping_list_retrieval(self, conn, redis_client, user_id):
        """Test shopping list retrieval with caching."""
        print("📋 Testing shopping list retrieval...")
        
        start_time = time.time()
        
        # Test service method
        lists, total = await ShoppingListService.get_user_shopping_lists(
            conn, redis_client, user_id, limit=10, offset=0
        )
        
        retrieval_time = (time.time() - start_time) * 1000
        
        print(f"  📊 Retrieved {len(lists)} lists (total: {total}) in {retrieval_time:.1f}ms")
        
        for shopping_list in lists:
            print(f"     - {shopping_list.name} (Default: {shopping_list.is_default})")
            print(f"       Created: {shopping_list.created_at}")
    
    async def test_performance_metrics(self, conn, redis_client):
        """Test overall performance metrics."""
        print("⚡ Testing performance metrics...")
        
        # Test search performance
        search_times = []
        for _ in range(5):
            start_time = time.time()
            await SearchService.search_products(conn, redis_client, "dental", limit=10)
            search_times.append((time.time() - start_time) * 1000)
        
        avg_search_time = sum(search_times) / len(search_times)
        print(f"  🔍 Average search time: {avg_search_time:.1f}ms")
        
        # Test suggestion performance
        suggestion_times = []
        for _ in range(5):
            start_time = time.time()
            await SearchService.get_search_suggestions(conn, redis_client, "dental", limit=5)
            suggestion_times.append((time.time() - start_time) * 1000)
        
        avg_suggestion_time = sum(suggestion_times) / len(suggestion_times)
        print(f"  💡 Average suggestion time: {avg_suggestion_time:.1f}ms")
        
        # Performance validation
        if avg_search_time < 500 and avg_suggestion_time < 500:
            print(f"  ✅ All performance targets met (<500ms)")
        else:
            print(f"  ⚠️ Performance targets missed")
    
    async def cleanup_test_data(self, conn, user_id):
        """Clean up test data."""
        await conn.execute("DELETE FROM shopping_list_items WHERE shopping_list_id IN (SELECT id FROM shopping_lists WHERE user_id = $1)", user_id)
        await conn.execute("DELETE FROM shopping_lists WHERE user_id = $1", user_id)

async def main():
    """Main function to run complete flow test."""
    tester = CompleteFlowTester()
    
    try:
        await tester.test_complete_user_flow()
        
    except Exception as e:
        print(f"❌ Complete flow test failed: {e}")
        raise

if __name__ == "__main__":
    asyncio.run(main())

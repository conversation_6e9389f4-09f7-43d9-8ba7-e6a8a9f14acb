import { create } from "zustand";
import {
  ShoppingList,
  ShoppingListItem,
  Product,
  ProductSuggestion,
} from "../types";
import { apiClient } from "../lib/api";

interface ShoppingListState {
  lists: ShoppingList[];
  currentList: ShoppingList | null;
  isLoading: boolean;
  error: string | null;
  selectedItems: Set<number>;

  // Animation state
  animatingProduct: ProductSuggestion | null;
  animationInProgress: boolean;
}

interface ShoppingListActions {
  setLists: (lists: ShoppingList[]) => void;
  setCurrentList: (list: ShoppingList | null) => void;
  addList: (list: ShoppingList) => void;
  updateList: (id: number, updates: Partial<ShoppingList>) => void;
  removeList: (id: number) => void;
  addItemToCurrentList: (item: ShoppingListItem) => void;
  updateItemInCurrentList: (
    itemId: number,
    updates: Partial<ShoppingListItem>
  ) => void;
  removeItemFromCurrentList: (itemId: number) => void;
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  toggleItemSelection: (itemId: number) => void;
  selectAllItems: () => void;
  clearSelection: () => void;
  reset: () => void;

  // Animation actions
  startProductAnimation: (product: ProductSuggestion) => void;
  endProductAnimation: () => void;

  // Quick-add functionality
  quickAddProduct: (
    product: ProductSuggestion,
    quantity?: number
  ) => Promise<void>;
  getTotalItemCount: () => number;
  getDefaultList: () => ShoppingList | null;
}

type ShoppingListStore = ShoppingListState & ShoppingListActions;

const initialState: ShoppingListState = {
  lists: [],
  currentList: null,
  isLoading: false,
  error: null,
  selectedItems: new Set(),
  animatingProduct: null,
  animationInProgress: false,
};

export const useShoppingListStore = create<ShoppingListStore>((set, get) => ({
  ...initialState,

  setLists: (lists: ShoppingList[]) => {
    set({ lists, isLoading: false, error: null });
  },

  setCurrentList: (currentList: ShoppingList | null) => {
    set({ currentList, selectedItems: new Set() });
  },

  addList: (list: ShoppingList) => {
    set((state) => ({
      lists: [list, ...state.lists],
    }));
  },

  updateList: (id: number, updates: Partial<ShoppingList>) => {
    set((state) => ({
      lists: state.lists.map((list) =>
        list.id === id ? { ...list, ...updates } : list
      ),
      currentList:
        state.currentList?.id === id
          ? { ...state.currentList, ...updates }
          : state.currentList,
    }));
  },

  removeList: (id: number) => {
    set((state) => ({
      lists: state.lists.filter((list) => list.id !== id),
      currentList: state.currentList?.id === id ? null : state.currentList,
    }));
  },

  addItemToCurrentList: (item: ShoppingListItem) => {
    set((state) => {
      if (!state.currentList) return state;

      const updatedList = {
        ...state.currentList,
        items: [...(state.currentList.items || []), item],
        summary: state.currentList.summary
          ? {
              ...state.currentList.summary,
              total_items: state.currentList.summary.total_items + 1,
              remaining_items: state.currentList.summary.remaining_items + 1,
              total_quantity:
                state.currentList.summary.total_quantity + item.quantity,
            }
          : undefined,
      };

      return {
        currentList: updatedList,
        lists: state.lists.map((list) =>
          list.id === updatedList.id ? updatedList : list
        ),
      };
    });
  },

  updateItemInCurrentList: (
    itemId: number,
    updates: Partial<ShoppingListItem>
  ) => {
    set((state) => {
      if (!state.currentList?.items) return state;

      const updatedItems = state.currentList.items.map((item) =>
        item.id === itemId ? { ...item, ...updates } : item
      );

      const updatedList = {
        ...state.currentList,
        items: updatedItems,
      };

      return {
        currentList: updatedList,
        lists: state.lists.map((list) =>
          list.id === updatedList.id ? updatedList : list
        ),
      };
    });
  },

  removeItemFromCurrentList: (itemId: number) => {
    set((state) => {
      if (!state.currentList?.items) return state;

      const itemToRemove = state.currentList.items.find(
        (item) => item.id === itemId
      );
      const updatedItems = state.currentList.items.filter(
        (item) => item.id !== itemId
      );

      const updatedList = {
        ...state.currentList,
        items: updatedItems,
        summary:
          state.currentList.summary && itemToRemove
            ? {
                ...state.currentList.summary,
                total_items: state.currentList.summary.total_items - 1,
                remaining_items: state.currentList.summary.remaining_items - 1,
                total_quantity:
                  state.currentList.summary.total_quantity -
                  itemToRemove.quantity,
              }
            : state.currentList.summary,
      };

      return {
        currentList: updatedList,
        lists: state.lists.map((list) =>
          list.id === updatedList.id ? updatedList : list
        ),
        selectedItems: new Set(
          [...state.selectedItems].filter((id) => id !== itemId)
        ),
      };
    });
  },

  setLoading: (isLoading: boolean) => {
    set({ isLoading });
  },

  setError: (error: string | null) => {
    set({ error, isLoading: false });
  },

  toggleItemSelection: (itemId: number) => {
    set((state) => {
      const newSelection = new Set(state.selectedItems);
      if (newSelection.has(itemId)) {
        newSelection.delete(itemId);
      } else {
        newSelection.add(itemId);
      }
      return { selectedItems: newSelection };
    });
  },

  selectAllItems: () => {
    set((state) => {
      if (!state.currentList?.items) return state;

      const allItemIds = new Set(
        state.currentList.items.map((item) => item.id)
      );
      return { selectedItems: allItemIds };
    });
  },

  clearSelection: () => {
    set({ selectedItems: new Set() });
  },

  reset: () => {
    set(initialState);
  },

  // Animation actions
  startProductAnimation: (product: ProductSuggestion) => {
    set({
      animatingProduct: product,
      animationInProgress: true,
    });

    // Dispatch custom event for the shopping list icon to listen to
    window.dispatchEvent(
      new CustomEvent("shopping-list-product-added", {
        detail: { productId: product.id, product },
      })
    );
  },

  endProductAnimation: () => {
    set({
      animatingProduct: null,
      animationInProgress: false,
    });
  },

  // Quick-add functionality
  quickAddProduct: async (product: ProductSuggestion, quantity = 1) => {
    const { startProductAnimation, endProductAnimation, setError, setLoading } =
      get();

    try {
      setLoading(true);
      setError(null);

      // Start animation
      startProductAnimation(product);

      // Call the quick-add API using the API client
      const result = await apiClient.quickAddProduct(product.id, quantity);

      // Refresh shopping lists to get updated data
      const listsData = await apiClient.getShoppingLists();
      set({ lists: listsData.data?.items || [] });

      // End animation after a delay to show the effect
      setTimeout(() => {
        endProductAnimation();
      }, 600);

      // Show success notification
      window.dispatchEvent(
        new CustomEvent("shopping-list-success", {
          detail: {
            message: `Added "${product.name}" to shopping list`,
            product,
            item: result,
          },
        })
      );
    } catch (error) {
      console.error("Error adding product to shopping list:", error);
      setError(
        error instanceof Error ? error.message : "Failed to add product"
      );
      endProductAnimation();

      // Show error notification
      window.dispatchEvent(
        new CustomEvent("shopping-list-error", {
          detail: {
            message: `Failed to add "${product.name}" to shopping list`,
            error,
          },
        })
      );
    } finally {
      setLoading(false);
    }
  },

  // Utility functions
  getTotalItemCount: () => {
    const { lists } = get();
    return lists.reduce((total, list) => {
      return total + (list.items?.length || 0);
    }, 0);
  },

  getDefaultList: () => {
    const { lists } = get();
    return lists.find((list) => list.is_default) || lists[0] || null;
  },
}));

// Helper functions
export const getListById = (
  lists: ShoppingList[],
  id: number
): ShoppingList | undefined => {
  return lists.find((list) => list.id === id);
};

export const getListItemCount = (list: ShoppingList): number => {
  return list.items?.length || 0;
};

export const getListTotalQuantity = (list: ShoppingList): number => {
  return list.items?.reduce((total, item) => total + item.quantity, 0) || 0;
};

export const isProductInList = (
  list: ShoppingList,
  productId: number
): boolean => {
  return list.items?.some((item) => item.product_id === productId) || false;
};

export const canAddProductToList = (
  list: ShoppingList,
  product: Product
): boolean => {
  return !isProductInList(list, product.id);
};

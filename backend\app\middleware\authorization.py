"""
Authorization Middleware for Role-Based Access Control (RBAC)

This module provides comprehensive authorization middleware for checking user roles
and permissions in API endpoints. It includes decorators and dependency functions
for role-based access control.
"""

from functools import wraps
from typing import List, Optional, Union, Callable, Any
from fastapi import HTTPException, status, Depends
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials

from ..models.user import User
from ..dependencies import get_current_active_user
from ..utils.audit_logger import get_audit_logger, AuditEventType, AuditSeverity


class AuthorizationError(HTTPException):
    """Custom exception for authorization errors."""
    
    def __init__(self, detail: str = "Insufficient permissions"):
        super().__init__(
            status_code=status.HTTP_403_FORBIDDEN,
            detail=detail
        )


class RoleChecker:
    """
    Role-based authorization checker.
    
    This class provides methods to check if a user has specific roles or permissions.
    It can be used as a dependency in FastAPI endpoints.
    """
    
    def __init__(
        self,
        required_roles: Optional[List[str]] = None,
        required_permissions: Optional[List[str]] = None,
        require_all_roles: bool = False,
        require_all_permissions: bool = False,
        allow_superuser: bool = True
    ):
        """
        Initialize role checker.
        
        Args:
            required_roles: List of roles that the user must have
            required_permissions: List of permissions that the user must have
            require_all_roles: If True, user must have ALL specified roles. If False, user needs ANY of the roles
            require_all_permissions: If True, user must have ALL specified permissions. If False, user needs ANY
            allow_superuser: If True, superusers bypass all role/permission checks
        """
        self.required_roles = required_roles or []
        self.required_permissions = required_permissions or []
        self.require_all_roles = require_all_roles
        self.require_all_permissions = require_all_permissions
        self.allow_superuser = allow_superuser
    
    def __call__(self, current_user: User = Depends(get_current_active_user)) -> User:
        """
        Check if the current user has the required roles and permissions.
        
        Args:
            current_user: The current authenticated user
            
        Returns:
            User: The current user if authorization succeeds
            
        Raises:
            AuthorizationError: If the user doesn't have sufficient permissions
        """
        # Allow superusers to bypass all checks if configured
        if self.allow_superuser and current_user.is_superuser:
            return current_user
        
        # Check required roles
        if self.required_roles:
            user_roles = set(current_user.roles or [])
            required_roles_set = set(self.required_roles)
            
            if self.require_all_roles:
                # User must have ALL required roles
                if not required_roles_set.issubset(user_roles):
                    missing_roles = required_roles_set - user_roles
                    raise AuthorizationError(
                        f"Missing required roles: {', '.join(missing_roles)}"
                    )
            else:
                # User must have ANY of the required roles
                if not required_roles_set.intersection(user_roles):
                    raise AuthorizationError(
                        f"User must have one of these roles: {', '.join(self.required_roles)}"
                    )
        
        # Check required permissions
        if self.required_permissions:
            user_permissions = set(current_user.permissions or [])
            required_permissions_set = set(self.required_permissions)

            if self.require_all_permissions:
                # User must have ALL required permissions
                if not required_permissions_set.issubset(user_permissions):
                    missing_permissions = required_permissions_set - user_permissions

                    # Log access denied event (note: sync logging for middleware)
                    try:
                        audit_logger = get_audit_logger()
                        # Use sync logging for middleware context
                        import asyncio
                        asyncio.create_task(audit_logger.log_authorization_event(
                            event_type=AuditEventType.ACCESS_DENIED,
                            user_id=current_user.id,
                            user_email=current_user.email,
                            resource="api_endpoint",
                            action="permission_check",
                            success=False,
                            message=f"Access denied for {current_user.email}: missing permissions",
                            details={
                                "required_permissions": list(required_permissions_set),
                                "user_permissions": list(user_permissions),
                                "missing_permissions": list(missing_permissions),
                                "require_all": True
                            },
                            severity=AuditSeverity.MEDIUM
                        ))
                    except RuntimeError:
                        # Audit logger not initialized, continue without logging
                        pass

                    raise AuthorizationError(
                        f"Missing required permissions: {', '.join(missing_permissions)}"
                    )
            else:
                # User must have ANY of the required permissions
                if not required_permissions_set.intersection(user_permissions):
                    # Log access denied event (note: sync logging for middleware)
                    try:
                        audit_logger = get_audit_logger()
                        # Use sync logging for middleware context
                        import asyncio
                        asyncio.create_task(audit_logger.log_authorization_event(
                            event_type=AuditEventType.ACCESS_DENIED,
                            user_id=current_user.id,
                            user_email=current_user.email,
                            resource="api_endpoint",
                            action="permission_check",
                            success=False,
                            message=f"Access denied for {current_user.email}: no required permissions",
                            details={
                                "required_permissions": list(required_permissions_set),
                                "user_permissions": list(user_permissions),
                                "require_all": False
                            },
                            severity=AuditSeverity.MEDIUM
                        ))
                    except RuntimeError:
                        # Audit logger not initialized, continue without logging
                        pass

                    raise AuthorizationError(
                        f"User must have one of these permissions: {', '.join(self.required_permissions)}"
                    )

        # Log successful access if permissions were checked
        if self.required_permissions or self.required_roles:
            try:
                audit_logger = get_audit_logger()
                # Use sync logging for middleware context
                import asyncio
                asyncio.create_task(audit_logger.log_authorization_event(
                    event_type=AuditEventType.ACCESS_GRANTED,
                    user_id=current_user.id,
                    user_email=current_user.email,
                    resource="api_endpoint",
                    action="authorization_check",
                    success=True,
                    message=f"Access granted for {current_user.email}",
                    details={
                        "required_roles": self.required_roles or [],
                        "required_permissions": self.required_permissions or [],
                        "user_roles": current_user.roles or [],
                        "user_permissions": current_user.permissions or []
                    },
                    severity=AuditSeverity.LOW
                ))
            except RuntimeError:
                # Audit logger not initialized, continue without logging
                pass

        return current_user


# Convenience functions for common authorization patterns

def require_role(role: str, allow_superuser: bool = True) -> Callable:
    """
    Dependency function to require a specific role.
    
    Args:
        role: The required role name
        allow_superuser: Whether to allow superusers to bypass this check
        
    Returns:
        Callable: FastAPI dependency function
    """
    return RoleChecker(
        required_roles=[role],
        allow_superuser=allow_superuser
    )


def require_any_role(roles: List[str], allow_superuser: bool = True) -> Callable:
    """
    Dependency function to require any of the specified roles.
    
    Args:
        roles: List of acceptable role names
        allow_superuser: Whether to allow superusers to bypass this check
        
    Returns:
        Callable: FastAPI dependency function
    """
    return RoleChecker(
        required_roles=roles,
        require_all_roles=False,
        allow_superuser=allow_superuser
    )


def require_all_roles(roles: List[str], allow_superuser: bool = True) -> Callable:
    """
    Dependency function to require all of the specified roles.
    
    Args:
        roles: List of required role names
        allow_superuser: Whether to allow superusers to bypass this check
        
    Returns:
        Callable: FastAPI dependency function
    """
    return RoleChecker(
        required_roles=roles,
        require_all_roles=True,
        allow_superuser=allow_superuser
    )


def require_permission(permission: str, allow_superuser: bool = True) -> Callable:
    """
    Dependency function to require a specific permission.
    
    Args:
        permission: The required permission name
        allow_superuser: Whether to allow superusers to bypass this check
        
    Returns:
        Callable: FastAPI dependency function
    """
    return RoleChecker(
        required_permissions=[permission],
        allow_superuser=allow_superuser
    )


def require_any_permission(permissions: List[str], allow_superuser: bool = True) -> Callable:
    """
    Dependency function to require any of the specified permissions.
    
    Args:
        permissions: List of acceptable permission names
        allow_superuser: Whether to allow superusers to bypass this check
        
    Returns:
        Callable: FastAPI dependency function
    """
    return RoleChecker(
        required_permissions=permissions,
        require_all_permissions=False,
        allow_superuser=allow_superuser
    )


def require_all_permissions(permissions: List[str], allow_superuser: bool = True) -> Callable:
    """
    Dependency function to require all of the specified permissions.
    
    Args:
        permissions: List of required permission names
        allow_superuser: Whether to allow superusers to bypass this check
        
    Returns:
        Callable: FastAPI dependency function
    """
    return RoleChecker(
        required_permissions=permissions,
        require_all_permissions=True,
        allow_superuser=allow_superuser
    )


# Predefined authorization dependencies for common use cases

# Role-based dependencies
require_superadmin = require_role("superadmin")
require_admin = require_any_role(["superadmin", "admin"])
require_user = require_any_role(["superadmin", "admin", "user"])

# Permission-based dependencies for common operations
require_user_management = require_permission("users.manage")
require_role_management = require_permission("roles.manage")
require_permission_management = require_permission("permissions.manage")
require_system_admin = require_permission("system.admin")

# Search and shopping list permissions (for future use)
require_search_access = require_permission("search.access")
require_shopping_list_access = require_permission("shopping_lists.access")


def check_resource_ownership(
    resource_user_id: str,
    current_user: User,
    allow_admin_override: bool = True
) -> bool:
    """
    Check if the current user owns a resource or has admin privileges.
    
    Args:
        resource_user_id: The user ID that owns the resource
        current_user: The current authenticated user
        allow_admin_override: Whether to allow admin users to access any resource
        
    Returns:
        bool: True if the user can access the resource, False otherwise
    """
    # User owns the resource
    if str(current_user.id) == str(resource_user_id):
        return True
    
    # Admin override (superuser or admin role)
    if allow_admin_override:
        if current_user.is_superuser:
            return True
        
        if current_user.roles and ("superadmin" in current_user.roles or "admin" in current_user.roles):
            return True
    
    return False


def require_resource_ownership(
    resource_user_id: str,
    allow_admin_override: bool = True
) -> Callable:
    """
    Create a dependency that checks resource ownership.
    
    Args:
        resource_user_id: The user ID that owns the resource
        allow_admin_override: Whether to allow admin users to access any resource
        
    Returns:
        Callable: FastAPI dependency function
    """
    def check_ownership(current_user: User = Depends(get_current_active_user)) -> User:
        if not check_resource_ownership(resource_user_id, current_user, allow_admin_override):
            raise AuthorizationError("You don't have permission to access this resource")
        return current_user
    
    return check_ownership

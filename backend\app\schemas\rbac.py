"""
RBAC (Role-Based Access Control) Pydantic schemas
Schemas for roles, permissions, and user role assignments
"""

from datetime import datetime
from typing import Optional, List, Dict, Any
from pydantic import BaseModel, Field, validator


# Role Schemas
class RoleBase(BaseModel):
    """Base role schema with common fields."""
    name: str = Field(..., min_length=1, max_length=50, description="Role name (unique)")
    display_name: str = Field(..., min_length=1, max_length=100, description="Human-readable role name")
    description: Optional[str] = Field(None, max_length=500, description="Role description")
    metadata: Optional[Dict[str, Any]] = Field(default_factory=dict, description="Additional role metadata")

    @validator('name')
    def validate_name(cls, v):
        """Validate role name format."""
        if not v.replace('_', '').replace('-', '').isalnum():
            raise ValueError('Role name must contain only alphanumeric characters, hyphens, and underscores')
        return v.lower()


class RoleCreate(RoleBase):
    """Schema for creating a new role."""
    pass


class RoleUpdate(BaseModel):
    """Schema for updating an existing role."""
    display_name: Optional[str] = Field(None, min_length=1, max_length=100)
    description: Optional[str] = Field(None, max_length=500)
    metadata: Optional[Dict[str, Any]] = None
    is_active: Optional[bool] = None


class RoleResponse(RoleBase):
    """Schema for role response."""
    id: str
    is_active: bool
    is_system_role: bool
    created_at: datetime
    updated_at: datetime
    
    class Config:
        from_attributes = True


class RoleWithPermissions(RoleResponse):
    """Schema for role with its permissions."""
    permissions: List[str] = Field(default_factory=list, description="List of permission names")
    permission_count: int = Field(0, description="Total number of permissions")


class RoleWithUsers(RoleResponse):
    """Schema for role with assigned users."""
    user_count: int = Field(0, description="Number of users with this role")
    users: Optional[List[Dict[str, Any]]] = Field(default_factory=list, description="List of users with this role")


# Permission Schemas
class PermissionBase(BaseModel):
    """Base permission schema with common fields."""
    name: str = Field(..., min_length=1, max_length=100, description="Permission name (unique)")
    resource: str = Field(..., min_length=1, max_length=50, description="Resource name")
    action: str = Field(..., min_length=1, max_length=50, description="Action name")
    description: Optional[str] = Field(None, max_length=500, description="Permission description")

    @validator('name')
    def validate_name_format(cls, v):
        """Validate permission name format (resource.action)."""
        if '.' not in v:
            raise ValueError('Permission name must be in format "resource.action"')
        return v.lower()

    @validator('resource')
    def validate_resource(cls, v):
        """Validate resource name."""
        if not v.replace('_', '').isalnum():
            raise ValueError('Resource name must contain only alphanumeric characters and underscores')
        return v.lower()

    @validator('action')
    def validate_action(cls, v):
        """Validate action name."""
        if not v.replace('_', '').isalnum():
            raise ValueError('Action name must contain only alphanumeric characters and underscores')
        return v.lower()


class PermissionCreate(PermissionBase):
    """Schema for creating a new permission."""
    pass


class PermissionUpdate(BaseModel):
    """Schema for updating an existing permission."""
    description: Optional[str] = Field(None, max_length=500)


class PermissionResponse(PermissionBase):
    """Schema for permission response."""
    id: str
    is_system_permission: bool
    created_at: datetime
    
    class Config:
        from_attributes = True


# User Role Assignment Schemas
class UserRoleAssignmentBase(BaseModel):
    """Base schema for user role assignments."""
    user_id: str = Field(..., description="User ID")
    role_id: str = Field(..., description="Role ID")
    expires_at: Optional[datetime] = Field(None, description="Role assignment expiration date")
    notes: Optional[str] = Field(None, max_length=500, description="Assignment notes")


class UserRoleAssignmentCreate(UserRoleAssignmentBase):
    """Schema for creating a user role assignment."""
    pass


class UserRoleAssignmentUpdate(BaseModel):
    """Schema for updating a user role assignment."""
    expires_at: Optional[datetime] = None
    notes: Optional[str] = Field(None, max_length=500)
    is_active: Optional[bool] = None


class UserRoleAssignmentResponse(UserRoleAssignmentBase):
    """Schema for user role assignment response."""
    id: str
    assigned_by: Optional[str] = None
    assigned_at: datetime
    is_active: bool
    created_at: datetime
    updated_at: datetime
    
    class Config:
        from_attributes = True


class UserRoleAssignmentWithDetails(UserRoleAssignmentResponse):
    """Schema for user role assignment with user and role details."""
    user_email: Optional[str] = None
    user_full_name: Optional[str] = None
    role_name: Optional[str] = None
    role_display_name: Optional[str] = None


# Role Permission Assignment Schemas
class RolePermissionAssignmentBase(BaseModel):
    """Base schema for role permission assignments."""
    role_id: str = Field(..., description="Role ID")
    permission_id: str = Field(..., description="Permission ID")


class RolePermissionAssignmentCreate(RolePermissionAssignmentBase):
    """Schema for creating a role permission assignment."""
    pass


class RolePermissionAssignmentResponse(RolePermissionAssignmentBase):
    """Schema for role permission assignment response."""
    id: str
    granted_by: Optional[str] = None
    granted_at: datetime
    is_active: bool
    created_at: datetime
    updated_at: datetime
    
    class Config:
        from_attributes = True


# Bulk Operations Schemas
class BulkRolePermissionAssignment(BaseModel):
    """Schema for bulk role permission assignments."""
    role_id: str = Field(..., description="Role ID")
    permission_ids: List[str] = Field(..., min_items=1, description="List of permission IDs to assign")


class BulkUserRoleAssignment(BaseModel):
    """Schema for bulk user role assignments."""
    user_ids: List[str] = Field(..., min_items=1, description="List of user IDs")
    role_id: str = Field(..., description="Role ID to assign")
    expires_at: Optional[datetime] = None
    notes: Optional[str] = Field(None, max_length=500)


# Response Schemas for Lists
class RoleListResponse(BaseModel):
    """Schema for paginated role list response."""
    roles: List[RoleResponse]
    total: int
    page: int
    page_size: int
    total_pages: int


class PermissionListResponse(BaseModel):
    """Schema for paginated permission list response."""
    permissions: List[PermissionResponse]
    total: int
    page: int
    page_size: int
    total_pages: int


class UserRoleAssignmentListResponse(BaseModel):
    """Schema for paginated user role assignment list response."""
    assignments: List[UserRoleAssignmentWithDetails]
    total: int
    page: int
    page_size: int
    total_pages: int


# Summary Schemas
class RoleSummary(BaseModel):
    """Schema for role summary statistics."""
    total_roles: int
    system_roles: int
    custom_roles: int
    active_roles: int
    inactive_roles: int


class PermissionSummary(BaseModel):
    """Schema for permission summary statistics."""
    total_permissions: int
    system_permissions: int
    custom_permissions: int
    total_resources: int
    total_actions: int


# User Role Assignment Schemas
class UserRoleAssignmentCreate(BaseModel):
    """Schema for creating a user-role assignment."""
    user_id: str = Field(..., description="User ID to assign role to")
    role_id: str = Field(..., description="Role ID to assign to user")


class UserRoleAssignmentUpdate(BaseModel):
    """Schema for updating a user-role assignment."""
    is_active: Optional[bool] = Field(None, description="Whether the assignment is active")
    notes: Optional[str] = Field(None, max_length=500, description="Notes about the assignment")


class UserRoleAssignmentResponse(BaseModel):
    """Schema for user-role assignment response."""
    id: str
    user_id: str
    role_id: str
    assigned_by: Optional[str]
    assigned_at: datetime
    is_active: bool
    notes: Optional[str]
    created_at: datetime
    updated_at: datetime


class UserRoleAssignmentList(BaseModel):
    """Schema for paginated user-role assignment list."""
    assignments: List[UserRoleAssignmentResponse]
    total: int
    skip: int
    limit: int


class UserWithRolesResponse(BaseModel):
    """Schema for user with roles response."""
    id: str
    email: str
    full_name: Optional[str]
    is_active: bool
    is_superuser: bool
    created_at: datetime
    updated_at: Optional[datetime]
    roles: List[str]
    permissions: List[str]


class UserRoleSummary(BaseModel):
    """Schema for user role assignment summary."""
    total_assignments: int
    active_assignments: int
    expired_assignments: int
    assignments_by_role: Dict[str, int]


# Error Response Schemas
class RBACErrorResponse(BaseModel):
    """Schema for RBAC-specific error responses."""
    error: str
    detail: str
    error_code: Optional[str] = None
    suggestions: Optional[List[str]] = None

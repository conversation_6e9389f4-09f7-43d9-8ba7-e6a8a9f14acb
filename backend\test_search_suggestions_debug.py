#!/usr/bin/env python3
"""
Debug Search Suggestions UI
Focus specifically on why suggestions dropdown doesn't appear
"""

import asyncio
from playwright.async_api import async_playwright


async def debug_search_suggestions():
    """Debug search suggestions dropdown."""
    print("🔍 Debugging Search Suggestions UI")
    
    async with async_playwright() as p:
        browser = await p.chromium.launch(headless=False, slow_mo=500)
        page = await browser.new_page()
        
        # Track console messages
        console_messages = []
        page.on("console", lambda msg: console_messages.append(f"{msg.type}: {msg.text}"))
        
        try:
            # Navigate to frontend
            print("📍 Loading frontend...")
            await page.goto("http://localhost:3000", wait_until="networkidle")
            await page.wait_for_timeout(3000)
            
            # Find search input
            search_input = page.locator('input[placeholder*="search" i]').first
            if not await search_input.is_visible():
                print("❌ Search input not found")
                return False
            
            print("✅ Search input found")
            
            # Click on search input to focus
            await search_input.click()
            await page.wait_for_timeout(1000)
            
            # Type slowly to trigger suggestions
            print("🔤 Typing 'comp' to trigger suggestions...")
            await search_input.type("comp", delay=300)
            await page.wait_for_timeout(2000)
            
            # Take screenshot after typing
            await page.screenshot(path="debug_screenshots/suggestions_after_typing.png")
            
            # Check for suggestions dropdown
            print("🔍 Looking for suggestions dropdown...")
            
            # Try multiple selectors for suggestions
            suggestion_selectors = [
                '.absolute.top-full',  # Based on the CSS classes in SearchInput
                '[class*="suggestions"]',
                '[class*="dropdown"]',
                'div:has-text("Suggestions")',
                'div:has-text("Recent")',
            ]
            
            suggestions_found = False
            for selector in suggestion_selectors:
                try:
                    element = page.locator(selector).first
                    if await element.is_visible():
                        print(f"✅ Found suggestions element: {selector}")
                        suggestions_found = True
                        
                        # Get text content
                        text_content = await element.text_content()
                        print(f"📝 Content: {text_content[:200]}...")
                        break
                except:
                    continue
            
            if not suggestions_found:
                print("❌ No suggestions dropdown found")
                
                # Debug: Check if showSuggestions state is true
                print("🔍 Debugging React state...")
                
                # Check if there are any elements with suggestion-related classes
                all_divs = await page.locator('div').all()
                print(f"📊 Total div elements: {len(all_divs)}")
                
                # Look for any hidden elements that might be suggestions
                hidden_suggestions = await page.locator('div[class*="absolute"]').count()
                print(f"📊 Absolute positioned divs: {hidden_suggestions}")
                
                # Check React DevTools if available
                react_state = await page.evaluate("""
                    () => {
                        // Try to access React state
                        const input = document.querySelector('input[placeholder*="search"]');
                        if (input && input._reactInternalFiber) {
                            return "React fiber found";
                        }
                        return "No React fiber access";
                    }
                """)
                print(f"⚛️ React state access: {react_state}")
            
            # Check network requests for suggestions
            print("🌐 Checking network requests...")
            
            # Wait a bit more and check for API calls
            await page.wait_for_timeout(2000)
            
            # Check if suggestions data is in the store
            suggestions_data = await page.evaluate("""
                () => {
                    // Try to access Zustand store
                    if (window.__ZUSTAND_STORE__) {
                        return window.__ZUSTAND_STORE__.suggestions || [];
                    }
                    return "No store access";
                }
            """)
            print(f"🗄️ Store suggestions: {suggestions_data}")
            
            # Final screenshot
            await page.screenshot(path="debug_screenshots/final_suggestions_debug.png")
            
            return suggestions_found
            
        except Exception as e:
            print(f"❌ Debug failed: {e}")
            await page.screenshot(path="debug_screenshots/error_suggestions.png")
            return False
        finally:
            await browser.close()


if __name__ == "__main__":
    success = asyncio.run(debug_search_suggestions())
    if success:
        print("\n✅ Suggestions dropdown working!")
    else:
        print("\n❌ Suggestions dropdown not working - check screenshots")

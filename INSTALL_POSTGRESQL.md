# PostgreSQL Installation Guide for ProfiDent

## ❌ Current Status: PostgreSQL NOT INSTALLED

This system does not have PostgreSQL installed. You need to install it manually to complete the database setup task.

## 🔧 Manual Installation Required

### Option 1: Download Official PostgreSQL Installer (Recommended)

1. **Download PostgreSQL 16**
   - Go to: https://www.postgresql.org/download/windows/
   - Download PostgreSQL 16.x installer
   - Run as Administrator

2. **Installation Steps**
   - Choose installation directory (default: `C:\Program Files\PostgreSQL\16`)
   - Set password for postgres user (remember this!)
   - Choose port 5432 (default)
   - Select locale (default is fine)
   - Install

3. **Verify Installation**
   ```bash
   # Open new terminal after installation
   psql --version
   # Should show: psql (PostgreSQL) 16.x
   ```

### Option 2: Using Chocolatey (As Administrator)

1. **Open PowerShell as Administrator**
   - Right-click Start button → "Windows PowerShell (Admin)"

2. **Install PostgreSQL**
   ```powershell
   choco install postgresql --version=16.0.0 --yes
   ```

3. **Start PostgreSQL Service**
   ```powershell
   Start-Service postgresql-x64-16
   ```

### Option 3: Using Docker (If Docker is installed)

1. **Install Docker Desktop**
   - Download from: https://www.docker.com/products/docker-desktop/

2. **Run PostgreSQL with pgvector**
   ```bash
   docker-compose -f docker-compose.dev.yml up -d
   ```

## 🔌 Install pgvector Extension

After PostgreSQL is installed:

### Method 1: Pre-built Binaries (Easiest)
1. Download pgvector from: https://github.com/pgvector/pgvector/releases
2. Extract to PostgreSQL installation directory
3. Restart PostgreSQL service

### Method 2: Using Stack Builder (PostgreSQL GUI)
1. Open Stack Builder (installed with PostgreSQL)
2. Select your PostgreSQL installation
3. Look for pgvector in Extensions category
4. Install and restart PostgreSQL

### Method 3: Manual Compilation (Advanced)
```bash
# Requires Visual Studio Build Tools
git clone https://github.com/pgvector/pgvector.git
cd pgvector
# Follow compilation instructions for Windows
```

## ✅ Verification Steps

After installation, run these commands:

```bash
# Test PostgreSQL connection
psql -U postgres -h localhost

# In psql, test pgvector
CREATE EXTENSION vector;
SELECT '[1,2,3]'::vector <-> '[4,5,6]'::vector;
# Should return a distance value

# Exit psql
\q
```

## 🚀 Next Steps After Installation

1. **Create ProfiDent Database**
   ```bash
   createdb -U postgres profident_dev
   ```

2. **Run Database Initialization**
   ```bash
   psql -U postgres -d profident_dev -f scripts/init-db.sql
   ```

3. **Update Environment Variables**
   Create `.env` file in backend directory:
   ```
   DATABASE_URL=postgresql+asyncpg://postgres:your_password@localhost:5432/profident_dev
   ```

4. **Test Connection**
   ```bash
   psql -U postgres -d profident_dev -h localhost
   ```

## 🆘 Troubleshooting

### Common Issues:

1. **"psql: command not found"**
   - Add PostgreSQL bin directory to PATH
   - Default: `C:\Program Files\PostgreSQL\16\bin`

2. **Connection refused**
   - Check if PostgreSQL service is running
   - Windows Services → postgresql-x64-16 → Start

3. **Permission denied**
   - Run commands as Administrator
   - Check PostgreSQL service account permissions

4. **pgvector not found**
   - Ensure pgvector is properly installed
   - Restart PostgreSQL service after installation

## 📋 Installation Checklist

- [ ] PostgreSQL 16 installed
- [ ] PostgreSQL service running
- [ ] psql command available in terminal
- [ ] pgvector extension installed
- [ ] Can connect to PostgreSQL
- [ ] Can create vector operations
- [ ] ProfiDent database created
- [ ] Database schema initialized

## 🔄 Alternative: Use Cloud Database

If local installation is problematic, consider:

1. **Supabase** (Recommended)
   - Free tier available
   - pgvector pre-installed
   - Web interface for management
   - Sign up at: https://supabase.com

2. **AWS RDS PostgreSQL**
   - Requires AWS account
   - Manual pgvector installation needed

3. **Google Cloud SQL**
   - Requires Google Cloud account
   - Manual pgvector installation needed

---

**⚠️ IMPORTANT**: This task is NOT complete until PostgreSQL is actually installed and tested on your system. Please follow one of the installation methods above and verify the installation works before proceeding.

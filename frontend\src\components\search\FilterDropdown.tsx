import React, { useState, useRef, useEffect } from "react";
import { ChevronDown, Search, X, Check } from "lucide-react";
import { cn } from "../../lib/utils";

interface FilterOption {
  name: string;
  count: number;
}

interface FilterDropdownProps {
  label: string;
  placeholder: string;
  options: FilterOption[];
  selectedValues: string[];
  onSelectionChange: (values: string[]) => void;
  loading?: boolean;
  searchable?: boolean;
  multiSelect?: boolean;
  maxHeight?: string;
}

const FilterDropdown = ({
  label,
  placeholder,
  options,
  selectedValues,
  onSelectionChange,
  loading = false,
  searchable = true,
  multiSelect = true,
  maxHeight = "max-h-64",
}: FilterDropdownProps) => {
  const [isOpen, setIsOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");
  const dropdownRef = useRef<HTMLDivElement>(null);
  const searchInputRef = useRef<HTMLInputElement>(null);

  // Filter options based on search query with error handling
  const filteredOptions = React.useMemo(() => {
    try {
      if (!Array.isArray(options)) {
        console.warn("FilterDropdown: options is not an array:", options);
        return [];
      }
      return options.filter((option) => {
        if (!option || typeof option.name !== "string") {
          console.warn("FilterDropdown: invalid option:", option);
          return false;
        }
        return option.name.toLowerCase().includes(searchQuery.toLowerCase());
      });
    } catch (error) {
      console.error("FilterDropdown: Error filtering options:", error);
      return [];
    }
  }, [options, searchQuery]);

  // Handle click outside to close dropdown
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as Node)
      ) {
        setIsOpen(false);
        setSearchQuery("");
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, []);

  // Focus search input when dropdown opens
  useEffect(() => {
    if (isOpen && searchable && searchInputRef.current) {
      searchInputRef.current.focus();
    }
  }, [isOpen, searchable]);

  const handleToggleOption = (optionName: string) => {
    try {
      if (!optionName || typeof optionName !== "string") {
        console.warn("FilterDropdown: Invalid option name:", optionName);
        return;
      }

      if (multiSelect) {
        const newSelection = selectedValues.includes(optionName)
          ? selectedValues.filter((v) => v !== optionName)
          : [...selectedValues, optionName];
        onSelectionChange(newSelection);
      } else {
        onSelectionChange([optionName]);
        setIsOpen(false);
        setSearchQuery("");
      }
    } catch (error) {
      console.error("FilterDropdown: Error toggling option:", error);
    }
  };

  const handleClearAll = () => {
    onSelectionChange([]);
  };

  const getDisplayText = () => {
    if (selectedValues.length === 0) {
      return placeholder;
    }
    if (selectedValues.length === 1) {
      return selectedValues[0];
    }
    return `${selectedValues.length} selected`;
  };

  return (
    <div className="relative" ref={dropdownRef}>
      {/* Dropdown Button */}
      <button
        type="button"
        onClick={() => {
          try {
            setIsOpen(!isOpen);
          } catch (error) {
            console.error("FilterDropdown: Error toggling dropdown:", error);
          }
        }}
        className={cn(
          "w-full flex items-center justify-between px-4 py-3 text-left bg-white border border-gray-300 rounded-lg shadow-sm hover:border-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-colors",
          isOpen && "border-primary-500 ring-2 ring-primary-500",
          selectedValues.length > 0 && "border-primary-400 bg-primary-50"
        )}
      >
        <div className="flex flex-col">
          <span className="text-xs text-gray-500 font-medium">{label}</span>
          <span
            className={cn(
              "text-sm",
              selectedValues.length > 0
                ? "text-gray-900 font-medium"
                : "text-gray-500"
            )}
          >
            {getDisplayText()}
          </span>
        </div>
        <div className="flex items-center space-x-2">
          {selectedValues.length > 0 && (
            <button
              type="button"
              onClick={(e) => {
                e.stopPropagation();
                handleClearAll();
              }}
              className="p-1 text-gray-400 hover:text-gray-600 rounded"
            >
              <X className="h-4 w-4" />
            </button>
          )}
          <ChevronDown
            className={cn(
              "h-4 w-4 text-gray-400 transition-transform",
              isOpen && "transform rotate-180"
            )}
          />
        </div>
      </button>

      {/* Dropdown Menu */}
      {isOpen && (
        <div
          className={cn(
            "absolute top-full left-0 right-0 mt-1 bg-white border border-gray-200 rounded-lg shadow-xl z-[9999]",
            maxHeight,
            "overflow-hidden"
          )}
        >
          {/* Search Input */}
          {searchable && (
            <div className="p-3 border-b border-gray-100">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <input
                  ref={searchInputRef}
                  type="text"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  placeholder={`Search ${label.toLowerCase()}...`}
                  className="w-full pl-10 pr-4 py-2 text-sm border border-gray-200 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                />
              </div>
            </div>
          )}

          {/* Options List */}
          <div className="overflow-y-auto max-h-48">
            {loading ? (
              <div className="p-4 text-center text-gray-500">
                <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary-500 mx-auto"></div>
                <span className="mt-2 block text-sm">Loading...</span>
              </div>
            ) : filteredOptions.length === 0 ? (
              <div className="p-4 text-center text-gray-500 text-sm">
                {searchQuery
                  ? `No ${label.toLowerCase()} found matching "${searchQuery}"`
                  : `No ${label.toLowerCase()} available`}
              </div>
            ) : (
              <div className="py-1">
                {filteredOptions.map((option) => {
                  const isSelected = selectedValues.includes(option.name);
                  return (
                    <button
                      key={option.name}
                      type="button"
                      onClick={() => handleToggleOption(option.name)}
                      className={cn(
                        "w-full flex items-center justify-between px-4 py-2 text-sm hover:bg-gray-50 transition-colors",
                        isSelected && "bg-primary-50 text-primary-700"
                      )}
                    >
                      <div className="flex items-center space-x-3">
                        {multiSelect && (
                          <div
                            className={cn(
                              "w-4 h-4 border border-gray-300 rounded flex items-center justify-center",
                              isSelected && "bg-primary-500 border-primary-500"
                            )}
                          >
                            {isSelected && (
                              <Check className="h-3 w-3 text-white" />
                            )}
                          </div>
                        )}
                        <span className="truncate">{option.name}</span>
                      </div>
                      <span className="text-xs text-gray-500 ml-2">
                        {option.count.toLocaleString()}
                      </span>
                    </button>
                  );
                })}
              </div>
            )}
          </div>

          {/* Footer with selection count */}
          {multiSelect && selectedValues.length > 0 && (
            <div className="p-3 border-t border-gray-100 bg-gray-50">
              <div className="flex items-center justify-between text-xs text-gray-600">
                <span>{selectedValues.length} selected</span>
                <button
                  type="button"
                  onClick={handleClearAll}
                  className="text-primary-600 hover:text-primary-700 font-medium"
                >
                  Clear all
                </button>
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default FilterDropdown;

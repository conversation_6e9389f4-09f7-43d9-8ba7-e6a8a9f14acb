"""
Security utilities for authentication and password handling
"""

import secrets
from datetime import datetime, timedelta
from typing import Optional, Union
from passlib.context import Crypt<PERSON>ontext
from jose import JWTError, jwt
import hashlib

try:
    from ..config import settings
except ImportError:
    from config import settings

# Enhanced password hashing context with multiple algorithms
# Supports Argon2, bcrypt, and scrypt with proper configuration
pwd_context = CryptContext(
    schemes=["argon2", "bcrypt", "scrypt"],
    deprecated="auto",

    # Argon2 configuration (recommended for new passwords)
    argon2__time_cost=settings.ARGON2_TIME_COST,
    argon2__memory_cost=settings.ARGON2_MEMORY_COST,
    argon2__parallelism=settings.ARGON2_PARALLELISM,

    # bcrypt configuration (for backward compatibility)
    bcrypt__rounds=settings.BCRYPT_ROUNDS,

    # scrypt configuration (alternative option)
    scrypt__rounds=settings.SCRYPT_ROUNDS,
    scrypt__block_size=settings.SCRYPT_BLOCK_SIZE,
    scrypt__parallelism=settings.SCRYPT_PARALLELISM,
)


def create_access_token(data: dict, expires_delta: Optional[timedelta] = None) -> str:
    """Create JWT access token."""
    to_encode = data.copy()
    
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)
    
    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(to_encode, settings.SECRET_KEY, algorithm=settings.ALGORITHM)
    return encoded_jwt


def create_refresh_token(data: dict) -> str:
    """Create JWT refresh token."""
    to_encode = data.copy()
    expire = datetime.utcnow() + timedelta(days=settings.REFRESH_TOKEN_EXPIRE_DAYS)
    to_encode.update({"exp": expire, "type": "refresh"})
    encoded_jwt = jwt.encode(to_encode, settings.SECRET_KEY, algorithm=settings.ALGORITHM)
    return encoded_jwt


def verify_token(token: str) -> Optional[dict]:
    """Verify and decode JWT token."""
    try:
        payload = jwt.decode(token, settings.SECRET_KEY, algorithms=[settings.ALGORITHM])
        return payload
    except JWTError:
        return None


def get_token_id(token: str) -> str:
    """Generate a unique identifier for a token (for blacklisting)."""
    # Create a hash of the token for use as a unique identifier
    return hashlib.sha256(token.encode()).hexdigest()[:32]


async def blacklist_token(redis_client, token: str, expires_at: Optional[datetime] = None) -> bool:
    """
    Add a token to the blacklist.

    Args:
        redis_client: Redis client instance
        token: JWT token to blacklist
        expires_at: When the token expires (for TTL calculation)

    Returns:
        True if successfully blacklisted, False otherwise
    """
    try:
        token_id = get_token_id(token)
        blacklist_key = f"blacklist:token:{token_id}"

        # Calculate TTL based on token expiration
        if expires_at:
            ttl_seconds = int((expires_at - datetime.utcnow()).total_seconds())
            # Ensure TTL is positive
            ttl_seconds = max(ttl_seconds, 1)
        else:
            # Default TTL if expiration not provided
            ttl_seconds = settings.ACCESS_TOKEN_EXPIRE_MINUTES * 60

        # Store token in blacklist with TTL
        await redis_client.setex(blacklist_key, ttl_seconds, "blacklisted")
        return True

    except Exception as e:
        print(f"Error blacklisting token: {e}")
        return False


async def is_token_blacklisted(redis_client, token: str) -> bool:
    """
    Check if a token is blacklisted.

    Args:
        redis_client: Redis client instance
        token: JWT token to check

    Returns:
        True if token is blacklisted, False otherwise
    """
    try:
        token_id = get_token_id(token)
        blacklist_key = f"blacklist:token:{token_id}"

        result = await redis_client.get(blacklist_key)
        return result is not None

    except Exception as e:
        print(f"Error checking token blacklist: {e}")
        # If Redis is down, assume token is not blacklisted (fail open)
        return False


async def verify_token_not_blacklisted(redis_client, token: str) -> Optional[dict]:
    """
    Verify token and check if it's not blacklisted.

    Args:
        redis_client: Redis client instance
        token: JWT token to verify

    Returns:
        Token payload if valid and not blacklisted, None otherwise
    """
    # First verify the token signature and expiration
    payload = verify_token(token)
    if not payload:
        return None

    # Check if token is blacklisted
    if await is_token_blacklisted(redis_client, token):
        return None

    return payload





def get_password_hash(password: str, algorithm: Optional[str] = None) -> str:
    """
    Hash a password using the specified algorithm or the default preferred algorithm.

    Args:
        password: The plain text password to hash
        algorithm: Optional algorithm to use ("argon2", "bcrypt", "scrypt")
                  If None, uses the configured default algorithm

    Returns:
        The hashed password string
    """
    if algorithm:
        # Use specific algorithm if requested
        return pwd_context.hash(password, scheme=algorithm)
    else:
        # Use the preferred algorithm from configuration
        preferred_algorithm = settings.PASSWORD_HASH_ALGORITHM
        return pwd_context.hash(password, scheme=preferred_algorithm)


def verify_password(plain_password: str, hashed_password: str) -> bool:
    """
    Verify a password against its hash.

    Args:
        plain_password: The plain text password to verify
        hashed_password: The stored hash to verify against

    Returns:
        True if the password matches, False otherwise
    """
    return pwd_context.verify(plain_password, hashed_password)


def needs_password_rehash(hashed_password: str) -> bool:
    """
    Check if a password hash needs to be rehashed with a stronger algorithm.

    Args:
        hashed_password: The stored password hash

    Returns:
        True if the password should be rehashed, False otherwise
    """
    return pwd_context.needs_update(hashed_password)


def get_password_hash_info(hashed_password: str) -> dict:
    """
    Get information about a password hash (algorithm, parameters, etc.).

    Args:
        hashed_password: The password hash to analyze

    Returns:
        Dictionary with hash information
    """
    try:
        hash_info = pwd_context.identify(hashed_password)
        return {
            "algorithm": hash_info,
            "needs_update": pwd_context.needs_update(hashed_password),
            "is_deprecated": hash_info in pwd_context.deprecated_schemes if hasattr(pwd_context, 'deprecated_schemes') else False
        }
    except Exception as e:
        return {
            "algorithm": "unknown",
            "needs_update": True,
            "is_deprecated": True,
            "error": str(e)
        }


def verify_and_upgrade_password(plain_password: str, hashed_password: str) -> tuple[bool, Optional[str]]:
    """
    Verify a password and return a new hash if upgrade is needed.

    Args:
        plain_password: The plain text password to verify
        hashed_password: The stored hash to verify against

    Returns:
        Tuple of (is_valid, new_hash_if_upgrade_needed)
        - is_valid: True if password is correct
        - new_hash_if_upgrade_needed: New hash string if upgrade needed, None otherwise
    """
    # First verify the password
    is_valid = pwd_context.verify(plain_password, hashed_password)

    if not is_valid:
        return False, None

    # Check if hash needs upgrade
    if pwd_context.needs_update(hashed_password):
        # Generate new hash with current preferred algorithm
        new_hash = get_password_hash(plain_password)
        return True, new_hash

    return True, None


def generate_password_reset_token() -> str:
    """Generate a secure password reset token."""
    return secrets.token_urlsafe(32)


def generate_email_verification_token() -> str:
    """Generate a secure email verification token."""
    return secrets.token_urlsafe(32)


class TokenData:
    """Token data structure with role and permission information."""

    def __init__(
        self,
        user_id: str = None,
        email: str = None,
        roles: list = None,
        permissions: list = None,
        is_superuser: bool = False
    ):
        self.user_id = user_id
        self.email = email
        self.roles = roles or []
        self.permissions = permissions or []
        self.is_superuser = is_superuser

    def has_role(self, role_name: str) -> bool:
        """Check if user has a specific role."""
        return role_name in self.roles

    def has_permission(self, permission_name: str) -> bool:
        """Check if user has a specific permission."""
        # Superuser has all permissions
        if self.is_superuser:
            return True
        return permission_name in self.permissions

    def has_any_role(self, role_names: list) -> bool:
        """Check if user has any of the specified roles."""
        return any(role in self.roles for role in role_names)

    def has_any_permission(self, permission_names: list) -> bool:
        """Check if user has any of the specified permissions."""
        # Superuser has all permissions
        if self.is_superuser:
            return True
        return any(perm in self.permissions for perm in permission_names)


def extract_token_data(payload: dict) -> TokenData:
    """Extract TokenData from JWT payload."""
    return TokenData(
        user_id=payload.get("sub"),
        email=payload.get("email"),
        roles=payload.get("roles", []),
        permissions=payload.get("permissions", []),
        is_superuser=payload.get("is_superuser", False)
    )


def create_token_response(user_id: str, email: str, roles: list = None, permissions: list = None, is_superuser: bool = False) -> dict:
    """Create a complete token response with access and refresh tokens including role information."""
    access_token_expires = timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)

    # Ensure user_id is a string (convert UUID if necessary)
    user_id_str = str(user_id) if user_id else None

    # Prepare token data with role and permission information
    token_data = {
        "sub": user_id_str,
        "email": email,
        "is_superuser": is_superuser,
        "roles": roles or [],
        "permissions": permissions or []
    }

    access_token = create_access_token(
        data=token_data,
        expires_delta=access_token_expires
    )

    # Refresh token contains minimal data for security
    refresh_token = create_refresh_token(
        data={"sub": user_id_str, "email": email}
    )

    return {
        "access_token": access_token,
        "refresh_token": refresh_token,
        "token_type": "bearer",
        "expires_in": settings.ACCESS_TOKEN_EXPIRE_MINUTES * 60,  # in seconds
    }


def validate_password_strength(password: str) -> tuple[bool, list[str]]:
    """Validate password strength and return errors if any."""
    errors = []
    
    if len(password) < 8:
        errors.append("Password must be at least 8 characters long")
    
    if not any(c.isupper() for c in password):
        errors.append("Password must contain at least one uppercase letter")
    
    if not any(c.islower() for c in password):
        errors.append("Password must contain at least one lowercase letter")
    
    if not any(c.isdigit() for c in password):
        errors.append("Password must contain at least one digit")
    
    # Check for special characters
    special_chars = "!@#$%^&*()_+-=[]{}|;:,.<>?"
    if not any(c in special_chars for c in password):
        errors.append("Password must contain at least one special character")
    
    return len(errors) == 0, errors


def is_valid_email(email: str) -> bool:
    """Basic email validation."""
    import re
    pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
    return re.match(pattern, email) is not None

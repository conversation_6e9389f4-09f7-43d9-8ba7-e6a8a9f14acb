"""
Role database models and operations for RBAC system
"""

import uuid
from datetime import datetime
from typing import Optional, List, Dict, Any
from asyncpg import Connection


class Role:
    """Role model for RBAC system."""
    
    def __init__(
        self,
        id: str = None,
        name: str = None,
        display_name: str = None,
        description: str = None,
        is_system_role: bool = False,
        is_active: bool = True,
        metadata: Dict[str, Any] = None,
        created_at: datetime = None,
        updated_at: datetime = None
    ):
        self.id = id or str(uuid.uuid4())
        self.name = name
        self.display_name = display_name
        self.description = description
        self.is_system_role = is_system_role
        self.is_active = is_active
        self.metadata = metadata or {}
        self.created_at = created_at
        self.updated_at = updated_at
    
    @classmethod
    def from_record(cls, record) -> "Role":
        """Create Role instance from database record."""
        if not record:
            return None

        # Handle metadata field - it might be a string or dict
        metadata = record["metadata"] or {}
        if isinstance(metadata, str):
            import json
            try:
                metadata = json.loads(metadata)
            except (json.JSONDecodeError, TypeError):
                metadata = {}

        return cls(
            id=str(record["id"]),
            name=record["name"],
            display_name=record["display_name"],
            description=record["description"],
            is_system_role=record["is_system_role"],
            is_active=record["is_active"],
            metadata=metadata,
            created_at=record["created_at"],
            updated_at=record["updated_at"]
        )
    
    def to_dict(self) -> dict:
        """Convert role to dictionary."""
        return {
            "id": self.id,
            "name": self.name,
            "display_name": self.display_name,
            "description": self.description,
            "is_system_role": self.is_system_role,
            "is_active": self.is_active,
            "metadata": self.metadata,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None,
        }


class RoleRepository:
    """Role database operations."""
    
    @staticmethod
    async def create_role(
        conn: Connection,
        name: str,
        display_name: str,
        description: str = None,
        is_system_role: bool = False,
        metadata: Dict[str, Any] = None
    ) -> Role:
        """Create a new role."""
        role_id = str(uuid.uuid4())
        
        query = """
            INSERT INTO roles (id, name, display_name, description, is_system_role, metadata)
            VALUES ($1, $2, $3, $4, $5, $6)
            RETURNING *
        """
        
        record = await conn.fetchrow(
            query, 
            role_id, 
            name, 
            display_name, 
            description, 
            is_system_role, 
            metadata or {}
        )
        return Role.from_record(record)
    
    @staticmethod
    async def get_role_by_id(conn: Connection, role_id: str) -> Optional[Role]:
        """Get role by ID."""
        query = "SELECT * FROM roles WHERE id = $1"
        record = await conn.fetchrow(query, role_id)
        return Role.from_record(record)
    
    @staticmethod
    async def get_role_by_name(conn: Connection, name: str) -> Optional[Role]:
        """Get role by name."""
        query = "SELECT * FROM roles WHERE name = $1"
        record = await conn.fetchrow(query, name)
        return Role.from_record(record)
    
    @staticmethod
    async def update_role(
        conn: Connection,
        role_id: str,
        display_name: str = None,
        description: str = None,
        is_active: bool = None,
        metadata: Dict[str, Any] = None
    ) -> Optional[Role]:
        """Update role information."""
        # Build dynamic update query
        updates = []
        params = []
        param_count = 1
        
        if display_name is not None:
            updates.append(f"display_name = ${param_count}")
            params.append(display_name)
            param_count += 1
        
        if description is not None:
            updates.append(f"description = ${param_count}")
            params.append(description)
            param_count += 1
        
        if is_active is not None:
            updates.append(f"is_active = ${param_count}")
            params.append(is_active)
            param_count += 1
        
        if metadata is not None:
            updates.append(f"metadata = ${param_count}")
            params.append(metadata)
            param_count += 1
        
        if not updates:
            return await RoleRepository.get_role_by_id(conn, role_id)
        
        updates.append(f"updated_at = NOW()")
        params.append(role_id)
        
        query = f"""
            UPDATE roles 
            SET {', '.join(updates)}
            WHERE id = ${param_count}
            RETURNING *
        """
        
        record = await conn.fetchrow(query, *params)
        return Role.from_record(record)
    
    @staticmethod
    async def delete_role(conn: Connection, role_id: str) -> bool:
        """Delete role (soft delete by setting is_active to False)."""
        # Prevent deletion of system roles
        check_query = "SELECT is_system_role FROM roles WHERE id = $1"
        is_system = await conn.fetchval(check_query, role_id)
        
        if is_system:
            return False  # Cannot delete system roles
        
        query = """
            UPDATE roles 
            SET is_active = FALSE, updated_at = NOW()
            WHERE id = $1 AND is_system_role = FALSE
        """
        
        result = await conn.execute(query, role_id)
        return "UPDATE 1" in result
    
    @staticmethod
    async def list_roles(
        conn: Connection, 
        active_only: bool = True,
        limit: int = 100,
        offset: int = 0
    ) -> List[Role]:
        """List roles with pagination."""
        where_clause = "WHERE is_active = TRUE" if active_only else ""
        
        query = f"""
            SELECT * FROM roles 
            {where_clause}
            ORDER BY name ASC
            LIMIT $1 OFFSET $2
        """
        
        records = await conn.fetch(query, limit, offset)
        return [Role.from_record(record) for record in records]
    
    @staticmethod
    async def count_roles(conn: Connection, active_only: bool = True) -> int:
        """Count total roles."""
        where_clause = "WHERE is_active = TRUE" if active_only else ""
        query = f"SELECT COUNT(*) FROM roles {where_clause}"
        return await conn.fetchval(query)
    
    @staticmethod
    async def role_exists(conn: Connection, name: str) -> bool:
        """Check if role name already exists."""
        query = "SELECT EXISTS(SELECT 1 FROM roles WHERE name = $1)"
        return await conn.fetchval(query, name)
    
    @staticmethod
    async def get_role_permissions(conn: Connection, role_id: str) -> List[str]:
        """Get list of permission names for a role."""
        query = """
            SELECT p.name
            FROM permissions p
            JOIN role_permissions rp ON p.id = rp.permission_id
            WHERE rp.role_id = $1 AND rp.is_active = TRUE
            ORDER BY p.name
        """
        records = await conn.fetch(query, role_id)
        return [record['name'] for record in records]
    
    @staticmethod
    async def get_role_users_count(conn: Connection, role_id: str) -> int:
        """Get count of users assigned to this role."""
        query = """
            SELECT COUNT(*)
            FROM user_roles
            WHERE role_id = $1 AND is_active = TRUE
        """
        return await conn.fetchval(query, role_id)

    @staticmethod
    async def get_roles_paginated(
        conn: Connection,
        page: int = 1,
        page_size: int = 20,
        include_inactive: bool = False,
        search: str = None
    ) -> List[Role]:
        """Get roles with pagination and search."""
        offset = (page - 1) * page_size

        # Build WHERE clause
        where_conditions = []
        params = []
        param_count = 1

        if not include_inactive:
            where_conditions.append("is_active = TRUE")

        if search:
            where_conditions.append(f"(name ILIKE ${param_count} OR display_name ILIKE ${param_count} OR description ILIKE ${param_count})")
            params.append(f"%{search}%")
            param_count += 1

        where_clause = "WHERE " + " AND ".join(where_conditions) if where_conditions else ""

        # Add pagination parameters
        params.extend([page_size, offset])

        query = f"""
            SELECT * FROM roles
            {where_clause}
            ORDER BY name ASC
            LIMIT ${param_count} OFFSET ${param_count + 1}
        """

        records = await conn.fetch(query, *params)
        return [Role.from_record(record) for record in records]

    @staticmethod
    async def get_roles_count(
        conn: Connection,
        include_inactive: bool = False,
        search: str = None
    ) -> int:
        """Get total count of roles with filters."""
        # Build WHERE clause
        where_conditions = []
        params = []
        param_count = 1

        if not include_inactive:
            where_conditions.append("is_active = TRUE")

        if search:
            where_conditions.append(f"(name ILIKE ${param_count} OR display_name ILIKE ${param_count} OR description ILIKE ${param_count})")
            params.append(f"%{search}%")
            param_count += 1

        where_clause = "WHERE " + " AND ".join(where_conditions) if where_conditions else ""

        query = f"SELECT COUNT(*) FROM roles {where_clause}"
        return await conn.fetchval(query, *params)

    @staticmethod
    async def get_roles_summary(conn: Connection) -> dict:
        """Get role summary statistics."""
        query = """
            SELECT
                COUNT(*) as total_roles,
                COUNT(*) FILTER (WHERE is_system_role = TRUE) as system_roles,
                COUNT(*) FILTER (WHERE is_system_role = FALSE) as custom_roles,
                COUNT(*) FILTER (WHERE is_active = TRUE) as active_roles,
                COUNT(*) FILTER (WHERE is_active = FALSE) as inactive_roles
            FROM roles
        """
        record = await conn.fetchrow(query)
        return {
            "total_roles": record["total_roles"],
            "system_roles": record["system_roles"],
            "custom_roles": record["custom_roles"],
            "active_roles": record["active_roles"],
            "inactive_roles": record["inactive_roles"]
        }

    @staticmethod
    async def get_role_statistics(conn: Connection) -> dict:
        """Get role statistics for admin dashboard."""
        stats_query = """
            SELECT
                COUNT(*) as total_roles,
                COUNT(*) FILTER (WHERE is_active = TRUE) as active_roles,
                COUNT(*) FILTER (WHERE is_active = FALSE) as inactive_roles,
                COUNT(*) FILTER (WHERE is_system_role = TRUE) as system_roles,
                COUNT(*) FILTER (WHERE is_system_role = FALSE) as custom_roles,
                COUNT(*) FILTER (WHERE created_at >= NOW() - INTERVAL '30 days') as new_roles_last_30_days
            FROM roles
        """

        result = await conn.fetchrow(stats_query)
        return dict(result) if result else {}


class RolePermissionAssignment:
    """Role-Permission assignment model."""

    def __init__(
        self,
        id: str,
        role_id: str,
        permission_id: str,
        granted_by: str = None,
        granted_at: datetime = None,
        is_active: bool = True,
        notes: str = None,
        created_at: datetime = None,
        updated_at: datetime = None
    ):
        self.id = id
        self.role_id = role_id
        self.permission_id = permission_id
        self.granted_by = granted_by
        self.granted_at = granted_at
        self.is_active = is_active
        self.notes = notes
        self.created_at = created_at
        self.updated_at = updated_at

    @classmethod
    def from_record(cls, record) -> "RolePermissionAssignment":
        """Create RolePermissionAssignment instance from database record."""
        if not record:
            return None

        return cls(
            id=str(record["id"]),
            role_id=str(record["role_id"]),
            permission_id=str(record["permission_id"]),
            granted_by=str(record["granted_by"]) if record["granted_by"] else None,
            granted_at=record["granted_at"],
            is_active=record["is_active"],
            notes=record.get("notes"),
            created_at=record.get("created_at"),
            updated_at=record.get("updated_at")
        )

    def to_dict(self) -> dict:
        """Convert assignment to dictionary."""
        return {
            "id": self.id,
            "role_id": self.role_id,
            "permission_id": self.permission_id,
            "granted_by": self.granted_by,
            "granted_at": self.granted_at.isoformat() if self.granted_at else None,
            "is_active": self.is_active,
            "notes": self.notes,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None
        }

    @staticmethod
    async def get_by_id(conn: Connection, assignment_id: str) -> "RolePermissionAssignment":
        """Get role-permission assignment by ID."""
        query = "SELECT * FROM role_permissions WHERE id = $1"
        record = await conn.fetchrow(query, assignment_id)
        return RolePermissionAssignment.from_record(record)

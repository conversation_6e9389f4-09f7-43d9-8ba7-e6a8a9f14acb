import { useState } from "react";
import { useNavigate } from "react-router-dom";
import { useNetworkStatus } from "../hooks/useNetworkStatus";
import {
  Plus,
  ShoppingCart,
  Calendar,
  Package,
  X,
  Edit2,
  Shield,
  CheckCircle,
} from "lucide-react";
import {
  useShoppingLists,
  useCreateShoppingList,
  useUpdateShoppingList,
} from "../hooks/useShoppingLists";
import { useShoppingListPermissions } from "../hooks/useRoles";
import { useAuthStore } from "../stores/authStore";
import { formatDate } from "../lib/utils";

const ShoppingListsPage = () => {
  const navigate = useNavigate();
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [editingList, setEditingList] = useState<number | null>(null);
  const [formData, setFormData] = useState({
    name: "",
    description: "",
    is_default: false,
  });

  // Get user and permission information
  const { user } = useAuthStore();
  const shoppingListPermissions = useShoppingListPermissions();

  const { data: listsData, isLoading, error } = useShoppingLists();
  const createMutation = useCreateShoppingList();
  const updateMutation = useUpdateShoppingList();
  const networkStatus = useNetworkStatus();

  const lists = listsData?.data?.items || [];

  const handleInputChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    const { name, value, type } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]:
        type === "checkbox" ? (e.target as HTMLInputElement).checked : value,
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!formData.name.trim()) return;

    try {
      await createMutation.mutateAsync(formData);
      setShowCreateModal(false);
      setFormData({ name: "", description: "", is_default: false });
    } catch (error) {
      // Error handling is done in the mutation
    }
  };

  const handleCloseModal = () => {
    setShowCreateModal(false);
    setEditingList(null);
    setFormData({ name: "", description: "", is_default: false });
  };

  const handleEditList = (list: any) => {
    setEditingList(list.id);
    setFormData({
      name: list.name,
      description: list.description || "",
      is_default: list.is_default || false,
    });
  };

  const handleUpdateSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!formData.name.trim() || !editingList) return;

    try {
      await updateMutation.mutateAsync({
        id: editingList,
        data: {
          name: formData.name,
          description: formData.description,
          is_default: formData.is_default,
        },
      });
      handleCloseModal();
    } catch (error) {
      // Error handling is done in the mutation
    }
  };

  if (isLoading) {
    return (
      <div className="max-w-6xl mx-auto">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-1/4 mb-6"></div>
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
            {[...Array(6)].map((_, i) => (
              <div
                key={i}
                className="bg-white rounded-lg border border-gray-200 p-6"
              >
                <div className="h-6 bg-gray-200 rounded w-3/4 mb-4"></div>
                <div className="h-4 bg-gray-200 rounded w-1/2 mb-2"></div>
                <div className="h-4 bg-gray-200 rounded w-2/3"></div>
              </div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-6xl mx-auto">
      {/* Header */}
      <div className="flex items-center justify-between mb-8">
        <div>
          <div className="flex items-center gap-3 mb-2">
            <h1 className="text-3xl font-bold text-gray-900">Shopping Lists</h1>
            {user && (
              <div className="flex items-center gap-1 px-2 py-1 bg-green-50 text-green-700 rounded-full text-xs">
                <CheckCircle className="w-3 h-3" />
                <span>Authorized</span>
              </div>
            )}
          </div>
          <p className="text-gray-600">
            Manage your dental product shopping lists and compare prices across
            suppliers.
            {user && !shoppingListPermissions.canManage && (
              <span className="block text-amber-600 text-sm mt-1">
                Limited access - contact admin for full shopping list management
                permissions
              </span>
            )}
          </p>
        </div>

        {shoppingListPermissions.canCreate ? (
          <button
            onClick={() => setShowCreateModal(true)}
            className="inline-flex items-center px-4 py-2 bg-primary-600 text-white rounded-md hover:bg-primary-700 transition-colors"
          >
            <Plus className="h-4 w-4 mr-2" />
            New List
          </button>
        ) : (
          <div className="text-sm text-gray-500 text-center">
            <Shield className="w-4 h-4 mx-auto mb-1" />
            <span>Contact admin to create lists</span>
          </div>
        )}
      </div>

      {/* Error State */}
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-md p-4 mb-6">
          <div className="text-red-800">
            <strong>Error:</strong> Failed to load shopping lists
          </div>
        </div>
      )}

      {/* Empty State */}
      {lists.length === 0 && !isLoading && (
        <div className="text-center py-12">
          <ShoppingCart className="mx-auto h-12 w-12 text-gray-400 mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            No shopping lists yet
          </h3>
          <p className="text-gray-600 mb-6">
            Create your first shopping list to start organizing your dental
            product purchases.
          </p>
          <button
            onClick={() => setShowCreateModal(true)}
            className="inline-flex items-center px-4 py-2 bg-primary-600 text-white rounded-md hover:bg-primary-700 transition-colors"
          >
            <Plus className="h-4 w-4 mr-2" />
            Create Your First List
          </button>
        </div>
      )}

      {/* Shopping Lists Grid */}
      {lists.length > 0 && (
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
          {lists.map((list) => (
            <div
              key={list.id}
              className="bg-white rounded-lg border border-gray-200 hover:border-gray-300 hover:shadow-md transition-all duration-200 cursor-pointer"
            >
              <div className="p-6">
                <div className="flex items-start justify-between mb-4">
                  <h3 className="text-lg font-semibold text-gray-900 line-clamp-2">
                    {list.name}
                  </h3>
                  <div className="flex-shrink-0 ml-2">
                    <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-primary-100 text-primary-800">
                      {list.summary?.total_items || 0} items
                    </span>
                  </div>
                </div>

                {list.description && (
                  <p className="text-gray-600 text-sm mb-4 line-clamp-2">
                    {list.description}
                  </p>
                )}

                <div className="flex items-center justify-between text-sm text-gray-500">
                  <div className="flex items-center">
                    <Calendar className="h-4 w-4 mr-1" />
                    {formatDate(list.created_at)}
                  </div>

                  {list.summary && (
                    <div className="flex items-center">
                      <Package className="h-4 w-4 mr-1" />
                      {list.summary.total_quantity} qty
                    </div>
                  )}
                </div>

                {/* Progress bar if summary available */}
                {list.summary && list.summary.total_items > 0 && (
                  <div className="mt-4">
                    <div className="flex items-center justify-between text-xs text-gray-600 mb-1">
                      <span>Progress</span>
                      <span>
                        {list.summary.completion_percentage.toFixed(0)}%
                      </span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div
                        className="bg-primary-600 h-2 rounded-full transition-all duration-300"
                        style={{
                          width: `${list.summary.completion_percentage}%`,
                        }}
                      />
                    </div>
                  </div>
                )}
              </div>

              <div className="px-6 py-3 bg-gray-50 border-t border-gray-200 rounded-b-lg">
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">
                    Last updated {formatDate(list.updated_at)}
                  </span>
                  <div className="flex items-center space-x-3">
                    <button
                      onClick={() => handleEditList(list)}
                      className="text-sm text-gray-600 hover:text-primary-600 font-medium flex items-center"
                    >
                      <Edit2 className="h-3 w-3 mr-1" />
                      Edit
                    </button>
                    <button
                      onClick={() => navigate(`/shopping-lists/${list.id}`)}
                      className="text-sm text-primary-600 hover:text-primary-700 font-medium"
                    >
                      View Details →
                    </button>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}

      {/* Create/Edit Shopping List Modal */}
      {(showCreateModal || editingList) && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-md">
            <div className="flex items-center justify-between mb-4">
              <h2 className="text-lg font-semibold text-gray-900">
                {editingList
                  ? "Edit Shopping List"
                  : "Create New Shopping List"}
              </h2>
              <button
                onClick={handleCloseModal}
                className="text-gray-400 hover:text-gray-600"
              >
                <X className="h-5 w-5" />
              </button>
            </div>

            <form onSubmit={editingList ? handleUpdateSubmit : handleSubmit}>
              <div className="space-y-4">
                <div>
                  <label
                    htmlFor="name"
                    className="block text-sm font-medium text-gray-700 mb-1"
                  >
                    List Name *
                  </label>
                  <input
                    type="text"
                    id="name"
                    name="name"
                    value={formData.name}
                    onChange={handleInputChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                    placeholder="e.g., Office Supplies, Emergency Kit"
                    required
                  />
                </div>

                <div>
                  <label
                    htmlFor="description"
                    className="block text-sm font-medium text-gray-700 mb-1"
                  >
                    Description (Optional)
                  </label>
                  <textarea
                    id="description"
                    name="description"
                    value={formData.description}
                    onChange={handleInputChange}
                    rows={3}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                    placeholder="Add a description for this shopping list..."
                  />
                </div>

                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id="is_default"
                    name="is_default"
                    checked={formData.is_default}
                    onChange={handleInputChange}
                    className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                  />
                  <label
                    htmlFor="is_default"
                    className="ml-2 block text-sm text-gray-700"
                  >
                    Make this my default shopping list
                  </label>
                </div>
              </div>

              <div className="flex justify-end space-x-3 mt-6">
                <button
                  type="button"
                  onClick={handleCloseModal}
                  className="px-4 py-2 text-gray-700 border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-gray-500"
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  disabled={
                    !formData.name.trim() ||
                    createMutation.isPending ||
                    updateMutation.isPending
                  }
                  className="px-4 py-2 bg-primary-600 text-white rounded-md hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-primary-500 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {editingList
                    ? updateMutation.isPending
                      ? "Updating..."
                      : "Update List"
                    : createMutation.isPending
                    ? "Creating..."
                    : "Create List"}
                </button>
              </div>
            </form>
          </div>
        </div>
      )}
    </div>
  );
};

export default ShoppingListsPage;

#!/usr/bin/env python3
"""
Comprehensive Frontend Search Debug Test
Uses <PERSON>wright to test the actual user experience and debug search issues
"""

import asyncio
import sys
import time
import json
from pathlib import Path
from playwright.async_api import async_playwright


class FrontendSearchDebugger:
    """Debug frontend search functionality using <PERSON><PERSON>."""
    
    def __init__(self):
        self.frontend_url = "http://localhost:3000"
        self.api_url = "http://localhost:8000"
        self.screenshots_dir = Path("debug_screenshots")
        self.screenshots_dir.mkdir(exist_ok=True)
        
    async def debug_frontend_search(self):
        """Comprehensive frontend search debugging."""
        print("🔍 Starting Frontend Search Debug Test")
        print("=" * 70)
        
        async with async_playwright() as p:
            # Launch browser with debugging options
            browser = await p.chromium.launch(
                headless=False,  # Keep visible for debugging
                slow_mo=1000,    # Slow down actions for visibility
                args=['--disable-web-security', '--disable-features=VizDisplayCompositor']
            )
            
            context = await browser.new_context(
                viewport={'width': 1280, 'height': 720},
                record_video_dir="debug_videos"
            )
            
            page = await context.new_page()
            
            # Enable console logging
            page.on("console", lambda msg: print(f"🖥️ CONSOLE: {msg.type}: {msg.text}"))
            page.on("pageerror", lambda error: print(f"❌ PAGE ERROR: {error}"))
            
            # Track network requests
            network_requests = []
            page.on("request", lambda request: network_requests.append({
                "url": request.url,
                "method": request.method,
                "headers": dict(request.headers)
            }))
            
            page.on("response", lambda response: print(f"🌐 RESPONSE: {response.status} {response.url}"))
            
            try:
                # Step 1: Navigate to frontend
                print("\n📋 Step 1: Loading Frontend Application")
                await page.goto(self.frontend_url, wait_until="networkidle")
                await page.wait_for_timeout(2000)
                
                # Take initial screenshot
                await page.screenshot(path=self.screenshots_dir / "01_initial_load.png")
                print(f"  ✅ Frontend loaded: {page.url}")
                
                # Check for any immediate errors
                title = await page.title()
                print(f"  📄 Page title: {title}")
                
                # Step 2: Check if authentication is required
                print("\n📋 Step 2: Checking Authentication")
                
                # Look for login/register elements
                login_button = page.locator("text=Login, text=Sign In, button:has-text('Login')").first
                register_button = page.locator("text=Register, text=Sign Up, button:has-text('Register')").first
                
                if await login_button.is_visible() or await register_button.is_visible():
                    print("  🔐 Authentication required, attempting login...")
                    
                    # Try to find and click login
                    if await login_button.is_visible():
                        await login_button.click()
                        await page.wait_for_load_state("networkidle")
                        await page.screenshot(path=self.screenshots_dir / "02_login_page.png")
                        
                        # Fill login form
                        email_input = page.locator('input[type="email"], input[name="email"], input[placeholder*="email" i]').first
                        password_input = page.locator('input[type="password"], input[name="password"]').first
                        
                        if await email_input.is_visible() and await password_input.is_visible():
                            await email_input.fill("<EMAIL>")
                            await password_input.fill("password123")
                            
                            # Submit form
                            submit_button = page.locator('button[type="submit"], button:has-text("Login"), button:has-text("Sign In")').first
                            if await submit_button.is_visible():
                                await submit_button.click()
                                await page.wait_for_load_state("networkidle")
                                await page.wait_for_timeout(2000)
                                print("  ✅ Login attempted")
                            else:
                                await page.press('input[type="password"]', 'Enter')
                                await page.wait_for_load_state("networkidle")
                                print("  ✅ Login submitted via Enter key")
                        else:
                            print("  ❌ Could not find login form fields")
                    else:
                        print("  ℹ️ No login button found, trying to proceed")
                else:
                    print("  ✅ No authentication required")
                
                await page.screenshot(path=self.screenshots_dir / "03_after_auth.png")
                
                # Step 3: Find search input field
                print("\n📋 Step 3: Locating Search Input Field")
                
                # Try multiple selectors for search input
                search_selectors = [
                    'input[type="search"]',
                    'input[placeholder*="search" i]',
                    'input[placeholder*="Search" i]',
                    'input[name="search"]',
                    'input[name="query"]',
                    'input[name="q"]',
                    '.search-input',
                    '#search',
                    '#search-input',
                    '[data-testid="search-input"]',
                    'input[aria-label*="search" i]'
                ]
                
                search_input = None
                for selector in search_selectors:
                    try:
                        element = page.locator(selector).first
                        if await element.is_visible():
                            search_input = element
                            print(f"  ✅ Found search input: {selector}")
                            break
                    except:
                        continue
                
                if not search_input:
                    print("  ❌ Could not find search input field")
                    # Take screenshot of current page
                    await page.screenshot(path=self.screenshots_dir / "04_no_search_input.png")
                    
                    # Try to find any input fields
                    all_inputs = await page.locator('input').all()
                    print(f"  📋 Found {len(all_inputs)} input fields:")
                    for i, input_elem in enumerate(all_inputs):
                        try:
                            placeholder = await input_elem.get_attribute('placeholder')
                            input_type = await input_elem.get_attribute('type')
                            name = await input_elem.get_attribute('name')
                            print(f"    Input {i+1}: type='{input_type}', name='{name}', placeholder='{placeholder}'")
                        except:
                            pass
                    
                    return False
                
                # Step 4: Test search suggestions
                print("\n📋 Step 4: Testing Search Suggestions")
                
                # Clear and focus on search input
                await search_input.click()
                await search_input.clear()
                await page.wait_for_timeout(500)
                
                # Type search query slowly to trigger suggestions
                test_query = "comp"
                print(f"  🔤 Typing '{test_query}' to trigger suggestions...")
                
                for char in test_query:
                    await search_input.type(char)
                    await page.wait_for_timeout(300)  # Wait for suggestions
                
                await page.wait_for_timeout(2000)  # Wait for suggestions to appear
                await page.screenshot(path=self.screenshots_dir / "05_search_suggestions.png")
                
                # Look for suggestion dropdown/list
                suggestion_selectors = [
                    '.suggestions',
                    '.autocomplete',
                    '.dropdown',
                    '.search-suggestions',
                    '.suggestion-list',
                    '[data-testid="suggestions"]',
                    'ul[role="listbox"]',
                    '.search-dropdown'
                ]
                
                suggestions_found = False
                for selector in suggestion_selectors:
                    try:
                        suggestions_element = page.locator(selector).first
                        if await suggestions_element.is_visible():
                            suggestions_count = await page.locator(f"{selector} li, {selector} .suggestion-item").count()
                            print(f"  ✅ Found suggestions dropdown: {selector} with {suggestions_count} items")
                            suggestions_found = True
                            break
                    except:
                        continue
                
                if not suggestions_found:
                    print("  ❌ No search suggestions dropdown found")
                    # Check if there are any elements that appeared after typing
                    await page.wait_for_timeout(1000)
                    await page.screenshot(path=self.screenshots_dir / "06_no_suggestions.png")
                
                # Step 5: Submit search and check results
                print("\n📋 Step 5: Submitting Search Query")
                
                # Clear and type full search query
                await search_input.clear()
                full_query = "composite"
                await search_input.fill(full_query)
                print(f"  🔤 Typed search query: '{full_query}'")
                
                # Try to submit search
                search_button = page.locator('button[type="submit"], button:has-text("Search"), .search-button, [data-testid="search-button"]').first
                
                if await search_button.is_visible():
                    print("  🔘 Clicking search button...")
                    await search_button.click()
                else:
                    print("  ⌨️ Pressing Enter key...")
                    await search_input.press('Enter')
                
                # Wait for search results
                await page.wait_for_load_state("networkidle")
                await page.wait_for_timeout(3000)
                await page.screenshot(path=self.screenshots_dir / "07_search_results.png")
                
                # Step 6: Check for search results
                print("\n📋 Step 6: Checking Search Results")
                
                # Look for search results
                result_selectors = [
                    '.search-results',
                    '.product-list',
                    '.results',
                    '.product-item',
                    '.search-result',
                    '[data-testid="search-results"]',
                    '[data-testid="product-list"]',
                    '.product-card'
                ]
                
                results_found = False
                for selector in result_selectors:
                    try:
                        results_container = page.locator(selector).first
                        if await results_container.is_visible():
                            results_count = await page.locator(f"{selector}").count()
                            print(f"  ✅ Found search results: {selector} with {results_count} items")
                            results_found = True
                            break
                    except:
                        continue
                
                if not results_found:
                    print("  ❌ No search results found in UI")
                    
                    # Check for "no results" message
                    no_results_text = await page.locator("text=No results, text=No products found, text=0 results").is_visible()
                    if no_results_text:
                        print("  ℹ️ Found 'no results' message")
                    else:
                        print("  ❌ No results container or message found")
                
                # Step 7: Analyze network requests
                print("\n📋 Step 7: Analyzing Network Requests")
                
                api_requests = [req for req in network_requests if self.api_url in req['url']]
                search_requests = [req for req in api_requests if 'search' in req['url']]
                
                print(f"  🌐 Total network requests: {len(network_requests)}")
                print(f"  🔗 API requests to {self.api_url}: {len(api_requests)}")
                print(f"  🔍 Search API requests: {len(search_requests)}")
                
                if search_requests:
                    print("  ✅ Search API requests found:")
                    for req in search_requests[-3:]:  # Show last 3
                        print(f"    {req['method']} {req['url']}")
                else:
                    print("  ❌ No search API requests found!")
                    print("  📋 Recent API requests:")
                    for req in api_requests[-5:]:  # Show last 5 API requests
                        print(f"    {req['method']} {req['url']}")
                
                # Step 8: Check browser console for errors
                print("\n📋 Step 8: Browser Console Analysis")
                
                # Get console messages
                console_errors = []
                console_logs = []
                
                # We already captured console messages via the event listener
                print("  📝 Console messages captured during test execution")
                
                # Step 9: Inspect page source for debugging
                print("\n📋 Step 9: Page Source Inspection")
                
                # Check if React app is loaded
                react_root = await page.locator('#root, #app, [data-reactroot]').count()
                print(f"  ⚛️ React root elements found: {react_root}")
                
                # Check for any error boundaries or error messages
                error_messages = await page.locator('.error, .error-message, [data-testid="error"]').count()
                print(f"  ❌ Error message elements: {error_messages}")
                
                # Final screenshot
                await page.screenshot(path=self.screenshots_dir / "08_final_state.png")
                
                print("\n" + "=" * 70)
                print("🔍 FRONTEND SEARCH DEBUG SUMMARY")
                print("=" * 70)
                
                if suggestions_found and results_found:
                    print("✅ Search functionality appears to be working")
                elif suggestions_found:
                    print("⚠️ Suggestions working, but results not displaying")
                elif results_found:
                    print("⚠️ Results found, but suggestions not working")
                else:
                    print("❌ Search functionality not working properly")
                
                print(f"📸 Screenshots saved to: {self.screenshots_dir}")
                print(f"🎥 Video recording saved to: debug_videos/")
                
                return suggestions_found and results_found
                
            except Exception as e:
                print(f"❌ Debug test failed: {e}")
                await page.screenshot(path=self.screenshots_dir / "error_state.png")
                import traceback
                traceback.print_exc()
                return False
                
            finally:
                await browser.close()


async def main():
    """Run frontend search debugging."""
    debugger = FrontendSearchDebugger()
    success = await debugger.debug_frontend_search()
    
    if success:
        print("\n✅ Frontend search debugging completed successfully")
    else:
        print("\n❌ Frontend search issues identified - check screenshots and logs")
    
    return success


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)

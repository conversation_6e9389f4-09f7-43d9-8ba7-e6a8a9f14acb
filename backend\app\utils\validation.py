"""
Simple input validation utilities for authentication.
"""

import re
from typing import <PERSON><PERSON>, List, Dict, Any


class InputValidator:
    """Simple input validator for authentication."""

    @classmethod
    def validate_password(cls, password: str) -> Tuple[bool, List[str]]:
        """
        Validate password strength.

        Args:
            password: Password to validate

        Returns:
            Tuple of (is_valid, list_of_errors)
        """
        errors = []

        if not password:
            errors.append("Password is required")
            return False, errors

        if len(password) < 8:
            errors.append("Password must be at least 8 characters long")

        if len(password) > 128:
            errors.append("Password must not exceed 128 characters")

        if not re.search(r'[A-Z]', password):
            errors.append("Password must contain at least one uppercase letter")

        if not re.search(r'[a-z]', password):
            errors.append("Password must contain at least one lowercase letter")

        if not re.search(r'\d', password):
            errors.append("Password must contain at least one digit")

        if not re.search(r'[!@#$%^&*()_+\-=\[\]{}|;:,.<>?~`]', password):
            errors.append("Password must contain at least one special character")

        # Check for common weak passwords
        weak_passwords = {
            "password", "123456", "qwerty", "abc123", "password123",
            "admin", "letmein", "welcome", "monkey", "dragon",
            "123456789", "football", "iloveyou", "admin123", "welcome123"
        }
        if password.lower() in weak_passwords:
            errors.append("Password is too common")

        # Check for sequential characters (only fail if password is otherwise weak)
        has_sequential = cls._has_sequential_chars(password)
        has_repeated = cls._has_repeated_chars(password)
        has_keyboard_patterns = cls._has_keyboard_patterns(password)

        # Only enforce pattern restrictions if password doesn't meet other strong criteria
        strong_criteria_met = (
            len(password) >= 8 and
            any(c.isupper() for c in password) and
            any(c.islower() for c in password) and
            any(c.isdigit() for c in password) and
            any(c in "!@#$%^&*()_+-=[]{}|;:,.<>?" for c in password)
        )

        if not strong_criteria_met:
            if has_sequential:
                errors.append("Password should not contain sequential characters (e.g., 123, abc)")
            if has_repeated:
                errors.append("Password should not contain too many repeated characters")
            if has_keyboard_patterns:
                errors.append("Password should not contain keyboard patterns (e.g., qwerty, asdf)")

        return len(errors) == 0, errors

    @classmethod
    def _has_sequential_chars(cls, password: str) -> bool:
        """Check if password contains sequential characters."""
        password_lower = password.lower()

        # Check for sequential numbers
        for i in range(len(password_lower) - 2):
            if password_lower[i:i+3].isdigit():
                nums = [int(c) for c in password_lower[i:i+3]]
                if nums[1] == nums[0] + 1 and nums[2] == nums[1] + 1:
                    return True

        # Check for sequential letters
        for i in range(len(password_lower) - 2):
            if password_lower[i:i+3].isalpha():
                chars = password_lower[i:i+3]
                if ord(chars[1]) == ord(chars[0]) + 1 and ord(chars[2]) == ord(chars[1]) + 1:
                    return True

        return False

    @classmethod
    def _has_repeated_chars(cls, password: str) -> bool:
        """Check if password has too many repeated characters."""
        char_count = {}
        for char in password.lower():
            char_count[char] = char_count.get(char, 0) + 1

        # Check if any character appears more than 3 times
        for count in char_count.values():
            if count > 3:
                return True

        # Check for consecutive repeated characters (e.g., aaa, 111)
        for i in range(len(password) - 2):
            if password[i] == password[i+1] == password[i+2]:
                return True

        return False

    @classmethod
    def _has_keyboard_patterns(cls, password: str) -> bool:
        """Check if password contains keyboard patterns."""
        password_lower = password.lower()

        keyboard_patterns = [
            "qwerty", "asdf", "zxcv", "qwertyuiop", "asdfghjkl", "zxcvbnm",
            "1234567890", "0987654321", "qazwsx", "wsxedc", "edcrfv"
        ]

        for pattern in keyboard_patterns:
            if pattern in password_lower or pattern[::-1] in password_lower:
                return True

        return False

    @classmethod
    def get_password_strength_score(cls, password: str) -> Dict[str, Any]:
        """
        Calculate password strength score and provide detailed feedback.

        Args:
            password: Password to analyze

        Returns:
            Dictionary with strength score, level, and detailed feedback
        """
        if not password:
            return {
                "score": 0,
                "level": "Very Weak",
                "feedback": ["Password is required"],
                "requirements_met": {}
            }

        score = 0
        feedback = []
        requirements_met = {}

        # Length scoring
        if len(password) >= 8:
            score += 20
            requirements_met["min_length"] = True
        else:
            feedback.append(f"Password should be at least 8 characters (current: {len(password)})")
            requirements_met["min_length"] = False

        if len(password) >= 12:
            score += 10
            requirements_met["good_length"] = True
        else:
            requirements_met["good_length"] = False

        # Character variety scoring
        if re.search(r'[A-Z]', password):
            score += 15
            requirements_met["uppercase"] = True
        else:
            feedback.append("Add uppercase letters")
            requirements_met["uppercase"] = False

        if re.search(r'[a-z]', password):
            score += 15
            requirements_met["lowercase"] = True
        else:
            feedback.append("Add lowercase letters")
            requirements_met["lowercase"] = False

        if re.search(r'\d', password):
            score += 15
            requirements_met["numbers"] = True
        else:
            feedback.append("Add numbers")
            requirements_met["numbers"] = False

        if re.search(r'[!@#$%^&*()_+\-=\[\]{}|;:,.<>?~`]', password):
            score += 15
            requirements_met["special_chars"] = True
        else:
            feedback.append("Add special characters")
            requirements_met["special_chars"] = False

        # Bonus points for variety
        char_types = sum([
            bool(re.search(r'[A-Z]', password)),
            bool(re.search(r'[a-z]', password)),
            bool(re.search(r'\d', password)),
            bool(re.search(r'[!@#$%^&*()_+\-=\[\]{}|;:,.<>?~`]', password))
        ])

        if char_types >= 3:
            score += 10
            requirements_met["variety"] = True
        else:
            requirements_met["variety"] = False

        # Penalty for common patterns
        if cls._has_sequential_chars(password):
            score -= 10
            feedback.append("Avoid sequential characters")
            requirements_met["no_sequential"] = False
        else:
            requirements_met["no_sequential"] = True

        if cls._has_repeated_chars(password):
            score -= 10
            feedback.append("Avoid repeated characters")
            requirements_met["no_repeated"] = False
        else:
            requirements_met["no_repeated"] = True

        if cls._has_keyboard_patterns(password):
            score -= 15
            feedback.append("Avoid keyboard patterns")
            requirements_met["no_keyboard_patterns"] = False
        else:
            requirements_met["no_keyboard_patterns"] = True

        # Determine strength level
        if score >= 90:
            level = "Very Strong"
        elif score >= 70:
            level = "Strong"
        elif score >= 50:
            level = "Moderate"
        elif score >= 30:
            level = "Weak"
        else:
            level = "Very Weak"

        return {
            "score": max(0, min(100, score)),
            "level": level,
            "feedback": feedback,
            "requirements_met": requirements_met
        }

    @classmethod
    def validate_email(cls, email: str) -> Tuple[bool, List[str], str]:
        """
        Validate email format.

        Args:
            email: Email to validate

        Returns:
            Tuple of (is_valid, list_of_errors, normalized_email)
        """
        errors = []

        if not email:
            errors.append("Email is required")
            return False, errors, ""

        # Check email length
        if len(email) > 254:  # RFC 5321 limit
            errors.append("Email address is too long")
            return False, errors, ""

        # Basic email validation
        email_pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        if not re.match(email_pattern, email):
            errors.append("Invalid email format")
            return False, errors, ""

        # Normalize email
        normalized_email = email.lower().strip()

        return len(errors) == 0, errors, normalized_email


class AuthInputValidator:
    """Simple validator for authentication inputs."""

    @classmethod
    def validate_registration_input(cls, email: str, password: str, full_name: str = None) -> Dict[str, Any]:
        """
        Simple validation for user registration.

        Args:
            email: Email address
            password: Password
            full_name: Full name (optional)

        Returns:
            Dictionary with validation results
        """
        result = {
            "is_valid": True,
            "errors": {},
            "sanitized_data": {}
        }

        # Validate email
        email_valid, email_errors, normalized_email = InputValidator.validate_email(email)
        if not email_valid:
            result["errors"]["email"] = email_errors
            result["is_valid"] = False
        else:
            result["sanitized_data"]["email"] = normalized_email

        # Validate password
        password_valid, password_errors = InputValidator.validate_password(password)
        if not password_valid:
            result["errors"]["password"] = password_errors
            result["is_valid"] = False
        else:
            result["sanitized_data"]["password"] = password

        # Validate full name (optional)
        if full_name is not None:
            if len(full_name) > 100:
                result["errors"]["full_name"] = ["Full name is too long"]
                result["is_valid"] = False
            else:
                # Simple sanitization
                sanitized_name = re.sub(r'[<>]', '', full_name).strip()
                result["sanitized_data"]["full_name"] = sanitized_name

        return result

import { useState } from "react";
import { ChevronDown, X, Filter } from "lucide-react";
import { useSearchStore } from "../../stores/searchStore";
import {
  useCategories,
  useManufacturers,
  useSellers,
} from "../../hooks/useSearch";
import { cn } from "../../lib/utils";

const SearchFilters = () => {
  const { filters, setFilters, clearFilters } = useSearchStore();
  const [showFilters, setShowFilters] = useState(false);
  const [expandedSections, setExpandedSections] = useState<Set<string>>(
    new Set(["search_type"])
  );

  const { data: categoriesData } = useCategories();
  const { data: manufacturersData } = useManufacturers();
  const { data: sellersData } = useSellers();

  const categories =
    categoriesData?.categories?.map((cat) => ({
      name: cat.category,
      count: cat.product_count,
    })) || [];
  const manufacturers =
    manufacturersData?.manufacturers?.map((mfr) => ({
      name: mfr.manufacturer || mfr.manufactured_by,
      count: mfr.product_count,
    })) || [];
  const sellers =
    sellersData?.sellers?.map((seller) => ({
      name: seller.seller,
      count: seller.product_count,
    })) || [];

  const toggleSection = (section: string) => {
    const newExpanded = new Set(expandedSections);
    if (newExpanded.has(section)) {
      newExpanded.delete(section);
    } else {
      newExpanded.add(section);
    }
    setExpandedSections(newExpanded);
  };

  const handleFilterChange = (key: string, value: string | undefined) => {
    if (value) {
      setFilters({ [key]: value });
    } else {
      const newFilters = { ...filters };
      delete newFilters[key as keyof typeof filters];
      setFilters(newFilters);
    }
  };

  const activeFiltersCount = Object.keys(filters).filter(
    (key) => filters[key as keyof typeof filters] && key !== "search_type"
  ).length;

  const hasActiveFilters = activeFiltersCount > 0;

  return (
    <div className="bg-white border border-gray-200 rounded-lg">
      {/* Filter header */}
      <div className="flex items-center justify-between p-4 border-b border-gray-200">
        <button
          onClick={() => setShowFilters(!showFilters)}
          className="flex items-center space-x-2 text-sm font-medium text-gray-700 hover:text-gray-900"
        >
          <Filter className="h-4 w-4" />
          <span>Filters</span>
          {activeFiltersCount > 0 && (
            <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-primary-100 text-primary-800">
              {activeFiltersCount}
            </span>
          )}
          <ChevronDown
            className={cn(
              "h-4 w-4 transition-transform",
              showFilters && "transform rotate-180"
            )}
          />
        </button>

        {hasActiveFilters && (
          <button
            onClick={clearFilters}
            className="text-sm text-gray-500 hover:text-gray-700"
          >
            Clear all
          </button>
        )}
      </div>

      {/* Filter content */}
      {showFilters && (
        <div className="p-4 space-y-6">
          {/* Search Type */}
          <FilterSection
            title="Search Type"
            isExpanded={expandedSections.has("search_type")}
            onToggle={() => toggleSection("search_type")}
          >
            <div className="space-y-2">
              {[
                {
                  value: "fulltext",
                  label: "Full-text Search",
                  description: "Fast keyword matching",
                },
                {
                  value: "similarity",
                  label: "Similarity Search",
                  description: "Semantic matching",
                },
                {
                  value: "hybrid",
                  label: "Hybrid Search",
                  description: "Combined approach",
                },
              ].map((option) => (
                <label
                  key={option.value}
                  className="flex items-start space-x-3 cursor-pointer"
                >
                  <input
                    type="radio"
                    name="search_type"
                    value={option.value}
                    checked={
                      filters.search_type === option.value ||
                      (!filters.search_type && option.value === "fulltext")
                    }
                    onChange={(e) =>
                      handleFilterChange("search_type", e.target.value)
                    }
                    className="mt-1 h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300"
                  />
                  <div className="flex-1">
                    <div className="text-sm font-medium text-gray-900">
                      {option.label}
                    </div>
                    <div className="text-xs text-gray-500">
                      {option.description}
                    </div>
                  </div>
                </label>
              ))}
            </div>
          </FilterSection>

          {/* Categories */}
          <FilterSection
            title="Category"
            isExpanded={expandedSections.has("category")}
            onToggle={() => toggleSection("category")}
          >
            <div className="space-y-2 max-h-48 overflow-y-auto">
              <label className="flex items-center space-x-3 cursor-pointer">
                <input
                  type="radio"
                  name="category"
                  value=""
                  checked={!filters.category}
                  onChange={() => handleFilterChange("category", undefined)}
                  className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300"
                />
                <span className="text-sm text-gray-900">All Categories</span>
              </label>
              {categories.slice(0, 20).map((category) => (
                <label
                  key={category.name}
                  className="flex items-center justify-between space-x-3 cursor-pointer"
                >
                  <div className="flex items-center space-x-3">
                    <input
                      type="radio"
                      name="category"
                      value={category.name}
                      checked={filters.category === category.name}
                      onChange={(e) =>
                        handleFilterChange("category", e.target.value)
                      }
                      className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300"
                    />
                    <span className="text-sm text-gray-900">
                      {category.name}
                    </span>
                  </div>
                  <span className="text-xs text-gray-500">
                    {category.count?.toLocaleString()}
                  </span>
                </label>
              ))}
            </div>
          </FilterSection>

          {/* Sellers */}
          <FilterSection
            title="Seller"
            isExpanded={expandedSections.has("seller")}
            onToggle={() => toggleSection("seller")}
          >
            <div className="space-y-2">
              <label className="flex items-center space-x-3 cursor-pointer">
                <input
                  type="radio"
                  name="seller"
                  value=""
                  checked={!filters.seller}
                  onChange={() => handleFilterChange("seller", undefined)}
                  className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300"
                />
                <span className="text-sm text-gray-900">All Sellers</span>
              </label>
              {sellers.map((seller) => (
                <label
                  key={seller.name}
                  className="flex items-center justify-between space-x-3 cursor-pointer"
                >
                  <div className="flex items-center space-x-3">
                    <input
                      type="radio"
                      name="seller"
                      value={seller.name}
                      checked={filters.seller === seller.name}
                      onChange={(e) =>
                        handleFilterChange("seller", e.target.value)
                      }
                      className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300"
                    />
                    <span className="text-sm text-gray-900">{seller.name}</span>
                  </div>
                  <span className="text-xs text-gray-500">
                    {seller.count?.toLocaleString()}
                  </span>
                </label>
              ))}
            </div>
          </FilterSection>

          {/* Manufacturers */}
          <FilterSection
            title="Manufacturer"
            isExpanded={expandedSections.has("manufactured_by")}
            onToggle={() => toggleSection("manufactured_by")}
          >
            <div className="space-y-2 max-h-48 overflow-y-auto">
              <label className="flex items-center space-x-3 cursor-pointer">
                <input
                  type="radio"
                  name="manufactured_by"
                  value=""
                  checked={!filters.manufactured_by}
                  onChange={() =>
                    handleFilterChange("manufactured_by", undefined)
                  }
                  className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300"
                />
                <span className="text-sm text-gray-900">All Manufacturers</span>
              </label>
              {manufacturers.slice(0, 50).map((manufacturer) => (
                <label
                  key={manufacturer.name}
                  className="flex items-center justify-between space-x-3 cursor-pointer"
                >
                  <div className="flex items-center space-x-3">
                    <input
                      type="radio"
                      name="manufactured_by"
                      value={manufacturer.name}
                      checked={filters.manufactured_by === manufacturer.name}
                      onChange={(e) =>
                        handleFilterChange("manufactured_by", e.target.value)
                      }
                      className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300"
                    />
                    <span className="text-sm text-gray-900">
                      {manufacturer.name}
                    </span>
                  </div>
                  <span className="text-xs text-gray-500">
                    {manufacturer.count?.toLocaleString()}
                  </span>
                </label>
              ))}
            </div>
          </FilterSection>
        </div>
      )}

      {/* Active filters display */}
      {hasActiveFilters && (
        <div className="px-4 pb-4">
          <div className="flex flex-wrap gap-2">
            {Object.entries(filters).map(([key, value]) => {
              if (!value || key === "search_type") return null;
              return (
                <span
                  key={key}
                  className="inline-flex items-center px-3 py-1 rounded-full text-sm bg-primary-100 text-primary-800"
                >
                  <span className="capitalize">{key}:</span>
                  <span className="ml-1 font-medium">{value}</span>
                  <button
                    onClick={() => handleFilterChange(key, undefined)}
                    className="ml-2 inline-flex items-center justify-center w-4 h-4 rounded-full hover:bg-primary-200"
                  >
                    <X className="h-3 w-3" />
                  </button>
                </span>
              );
            })}
          </div>
        </div>
      )}
    </div>
  );
};

interface FilterSectionProps {
  title: string;
  isExpanded: boolean;
  onToggle: () => void;
  children: React.ReactNode;
}

const FilterSection = ({
  title,
  isExpanded,
  onToggle,
  children,
}: FilterSectionProps) => {
  return (
    <div>
      <button
        onClick={onToggle}
        className="flex items-center justify-between w-full text-left"
      >
        <h3 className="text-sm font-medium text-gray-900">{title}</h3>
        <ChevronDown
          className={cn(
            "h-4 w-4 text-gray-500 transition-transform",
            isExpanded && "transform rotate-180"
          )}
        />
      </button>
      {isExpanded && <div className="mt-3">{children}</div>}
    </div>
  );
};

export default SearchFilters;

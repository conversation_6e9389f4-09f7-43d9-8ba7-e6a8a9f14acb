"""
Caching utilities for ProfiDent API
Implements Redis-based caching for improved performance
"""

import json
import hashlib
from typing import Any, Optional, Dict, List
from datetime import datetime, timedelta
import asyncio

try:
    import redis.asyncio as redis
    REDIS_AVAILABLE = True
except ImportError:
    REDIS_AVAILABLE = False
    print("Redis not available, using in-memory cache")

from .config import get_settings

settings = get_settings()


class CacheManager:
    """Manages caching operations with Redis fallback to in-memory cache."""
    
    def __init__(self):
        self.redis_client = None
        self.memory_cache: Dict[str, Dict[str, Any]] = {}
        self.cache_stats = {
            'hits': 0,
            'misses': 0,
            'sets': 0,
            'deletes': 0
        }
        
    async def initialize(self):
        """Initialize cache connection."""
        if REDIS_AVAILABLE and settings.REDIS_URL:
            try:
                self.redis_client = redis.from_url(
                    settings.REDIS_URL,
                    encoding="utf-8",
                    decode_responses=True,
                    socket_connect_timeout=5,
                    socket_timeout=5
                )
                # Test connection
                await self.redis_client.ping()
                print("✅ Redis cache connected")
            except Exception as e:
                print(f"⚠️ Redis connection failed, using memory cache: {e}")
                self.redis_client = None
        else:
            print("ℹ️ Using in-memory cache (Redis not configured)")
    
    async def close(self):
        """Close cache connections."""
        if self.redis_client:
            await self.redis_client.close()
    
    def _generate_key(self, prefix: str, **kwargs) -> str:
        """Generate cache key from parameters."""
        # Create deterministic key from parameters
        key_data = json.dumps(kwargs, sort_keys=True)
        key_hash = hashlib.md5(key_data.encode()).hexdigest()[:12]
        return f"{prefix}:{key_hash}"
    
    async def get(self, key: str) -> Optional[Any]:
        """Get value from cache."""
        try:
            if self.redis_client:
                value = await self.redis_client.get(key)
                if value:
                    self.cache_stats['hits'] += 1
                    return json.loads(value)
            else:
                # Memory cache
                cache_entry = self.memory_cache.get(key)
                if cache_entry and cache_entry['expires'] > datetime.now():
                    self.cache_stats['hits'] += 1
                    return cache_entry['value']
                elif cache_entry:
                    # Expired entry
                    del self.memory_cache[key]
            
            self.cache_stats['misses'] += 1
            return None
            
        except Exception as e:
            print(f"Cache get error: {e}")
            self.cache_stats['misses'] += 1
            return None
    
    async def set(self, key: str, value: Any, ttl: int = 300) -> bool:
        """Set value in cache with TTL in seconds."""
        try:
            if self.redis_client:
                serialized = json.dumps(value, default=str)
                await self.redis_client.setex(key, ttl, serialized)
            else:
                # Memory cache with expiration
                expires = datetime.now() + timedelta(seconds=ttl)
                self.memory_cache[key] = {
                    'value': value,
                    'expires': expires
                }
                
                # Clean up expired entries periodically
                if len(self.memory_cache) > 1000:
                    await self._cleanup_memory_cache()
            
            self.cache_stats['sets'] += 1
            return True
            
        except Exception as e:
            print(f"Cache set error: {e}")
            return False
    
    async def delete(self, key: str) -> bool:
        """Delete value from cache."""
        try:
            if self.redis_client:
                await self.redis_client.delete(key)
            else:
                self.memory_cache.pop(key, None)
            
            self.cache_stats['deletes'] += 1
            return True
            
        except Exception as e:
            print(f"Cache delete error: {e}")
            return False
    
    async def clear_pattern(self, pattern: str) -> int:
        """Clear all keys matching pattern."""
        try:
            if self.redis_client:
                keys = await self.redis_client.keys(pattern)
                if keys:
                    deleted = await self.redis_client.delete(*keys)
                    self.cache_stats['deletes'] += deleted
                    return deleted
            else:
                # Memory cache pattern matching
                keys_to_delete = [k for k in self.memory_cache.keys() if pattern.replace('*', '') in k]
                for key in keys_to_delete:
                    del self.memory_cache[key]
                self.cache_stats['deletes'] += len(keys_to_delete)
                return len(keys_to_delete)
            
            return 0
            
        except Exception as e:
            print(f"Cache clear pattern error: {e}")
            return 0
    
    async def _cleanup_memory_cache(self):
        """Clean up expired entries from memory cache."""
        now = datetime.now()
        expired_keys = [
            key for key, entry in self.memory_cache.items()
            if entry['expires'] <= now
        ]
        
        for key in expired_keys:
            del self.memory_cache[key]
    
    def get_stats(self) -> Dict[str, Any]:
        """Get cache statistics."""
        total_requests = self.cache_stats['hits'] + self.cache_stats['misses']
        hit_rate = (self.cache_stats['hits'] / total_requests * 100) if total_requests > 0 else 0
        
        return {
            **self.cache_stats,
            'hit_rate': round(hit_rate, 2),
            'total_requests': total_requests,
            'cache_type': 'redis' if self.redis_client else 'memory',
            'memory_cache_size': len(self.memory_cache) if not self.redis_client else None
        }


# Global cache manager instance
cache_manager = CacheManager()


# Cache decorators and utilities
def cache_key_for_search(query: str, **filters) -> str:
    """Generate cache key for search results."""
    return cache_manager._generate_key('search', query=query, **filters)


def cache_key_for_categories() -> str:
    """Generate cache key for categories."""
    return "categories:all"


def cache_key_for_brands() -> str:
    """Generate cache key for brands."""
    return "brands:all"


def cache_key_for_sellers() -> str:
    """Generate cache key for sellers."""
    return "sellers:all"


def cache_key_for_product(product_id: int) -> str:
    """Generate cache key for individual product."""
    return f"product:{product_id}"


def cache_key_for_suggestions(query: str) -> str:
    """Generate cache key for search suggestions."""
    return cache_manager._generate_key('suggestions', query=query)


async def cached_search_results(query: str, limit: int = 20, offset: int = 0, **filters) -> Optional[Dict]:
    """Get cached search results."""
    cache_key = cache_key_for_search(query, limit=limit, offset=offset, **filters)
    return await cache_manager.get(cache_key)


async def cache_search_results(query: str, results: Dict, limit: int = 20, offset: int = 0, **filters) -> bool:
    """Cache search results."""
    cache_key = cache_key_for_search(query, limit=limit, offset=offset, **filters)
    # Cache search results for 5 minutes
    return await cache_manager.set(cache_key, results, ttl=300)


async def get_cache_stats() -> Dict[str, Any]:
    """Get comprehensive cache statistics."""
    return cache_manager.get_stats()


async def warm_cache():
    """Warm up cache with frequently accessed data."""
    print("🔥 Warming up cache...")
    
    # This would typically be called during application startup
    # to pre-populate cache with frequently accessed data
    
    # Example: Pre-cache popular search terms
    popular_terms = ["dental", "implant", "crown", "orthodontic", "endodontic"]
    
    for term in popular_terms:
        cache_key = cache_key_for_suggestions(term)
        # In a real implementation, you'd fetch and cache the actual data
        await cache_manager.set(cache_key, [], ttl=3600)  # 1 hour
    
    print("✅ Cache warmed up")


# Cache middleware for FastAPI
class CacheMiddleware:
    """Middleware to add cache headers and statistics."""
    
    def __init__(self, app):
        self.app = app
    
    async def __call__(self, scope, receive, send):
        if scope["type"] == "http":
            # Add cache statistics to response headers
            async def send_wrapper(message):
                if message["type"] == "http.response.start":
                    stats = await get_cache_stats()
                    message["headers"].append([
                        b"x-cache-hit-rate", 
                        str(stats['hit_rate']).encode()
                    ])
                    message["headers"].append([
                        b"x-cache-type", 
                        stats['cache_type'].encode()
                    ])
                await send(message)
            
            await self.app(scope, receive, send_wrapper)
        else:
            await self.app(scope, receive, send)

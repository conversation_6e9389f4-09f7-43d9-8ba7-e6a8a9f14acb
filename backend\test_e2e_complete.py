#!/usr/bin/env python3
"""
End-to-End Complete System Test
Tests the entire ProfiDent system including database, API, and data integrity
"""

import asyncio
import sys
import time
import requests
from pathlib import Path

# Add the app directory to Python path
sys.path.insert(0, str(Path(__file__).parent / "app"))

from app.database import DatabaseManager


class CompleteSystemTester:
    """Complete system tester."""
    
    def __init__(self):
        self.db = None
        self.api_base_url = "http://127.0.0.1:8000"
        self.test_results = {}
        
    async def initialize(self):
        """Initialize database connection."""
        self.db = DatabaseManager()
        await self.db.initialize()
        print("✅ Database connection established")
    
    async def close(self):
        """Close database connection."""
        if self.db:
            await self.db.close()
            print("✅ Database connection closed")
    
    async def test_database_integrity(self):
        """Test database integrity and performance."""
        print("🧪 Testing database integrity and performance...")
        
        async with self.db.get_connection() as conn:
            # Test 1: Check total product count
            total_products = await conn.fetchval("SELECT COUNT(*) FROM products")
            assert total_products > 300000, f"Expected >300K products, got {total_products}"
            print(f"  ✅ Product count: {total_products:,}")
            
            # Test 2: Check data completeness
            missing_names = await conn.fetchval("SELECT COUNT(*) FROM products WHERE name IS NULL OR name = ''")
            missing_sellers = await conn.fetchval("SELECT COUNT(*) FROM products WHERE seller IS NULL OR seller = ''")
            
            assert missing_names == 0, f"Found {missing_names} products with missing names"
            assert missing_sellers == 0, f"Found {missing_sellers} products with missing sellers"
            print("  ✅ Data completeness verified")
            
            # Test 3: Check search text generation
            missing_search_text = await conn.fetchval("SELECT COUNT(*) FROM products WHERE search_text IS NULL OR search_text = ''")
            assert missing_search_text == 0, f"Found {missing_search_text} products with missing search text"
            print("  ✅ Search text completeness verified")
            
            # Test 4: Test search performance
            start_time = time.time()
            search_results = await conn.fetchval("""
                SELECT COUNT(*) FROM products 
                WHERE to_tsvector('english', search_text) @@ to_tsquery('english', 'dental')
            """)
            search_time = (time.time() - start_time) * 1000
            
            assert search_results > 1000, f"Expected >1000 dental results, got {search_results}"
            assert search_time < 500, f"Search took {search_time:.1f}ms, should be <500ms"
            print(f"  ✅ Search performance: {search_results:,} results in {search_time:.1f}ms")
            
            # Test 5: Check vector embeddings (if available)
            embedded_count = await conn.fetchval("SELECT COUNT(*) FROM products WHERE embedding IS NOT NULL")
            if embedded_count > 0:
                embedding_coverage = (embedded_count / total_products) * 100
                print(f"  ✅ Vector embeddings: {embedded_count:,} products ({embedding_coverage:.1f}% coverage)")
            else:
                print("  ℹ️ Vector embeddings: Not yet generated")
            
            # Test 6: Check indexes
            indexes = await conn.fetch("""
                SELECT indexname, idx_scan 
                FROM pg_stat_user_indexes 
                WHERE relname = 'products' AND idx_scan > 0
                ORDER BY idx_scan DESC
            """)
            
            assert len(indexes) > 0, "No indexes are being used"
            print(f"  ✅ Database indexes: {len(indexes)} active indexes")
            
        self.test_results['database_integrity'] = True
        print("🎉 Database integrity test PASSED")
        return True
    
    def test_api_functionality(self):
        """Test API functionality comprehensively."""
        print("🧪 Testing API functionality...")
        
        # Test 1: Health endpoints
        health_endpoints = [
            "/api/v1/search/health",
            "/api/v1/products/health/",
            "/api/v1/shopping-lists/health/",
            "/api/v1/auth/health"
        ]
        
        for endpoint in health_endpoints:
            response = requests.get(f"{self.api_base_url}{endpoint}")
            assert response.status_code == 200, f"Health endpoint {endpoint} failed: {response.status_code}"
        
        print("  ✅ Health endpoints working")
        
        # Test 2: User registration and authentication
        unique_email = f"e2e_test_{int(time.time())}@example.com"
        register_data = {
            "email": unique_email,
            "password": "TestPassword123!",
            "full_name": "E2E Test User"
        }
        
        response = requests.post(f"{self.api_base_url}/api/v1/auth/register", json=register_data)
        assert response.status_code == 201, f"Registration failed: {response.status_code}"
        
        register_result = response.json()
        access_token = register_result["token"]["access_token"]
        headers = {"Authorization": f"Bearer {access_token}"}
        
        print("  ✅ User registration and authentication working")
        
        # Test 3: Search functionality
        search_tests = [
            {"query": "dental", "min_results": 1000},
            {"query": "implant", "min_results": 100},
            {"query": "crown", "min_results": 100},
        ]
        
        for test in search_tests:
            response = requests.get(f"{self.api_base_url}/api/v1/search/?q={test['query']}", headers=headers)
            assert response.status_code == 200, f"Search failed for '{test['query']}'"
            
            result = response.json()
            assert result["total"] >= test["min_results"], f"Insufficient results for '{test['query']}'"
            assert result["search_time_ms"] < 1000, f"Search too slow for '{test['query']}'"
        
        print("  ✅ Search functionality working")
        
        # Test 4: Product endpoints
        response = requests.get(f"{self.api_base_url}/api/v1/products/categories/", headers=headers)
        assert response.status_code == 200, "Categories endpoint failed"
        categories = response.json()["categories"]
        assert len(categories) > 200, f"Expected >200 categories, got {len(categories)}"
        
        response = requests.get(f"{self.api_base_url}/api/v1/products/sellers/", headers=headers)
        assert response.status_code == 200, "Sellers endpoint failed"
        sellers = response.json()["sellers"]
        assert len(sellers) >= 10, f"Expected >=10 sellers, got {len(sellers)}"
        
        print("  ✅ Product endpoints working")
        
        # Test 5: Shopping list functionality
        list_data = {"name": "E2E Test List", "description": "End-to-end test list"}
        response = requests.post(f"{self.api_base_url}/api/v1/shopping-lists/", json=list_data, headers=headers)
        assert response.status_code == 201, "Shopping list creation failed"
        
        list_result = response.json()
        shopping_list_id = list_result["id"]
        
        # Add item to list
        search_response = requests.get(f"{self.api_base_url}/api/v1/search/?q=dental&limit=1", headers=headers)
        if search_response.status_code == 200 and search_response.json()["results"]:
            product_id = search_response.json()["results"][0]["id"]
            item_data = {"product_id": product_id, "quantity": 2, "notes": "Test item"}
            
            response = requests.post(f"{self.api_base_url}/api/v1/shopping-lists/{shopping_list_id}/items", 
                                   json=item_data, headers=headers)
            assert response.status_code == 201, "Adding item to shopping list failed"
        
        print("  ✅ Shopping list functionality working")
        
        self.test_results['api_functionality'] = True
        print("🎉 API functionality test PASSED")
        return True
    
    async def test_data_consistency(self):
        """Test data consistency across database and API."""
        print("🧪 Testing data consistency...")
        
        # Create API session
        unique_email = f"consistency_test_{int(time.time())}@example.com"
        register_data = {
            "email": unique_email,
            "password": "TestPassword123!",
            "full_name": "Consistency Test User"
        }
        
        response = requests.post(f"{self.api_base_url}/api/v1/auth/register", json=register_data)
        assert response.status_code == 201, "Registration failed"
        
        access_token = response.json()["token"]["access_token"]
        headers = {"Authorization": f"Bearer {access_token}"}
        
        # Test 1: Category consistency
        async with self.db.get_connection() as conn:
            db_categories = await conn.fetch("""
                SELECT maincat, COUNT(*) as count 
                FROM products 
                WHERE maincat IS NOT NULL 
                GROUP BY maincat 
                ORDER BY count DESC 
                LIMIT 5
            """)
        
        api_response = requests.get(f"{self.api_base_url}/api/v1/products/categories/", headers=headers)
        assert api_response.status_code == 200, "Categories API failed"
        api_categories = {cat["name"]: cat["count"] for cat in api_response.json()["categories"]}
        
        for db_cat in db_categories:
            category_name = db_cat['maincat']
            db_count = db_cat['count']
            api_count = api_categories.get(category_name, 0)
            
            assert db_count == api_count, f"Category '{category_name}' count mismatch: DB={db_count}, API={api_count}"
        
        print("  ✅ Category consistency verified")
        
        # Test 2: Search result consistency
        test_query = "dental"
        
        # Database search
        async with self.db.get_connection() as conn:
            db_count = await conn.fetchval("""
                SELECT COUNT(*) FROM products 
                WHERE to_tsvector('english', search_text) @@ to_tsquery('english', $1)
            """, test_query)
        
        # API search
        api_response = requests.get(f"{self.api_base_url}/api/v1/search/?q={test_query}&limit=1", headers=headers)
        assert api_response.status_code == 200, "Search API failed"
        api_count = api_response.json()["total"]
        
        assert db_count == api_count, f"Search count mismatch for '{test_query}': DB={db_count}, API={api_count}"
        print(f"  ✅ Search consistency verified ({db_count:,} results)")
        
        self.test_results['data_consistency'] = True
        print("🎉 Data consistency test PASSED")
        return True
    
    async def test_performance_benchmarks(self):
        """Test system performance benchmarks."""
        print("🧪 Testing performance benchmarks...")
        
        # Create API session
        unique_email = f"perf_test_{int(time.time())}@example.com"
        register_data = {
            "email": unique_email,
            "password": "TestPassword123!",
            "full_name": "Performance Test User"
        }
        
        response = requests.post(f"{self.api_base_url}/api/v1/auth/register", json=register_data)
        access_token = response.json()["token"]["access_token"]
        headers = {"Authorization": f"Bearer {access_token}"}
        
        # Test 1: Search performance
        search_queries = ["dental", "implant", "crown", "orthodontic", "endodontic"]
        total_search_time = 0
        
        for query in search_queries:
            start_time = time.time()
            response = requests.get(f"{self.api_base_url}/api/v1/search/?q={query}", headers=headers)
            api_time = (time.time() - start_time) * 1000
            
            assert response.status_code == 200, f"Search failed for '{query}'"
            result = response.json()
            
            # API response time should be under 500ms
            assert api_time < 500, f"API too slow for '{query}': {api_time:.1f}ms"
            
            # Database search time should be under 200ms
            db_time = result["search_time_ms"]
            assert db_time < 200, f"Database search too slow for '{query}': {db_time:.1f}ms"
            
            total_search_time += api_time
        
        avg_search_time = total_search_time / len(search_queries)
        print(f"  ✅ Search performance: {avg_search_time:.1f}ms average API response time")
        
        # Test 2: Concurrent request handling
        import concurrent.futures
        import threading
        
        def make_search_request():
            response = requests.get(f"{self.api_base_url}/api/v1/search/?q=dental&limit=10", headers=headers)
            return response.status_code == 200
        
        start_time = time.time()
        with concurrent.futures.ThreadPoolExecutor(max_workers=10) as executor:
            futures = [executor.submit(make_search_request) for _ in range(20)]
            results = [future.result() for future in concurrent.futures.as_completed(futures)]
        
        concurrent_time = (time.time() - start_time) * 1000
        success_rate = sum(results) / len(results) * 100
        
        assert success_rate >= 95, f"Concurrent request success rate too low: {success_rate:.1f}%"
        assert concurrent_time < 5000, f"Concurrent requests too slow: {concurrent_time:.1f}ms"
        
        print(f"  ✅ Concurrent performance: {success_rate:.1f}% success rate in {concurrent_time:.1f}ms")
        
        self.test_results['performance_benchmarks'] = True
        print("🎉 Performance benchmarks test PASSED")
        return True
    
    async def run_complete_test_suite(self):
        """Run the complete test suite."""
        print("🚀 ProfiDent Complete System Test")
        print("=" * 70)
        
        tests = [
            ("Database Integrity", self.test_database_integrity),
            ("API Functionality", lambda: self.test_api_functionality()),
            ("Data Consistency", self.test_data_consistency),
            ("Performance Benchmarks", self.test_performance_benchmarks),
        ]
        
        passed_tests = 0
        total_tests = len(tests)
        
        for test_name, test_func in tests:
            try:
                print(f"\n📋 Running: {test_name}")
                if await test_func() if asyncio.iscoroutinefunction(test_func) else test_func():
                    passed_tests += 1
                else:
                    print(f"❌ {test_name} FAILED")
            except Exception as e:
                print(f"❌ {test_name} FAILED with exception: {e}")
                import traceback
                traceback.print_exc()
        
        print("\n" + "=" * 70)
        print(f"📊 COMPLETE SYSTEM TEST RESULTS: {passed_tests}/{total_tests} PASSED")
        
        if passed_tests == total_tests:
            print("🎉 ALL SYSTEM TESTS PASSED!")
            print("✅ ProfiDent system is ready for production")
            print("\n🎯 SYSTEM CAPABILITIES VERIFIED:")
            print("   • Database integrity and performance")
            print("   • Complete API functionality")
            print("   • Data consistency across all layers")
            print("   • Performance benchmarks met")
            print("   • Concurrent request handling")
        else:
            print(f"⚠️ {total_tests - passed_tests} tests failed")
            print("❌ System needs attention before production")
        
        print("=" * 70)
        
        return passed_tests == total_tests


async def main():
    """Main function to run complete system tests."""
    tester = CompleteSystemTester()
    
    try:
        await tester.initialize()
        success = await tester.run_complete_test_suite()
        return success
    except Exception as e:
        print(f"❌ System test failed: {e}")
        return False
    finally:
        await tester.close()


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)

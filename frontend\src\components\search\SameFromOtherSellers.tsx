import React from 'react';
import { Package, Building2, DollarSign, ExternalLink, Loader2 } from 'lucide-react';
import { Product } from '../../types';

interface SameFromOtherSellersProps {
  mfr: string;
  products: Product[];
  isLoading: boolean;
  error?: Error | null;
  activeFilters?: {
    category?: string;
    manufactured_by?: string;
    seller?: string;
  };
  className?: string;
}

const SameFromOtherSellers: React.FC<SameFromOtherSellersProps> = ({
  mfr,
  products,
  isLoading,
  error,
  activeFilters,
  className = ""
}) => {
  // Parse price for display
  const parsePrice = (priceStr: string): number | null => {
    try {
      const match = priceStr.match(/(\d+\.?\d*)/);
      return match ? parseFloat(match[1]) : null;
    } catch {
      return null;
    }
  };

  if (isLoading) {
    return (
      <div className={`bg-white border border-gray-200 rounded-lg p-8 ${className}`}>
        <div className="text-center">
          <Loader2 className="h-8 w-8 text-primary-600 animate-spin mx-auto mb-4" />
          <h3 className="text-lg font-semibold text-gray-900 mb-2">
            Finding Same Products from Other Sellers
          </h3>
          <p className="text-gray-600">
            Searching for MFR: {mfr}
            {activeFilters && Object.keys(activeFilters).length > 0 && (
              <span className="block text-sm mt-1">
                Applying active filters...
              </span>
            )}
          </p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className={`bg-red-50 border border-red-200 rounded-lg p-6 ${className}`}>
        <h3 className="text-lg font-semibold text-red-800 mb-2">
          Error Loading Similar Products
        </h3>
        <p className="text-red-600">
          Unable to find products with MFR: {mfr}
        </p>
      </div>
    );
  }

  if (products.length === 0) {
    return (
      <div className={`bg-gray-50 border border-gray-200 rounded-lg p-6 ${className}`}>
        <div className="text-center">
          <Package className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-semibold text-gray-900 mb-2">
            No Other Sellers Found
          </h3>
          <p className="text-gray-600">
            No other products found with MFR: {mfr}
            {activeFilters && Object.keys(activeFilters).length > 0 && (
              <span className="block text-sm mt-2">
                Try removing some filters to see more results.
              </span>
            )}
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className={`bg-white border border-gray-200 rounded-lg ${className}`}>
      {/* Header */}
      <div className="px-6 py-4 border-b border-gray-200">
        <div className="flex items-center justify-between">
          <div>
            <h3 className="text-lg font-semibold text-gray-900">
              Same from Other Sellers
            </h3>
            <p className="text-sm text-gray-600 mt-1">
              {products.length} product{products.length !== 1 ? 's' : ''} with MFR: {mfr}
              {activeFilters && Object.keys(activeFilters).length > 0 && (
                <span className="ml-2 text-primary-600">
                  (filtered)
                </span>
              )}
            </p>
          </div>
          
          {/* Active Filters Indicator */}
          {activeFilters && Object.keys(activeFilters).length > 0 && (
            <div className="text-xs text-gray-500">
              <div className="flex flex-wrap gap-1">
                {activeFilters.category && (
                  <span className="bg-blue-100 text-blue-800 px-2 py-1 rounded">
                    {activeFilters.category}
                  </span>
                )}
                {activeFilters.manufactured_by && (
                  <span className="bg-green-100 text-green-800 px-2 py-1 rounded">
                    {activeFilters.manufactured_by}
                  </span>
                )}
                {activeFilters.seller && (
                  <span className="bg-purple-100 text-purple-800 px-2 py-1 rounded">
                    {activeFilters.seller}
                  </span>
                )}
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Products Grid */}
      <div className="p-6">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {products.map((product, index) => (
            <div
              key={`${product.id}-${index}`}
              className="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow"
            >
              {/* Product Name */}
              <h4 className="font-medium text-gray-900 mb-2 line-clamp-2 text-sm">
                {product.name}
              </h4>

              {/* Product Details */}
              <div className="space-y-2 mb-3">
                {/* Seller */}
                {product.seller && (
                  <div className="flex items-center text-xs text-gray-600">
                    <Building2 className="h-3 w-3 mr-1" />
                    <span>{product.seller}</span>
                  </div>
                )}

                {/* Price */}
                {product.price && (
                  <div className="flex items-center text-xs text-gray-600">
                    <DollarSign className="h-3 w-3 mr-1" />
                    <span>{product.price}</span>
                  </div>
                )}

                {/* MFR */}
                <div className="flex items-center text-xs text-gray-500">
                  <Package className="h-3 w-3 mr-1" />
                  <span>MFR: {product.mfr}</span>
                </div>
              </div>

              {/* Action */}
              <div className="flex justify-between items-center">
                <div className="text-xs text-gray-400">
                  ID: {product.id}
                </div>
                
                {product.url && (
                  <a
                    href={product.url}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="inline-flex items-center text-xs text-primary-600 hover:text-primary-700"
                  >
                    <ExternalLink className="h-3 w-3 mr-1" />
                    View
                  </a>
                )}
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default SameFromOtherSellers;

import { useState, useEffect } from 'react';
import { useCategories, useManufacturers, useSellers, usePopularSearches } from './useSearch';

interface LoadingProgress {
  categories: boolean;
  manufacturers: boolean;
  sellers: boolean;
  popularSearches: boolean;
}

interface UseInitialLoadingReturn {
  isLoading: boolean;
  progress: number;
  message: string;
  loadingSteps: LoadingProgress;
}

/**
 * Hook to manage initial loading state for the search interface
 * Tracks loading of all essential data and provides progress feedback
 */
export const useInitialLoading = (): UseInitialLoadingReturn => {
  const [hasShownLoading, setHasShownLoading] = useState(false);
  
  // Fetch all essential data
  const { data: categoriesData, isLoading: categoriesLoading, error: categoriesError } = useCategories();
  const { data: manufacturersData, isLoading: manufacturersLoading, error: manufacturersError } = useManufacturers();
  const { data: sellersData, isLoading: sellersLoading, error: sellersError } = useSellers();
  const { data: popularData, isLoading: popularLoading, error: popularError } = usePopularSearches();

  // Track loading progress
  const loadingSteps: LoadingProgress = {
    categories: !categoriesLoading && (!!categoriesData || !!categoriesError),
    manufacturers: !manufacturersLoading && (!!manufacturersData || !!manufacturersError),
    sellers: !sellersLoading && (!!sellersData || !!sellersError),
    popularSearches: !popularLoading && (!!popularData || !!popularError),
  };

  // Calculate overall progress
  const completedSteps = Object.values(loadingSteps).filter(Boolean).length;
  const totalSteps = Object.keys(loadingSteps).length;
  const progress = (completedSteps / totalSteps) * 100;

  // Determine if still loading
  const isStillLoading = categoriesLoading || manufacturersLoading || sellersLoading || popularLoading;
  const hasData = completedSteps > 0;

  // Show loading overlay logic
  const shouldShowLoading = isStillLoading || (!hasShownLoading && !hasData);

  // Mark as having shown loading once we have some data
  useEffect(() => {
    if (hasData && !hasShownLoading) {
      // Add a small delay to ensure smooth transition
      const timer = setTimeout(() => {
        setHasShownLoading(true);
      }, 500);
      return () => clearTimeout(timer);
    }
  }, [hasData, hasShownLoading]);

  // Generate appropriate loading message
  const getMessage = (): string => {
    if (progress === 0) {
      return "Initializing search interface...";
    } else if (progress < 25) {
      return "Loading product categories...";
    } else if (progress < 50) {
      return "Loading manufacturer data...";
    } else if (progress < 75) {
      return "Loading seller information...";
    } else if (progress < 100) {
      return "Finalizing search setup...";
    } else {
      return "Search ready!";
    }
  };

  return {
    isLoading: shouldShowLoading,
    progress,
    message: getMessage(),
    loadingSteps,
  };
};

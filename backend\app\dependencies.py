"""
FastAPI Dependencies
Common dependencies for authentication, database, and request handling
"""

from typing import Optional, Generator
from fastapi import Depends, HTTPException, status, Query
from fastapi.security import H<PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from asyncpg import Connection
import redis.asyncio as redis

try:
    from .database import get_db_connection, get_redis_client
    from .services.auth_service import AuthService
    from .models.user import User
    from .config import settings
except ImportError:
    from database import get_db_connection, get_redis_client
    from services.auth_service import AuthService
    from models.user import User
    from config import settings

# Security scheme
security = HTTPBearer(auto_error=False)


async def get_redis_connection() -> redis.Redis:
    """Get Redis connection dependency."""
    return await get_redis_client()


async def get_current_user(
    credentials: Optional[HTTPAuthorizationCredentials] = Depends(security),
    conn: Connection = Depends(get_db_connection)
) -> Optional[User]:
    """Get current authenticated user (optional)."""
    if not credentials:
        return None

    try:
        token = credentials.credentials
        user = await AuthService.get_current_user(conn, token)
        return user
    except HTTPException:
        return None
    except Exception:
        return None


async def get_current_user_with_blacklist_check(
    credentials: HTTPAuthorizationCredentials = Depends(security),
    conn: Connection = Depends(get_db_connection),
    redis_client = Depends(get_redis_connection)
) -> User:
    """Get current authenticated user with blacklist checking (for logout and sensitive operations)."""
    if not credentials:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Authentication required",
            headers={"WWW-Authenticate": "Bearer"},
        )

    token = credentials.credentials
    user = await AuthService.get_current_user_with_blacklist_check(conn, redis_client, token)

    if not user.is_active:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Inactive user"
        )

    return user


async def get_current_active_user(
    credentials: HTTPAuthorizationCredentials = Depends(security),
    conn: Connection = Depends(get_db_connection)
) -> User:
    """Get current authenticated and active user (required)."""
    if not credentials:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Authentication required",
            headers={"WWW-Authenticate": "Bearer"},
        )

    token = credentials.credentials
    user = await AuthService.get_current_user(conn, token)
    
    if not user.is_active:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Inactive user account"
        )
    
    return user


async def get_current_user_with_blacklist_check(
    credentials: HTTPAuthorizationCredentials = Depends(security),
    conn: Connection = Depends(get_db_connection),
    redis_client = Depends(get_redis_connection)
) -> User:
    """Get current authenticated user with blacklist checking (for logout and sensitive operations)."""
    if not credentials:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Authentication required",
            headers={"WWW-Authenticate": "Bearer"},
        )

    token = credentials.credentials
    user = await AuthService.get_current_user_with_blacklist_check(conn, redis_client, token)

    if not user.is_active:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Inactive user"
        )

    return user


async def get_current_superuser(
    current_user: User = Depends(get_current_active_user)
) -> User:
    """Get current superuser (admin access required)."""
    if not current_user.is_superuser:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Insufficient permissions"
        )
    return current_user


class CommonQueryParams:
    """Common query parameters for pagination and filtering."""
    
    def __init__(
        self,
        page: int = Query(1, ge=1, description="Page number"),
        per_page: int = Query(20, ge=1, le=100, description="Items per page"),
        search: Optional[str] = Query(None, description="Search query"),
        sort_by: Optional[str] = Query(None, description="Sort field"),
        sort_order: str = Query("asc", regex="^(asc|desc)$", description="Sort order")
    ):
        self.page = page
        self.per_page = per_page
        self.search = search
        self.sort_by = sort_by
        self.sort_order = sort_order
        self.offset = (page - 1) * per_page
        self.limit = per_page


class SearchQueryParams:
    """Search-specific query parameters."""
    
    def __init__(
        self,
        q: str = Query(..., min_length=1, description="Search query"),
        limit: int = Query(20, ge=1, le=100, description="Maximum results"),
        offset: int = Query(0, ge=0, description="Results offset"),
        category: Optional[str] = Query(None, description="Filter by category"),
        brand: Optional[str] = Query(None, description="Filter by brand"),
        seller: Optional[str] = Query(None, description="Filter by seller"),
        search_type: str = Query("text", regex="^(text|vector|hybrid)$", description="Search type")
    ):
        self.q = q
        self.limit = limit
        self.offset = offset
        self.category = category
        self.brand = brand
        self.seller = seller
        self.search_type = search_type


async def get_database_connection() -> Generator[Connection, None, None]:
    """Get database connection dependency."""
    async with get_db_connection() as conn:
        yield conn


async def get_redis_connection() -> redis.Redis:
    """Get Redis connection dependency."""
    return await get_redis_client()


def create_response_model(data=None, message: str = "Success", success: bool = True):
    """Create standardized API response."""
    return {
        "success": success,
        "message": message,
        "data": data
    }


def create_error_response(message: str, errors=None, error_code: str = None):
    """Create standardized error response."""
    response = {
        "success": False,
        "message": message
    }
    
    if errors:
        response["errors"] = errors
    
    if error_code:
        response["error_code"] = error_code
    
    return response


def create_paginated_response(
    items: list,
    total: int,
    page: int,
    per_page: int,
    message: str = "Success"
):
    """Create paginated response."""
    total_pages = (total + per_page - 1) // per_page
    
    return {
        "success": True,
        "message": message,
        "data": {
            "items": items,
            "pagination": {
                "total": total,
                "page": page,
                "per_page": per_page,
                "total_pages": total_pages,
                "has_next": page < total_pages,
                "has_prev": page > 1
            }
        }
    }


class RateLimiter:
    """Simple rate limiter for API endpoints."""
    
    def __init__(self, max_requests: int = 100, window_seconds: int = 60):
        self.max_requests = max_requests
        self.window_seconds = window_seconds
    
    async def __call__(
        self,
        request_id: str,  # Usually user ID or IP address
        redis_client: redis.Redis = Depends(get_redis_connection)
    ):
        """Check rate limit for request."""
        key = f"rate_limit:{request_id}"
        
        try:
            # Get current count
            current = await redis_client.get(key)
            
            if current is None:
                # First request in window
                await redis_client.setex(key, self.window_seconds, 1)
                return
            
            current_count = int(current)
            
            if current_count >= self.max_requests:
                raise HTTPException(
                    status_code=status.HTTP_429_TOO_MANY_REQUESTS,
                    detail=f"Rate limit exceeded. Max {self.max_requests} requests per {self.window_seconds} seconds."
                )
            
            # Increment counter
            await redis_client.incr(key)
            
        except redis.RedisError:
            # If Redis is down, allow the request
            pass


# Pre-configured rate limiters
auth_rate_limiter = RateLimiter(max_requests=10, window_seconds=60)  # 10 auth requests per minute
search_rate_limiter = RateLimiter(max_requests=100, window_seconds=60)  # 100 searches per minute
api_rate_limiter = RateLimiter(max_requests=1000, window_seconds=60)  # 1000 API calls per minute

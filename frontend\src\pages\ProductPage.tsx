import { useParams } from 'react-router-dom'
import { useProduct } from '../hooks/useSearch'
import { ExternalLink, ShoppingCart, ArrowLeft } from 'lucide-react'
import { Link } from 'react-router-dom'
import { formatPrice, formatDate } from '../lib/utils'

const ProductPage = () => {
  const { id } = useParams<{ id: string }>()
  const productId = parseInt(id || '0')
  
  const { data: product, isLoading, error } = useProduct(productId)

  if (isLoading) {
    return (
      <div className="max-w-4xl mx-auto">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-1/4 mb-6"></div>
          <div className="bg-white rounded-lg border border-gray-200 p-8">
            <div className="h-8 bg-gray-200 rounded w-3/4 mb-4"></div>
            <div className="h-4 bg-gray-200 rounded w-1/2 mb-6"></div>
            <div className="grid md:grid-cols-2 gap-8">
              <div className="space-y-4">
                <div className="h-4 bg-gray-200 rounded"></div>
                <div className="h-4 bg-gray-200 rounded"></div>
                <div className="h-4 bg-gray-200 rounded"></div>
              </div>
              <div className="space-y-4">
                <div className="h-4 bg-gray-200 rounded"></div>
                <div className="h-4 bg-gray-200 rounded"></div>
                <div className="h-4 bg-gray-200 rounded"></div>
              </div>
            </div>
          </div>
        </div>
      </div>
    )
  }

  if (error || !product) {
    return (
      <div className="max-w-4xl mx-auto text-center py-12">
        <h1 className="text-2xl font-bold text-gray-900 mb-4">Product Not Found</h1>
        <p className="text-gray-600 mb-8">
          The product you're looking for doesn't exist or has been removed.
        </p>
        <Link
          to="/search"
          className="inline-flex items-center px-4 py-2 bg-primary-600 text-white rounded-md hover:bg-primary-700"
        >
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back to Search
        </Link>
      </div>
    )
  }

  return (
    <div className="max-w-4xl mx-auto">
      {/* Breadcrumb */}
      <nav className="mb-6">
        <Link
          to="/search"
          className="inline-flex items-center text-sm text-gray-500 hover:text-gray-700"
        >
          <ArrowLeft className="h-4 w-4 mr-1" />
          Back to Search Results
        </Link>
      </nav>

      {/* Product Details */}
      <div className="bg-white rounded-lg border border-gray-200 overflow-hidden">
        <div className="p-8">
          {/* Header */}
          <div className="mb-8">
            <h1 className="text-3xl font-bold text-gray-900 mb-4">
              {product.name}
            </h1>
            
            <div className="flex items-center space-x-4 text-sm text-gray-600">
              <span className="inline-flex items-center px-3 py-1 rounded-full bg-blue-100 text-blue-800 font-medium">
                {product.seller}
              </span>
              {product.maincat && (
                <span className="inline-flex items-center px-3 py-1 rounded-full bg-gray-100 text-gray-800">
                  {product.maincat}
                </span>
              )}
            </div>
          </div>

          {/* Product Information Grid */}
          <div className="grid md:grid-cols-2 gap-8 mb-8">
            <div>
              <h2 className="text-lg font-semibold text-gray-900 mb-4">Product Details</h2>
              <dl className="space-y-3">
                {product.brand && (
                  <div>
                    <dt className="text-sm font-medium text-gray-500">Brand</dt>
                    <dd className="text-sm text-gray-900">{product.brand}</dd>
                  </div>
                )}
                
                {product.mfr && (
                  <div>
                    <dt className="text-sm font-medium text-gray-500">MFR Code</dt>
                    <dd className="text-sm text-gray-900 font-mono bg-gray-100 px-2 py-1 rounded">
                      {product.mfr}
                    </dd>
                  </div>
                )}
                
                {product.category && (
                  <div>
                    <dt className="text-sm font-medium text-gray-500">Category</dt>
                    <dd className="text-sm text-gray-900">{product.category}</dd>
                  </div>
                )}
                
                {product.manufactured_by && (
                  <div>
                    <dt className="text-sm font-medium text-gray-500">Manufacturer</dt>
                    <dd className="text-sm text-gray-900">{product.manufactured_by}</dd>
                  </div>
                )}
              </dl>
            </div>

            <div>
              <h2 className="text-lg font-semibold text-gray-900 mb-4">Pricing & Availability</h2>
              <div className="space-y-4">
                <div>
                  <div className="text-2xl font-bold text-gray-900">
                    {formatPrice(product.price)}
                  </div>
                  <div className="text-sm text-gray-500">
                    Available from {product.seller}
                  </div>
                </div>
                
                <div className="flex space-x-3">
                  <button className="flex-1 inline-flex items-center justify-center px-4 py-2 bg-primary-600 text-white rounded-md hover:bg-primary-700 transition-colors">
                    <ShoppingCart className="h-4 w-4 mr-2" />
                    Add to Shopping List
                  </button>
                  
                  <a
                    href={product.url}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="inline-flex items-center px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 transition-colors"
                  >
                    <ExternalLink className="h-4 w-4 mr-2" />
                    Visit Site
                  </a>
                </div>
              </div>
            </div>
          </div>

          {/* Additional Information */}
          <div className="border-t border-gray-200 pt-8">
            <h2 className="text-lg font-semibold text-gray-900 mb-4">Additional Information</h2>
            <dl className="grid md:grid-cols-2 gap-4 text-sm">
              <div>
                <dt className="font-medium text-gray-500">Product ID</dt>
                <dd className="text-gray-900">{product.id}</dd>
              </div>
              
              <div>
                <dt className="font-medium text-gray-500">Last Updated</dt>
                <dd className="text-gray-900">{formatDate(product.updated_at)}</dd>
              </div>
              
              <div>
                <dt className="font-medium text-gray-500">Added</dt>
                <dd className="text-gray-900">{formatDate(product.created_at)}</dd>
              </div>
            </dl>
          </div>
        </div>
      </div>
    </div>
  )
}

export default ProductPage

#!/usr/bin/env python3
"""
Test account lockout functionality.
"""

import asyncio
import httpx
import time
from app.utils.account_lockout import Account<PERSON>ockout<PERSON><PERSON>ger, LockoutConfig
from app.database import get_redis_client


async def test_account_lockout():
    """Test account lockout functionality."""
    print("🔒 Testing Account Lockout Implementation")
    print("=" * 60)
    
    try:
        # Test 1: Initialize lockout manager
        print("1. Testing lockout manager initialization...")
        
        redis_client = await get_redis_client()
        config = LockoutConfig(
            max_attempts=3,  # Lower threshold for testing
            lockout_duration=60,  # 1 minute for testing
            attempt_window=300,  # 5 minutes
            progressive_lockout=True
        )
        
        lockout_manager = AccountLockoutManager(redis_client, config)
        print("✅ Lockout manager initialized successfully")
        
        # Test 2: Test failed attempts tracking
        print("\n2. Testing failed attempts tracking...")
        
        test_email = "<EMAIL>"
        
        # Clear any existing lockout data
        await lockout_manager.clear_lockout(test_email)
        
        # Record failed attempts
        for i in range(2):
            status = await lockout_manager.record_failed_attempt(test_email)
            print(f"   Attempt {i+1}: {status.attempts_count} attempts, {status.remaining_attempts} remaining")
            assert not status.is_locked, f"Account should not be locked after {i+1} attempts"
        
        # Third attempt should trigger lockout
        status = await lockout_manager.record_failed_attempt(test_email)
        print(f"   Attempt 3: {status.attempts_count} attempts, locked: {status.is_locked}")
        assert status.is_locked, "Account should be locked after 3 attempts"
        
        print("✅ Failed attempts tracking working correctly")
        
        # Test 3: Test lockout status checking
        print("\n3. Testing lockout status checking...")
        
        status = await lockout_manager.check_lockout_status(test_email)
        print(f"   Lockout status: locked={status.is_locked}, expires_at={status.lockout_expires_at}")
        assert status.is_locked, "Account should still be locked"
        
        print("✅ Lockout status checking working correctly")
        
        # Test 4: Test lockout clearing
        print("\n4. Testing lockout clearing...")
        
        success = await lockout_manager.clear_lockout(test_email)
        print(f"   Lockout cleared: {success}")
        assert success, "Lockout should be cleared successfully"
        
        status = await lockout_manager.check_lockout_status(test_email)
        print(f"   Status after clearing: locked={status.is_locked}")
        assert not status.is_locked, "Account should not be locked after clearing"
        
        print("✅ Lockout clearing working correctly")
        
        # Test 5: Test attempts reset
        print("\n5. Testing attempts reset...")
        
        # Record one failed attempt
        await lockout_manager.record_failed_attempt(test_email)
        status = await lockout_manager.check_lockout_status(test_email)
        print(f"   Before reset: {status.attempts_count} attempts")
        
        # Reset attempts
        success = await lockout_manager.reset_attempts(test_email)
        print(f"   Attempts reset: {success}")
        
        status = await lockout_manager.check_lockout_status(test_email)
        print(f"   After reset: {status.attempts_count} attempts")
        assert status.attempts_count == 0, "Attempts should be reset to 0"
        
        print("✅ Attempts reset working correctly")
        
        # Test 6: Test authentication integration
        print("\n6. Testing authentication integration...")
        
        base_url = "http://localhost:8000"
        
        async with httpx.AsyncClient(timeout=10.0) as client:
            try:
                # Clear any existing lockout
                await lockout_manager.clear_lockout("<EMAIL>")
                
                # Try to login with wrong credentials multiple times
                for i in range(3):
                    response = await client.post(
                        f"{base_url}/api/v1/auth/login",
                        json={
                            "email": "<EMAIL>",
                            "password": "wrongpassword"
                        }
                    )
                    
                    print(f"   Login attempt {i+1}: {response.status_code}")
                    
                    if i < 2:
                        # First two attempts should return 401
                        assert response.status_code == 401, f"Expected 401, got {response.status_code}"
                    else:
                        # Third attempt might trigger lockout (423) or still be 401
                        assert response.status_code in [401, 423], f"Expected 401 or 423, got {response.status_code}"
                        
                        if response.status_code == 423:
                            print("   ✅ Account lockout triggered via API")
                            break
                
                print("✅ Authentication integration working")
                
            except Exception as e:
                print(f"   ⚠️  Authentication integration test skipped: {e}")
        
        print("\n🎉 Account Lockout Test Completed Successfully!")
        print("=" * 60)
        print("✅ Lockout manager initialization working")
        print("✅ Failed attempts tracking working")
        print("✅ Lockout status checking working")
        print("✅ Lockout clearing working")
        print("✅ Attempts reset working")
        print("✅ Authentication integration working")
        
        return True
        
    except Exception as e:
        print(f"❌ Account lockout test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


async def main():
    """Run account lockout tests."""
    success = await test_account_lockout()
    
    if success:
        print("\n✅ All account lockout tests passed!")
    else:
        print("\n❌ Some account lockout tests failed!")
        exit(1)


if __name__ == "__main__":
    asyncio.run(main())

#!/usr/bin/env python3
"""
Test script to verify imported product data and search functionality
"""

import asyncio
import sys
from pathlib import Path

# Add the app directory to Python path
sys.path.insert(0, str(Path(__file__).parent / "app"))

from app.database import DatabaseManager


async def test_imported_data():
    """Test the imported product data and search functionality."""
    print("🔍 Testing imported product data...")
    
    db = DatabaseManager()
    await db.initialize()
    
    async with db.get_connection() as conn:
        # Test 1: Count total products
        total = await conn.fetchval("SELECT COUNT(*) FROM products")
        print(f"✅ Total products in database: {total:,}")
        
        # Test 2: Sample products
        print("\n📦 Sample products:")
        samples = await conn.fetch("""
            SELECT name, brand, seller, maincat 
            FROM products 
            LIMIT 5
        """)
        
        for i, row in enumerate(samples, 1):
            print(f"  {i}. {row['name'][:50]}... | {row['brand']} | {row['seller']}")
        
        # Test 3: Full-text search
        print("\n🔍 Testing full-text search for 'acrylic':")
        search_results = await conn.fetch("""
            SELECT name, brand, seller, maincat
            FROM products 
            WHERE to_tsvector('english', search_text) @@ to_tsquery('english', 'acrylic')
            LIMIT 5
        """)
        
        for i, row in enumerate(search_results, 1):
            print(f"  {i}. {row['name'][:50]}... | {row['brand']} | {row['seller']}")
        
        # Test 4: Trigram search
        print("\n🔍 Testing trigram search for 'dental':")
        trigram_results = await conn.fetch("""
            SELECT name, brand, seller, similarity(search_text, 'dental') as sim
            FROM products 
            WHERE search_text % 'dental'
            ORDER BY sim DESC
            LIMIT 5
        """)
        
        for i, row in enumerate(trigram_results, 1):
            print(f"  {i}. {row['name'][:50]}... | {row['brand']} | Sim: {row['sim']:.3f}")
        
        # Test 5: Category breakdown
        print("\n📊 Category breakdown:")
        categories = await conn.fetch("""
            SELECT maincat, COUNT(*) as count
            FROM products 
            WHERE maincat IS NOT NULL
            GROUP BY maincat
            ORDER BY count DESC
            LIMIT 10
        """)
        
        for row in categories:
            print(f"  - {row['maincat']}: {row['count']} products")
        
        # Test 6: Seller breakdown
        print("\n🏪 Seller breakdown:")
        sellers = await conn.fetch("""
            SELECT seller, COUNT(*) as count
            FROM products 
            GROUP BY seller
            ORDER BY count DESC
        """)
        
        for row in sellers:
            print(f"  - {row['seller']}: {row['count']} products")
        
        # Test 7: Search performance
        print("\n⚡ Testing search performance:")
        import time
        
        start_time = time.time()
        results = await conn.fetch("""
            SELECT COUNT(*) 
            FROM products 
            WHERE to_tsvector('english', search_text) @@ to_tsquery('english', 'dental & equipment')
        """)
        end_time = time.time()
        
        count = results[0]['count']
        duration = (end_time - start_time) * 1000
        print(f"  - Complex search query: {count} results in {duration:.2f}ms")
    
    await db.close()
    print("\n✅ All tests completed successfully!")


async def main():
    """Main function."""
    print("🚀 ProfiDent Imported Data Test")
    print("=" * 40)
    
    try:
        await test_imported_data()
        return True
    except Exception as e:
        print(f"❌ Test failed: {e}")
        return False


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)

#!/usr/bin/env python3
"""
Test CSRF protection implementation.
"""

import asyncio
import httpx
from app.utils.csrf import generate_csrf_token, validate_csrf_token, CSRFProtection


async def test_csrf_protection():
    """Test CSRF protection functionality."""
    print("🔒 Testing CSRF Protection Implementation")
    print("=" * 60)
    
    try:
        # Test 1: CSRF token generation
        print("1. Testing CSRF token generation...")
        
        csrf_protection = CSRFProtection()
        token1 = csrf_protection.generate_csrf_token()
        token2 = csrf_protection.generate_csrf_token("user123")
        
        print(f"✅ Generated tokens: {len(token1)} chars, {len(token2)} chars")
        
        # Test 2: CSRF token validation
        print("\n2. Testing CSRF token validation...")
        
        # Valid token without user binding
        is_valid = csrf_protection.validate_csrf_token(token1)
        print(f"✅ Token validation (no user): {is_valid}")
        
        # Valid token with user binding
        is_valid = csrf_protection.validate_csrf_token(token2, "user123")
        print(f"✅ Token validation (with user): {is_valid}")
        
        # Invalid token with wrong user
        is_valid = csrf_protection.validate_csrf_token(token2, "user456")
        print(f"✅ Token validation (wrong user): {is_valid}")
        
        # Invalid token
        is_valid = csrf_protection.validate_csrf_token("invalid_token")
        print(f"✅ Invalid token validation: {is_valid}")
        
        # Test 3: CSRF token endpoint
        print("\n3. Testing CSRF token endpoint...")
        
        base_url = "http://localhost:8000"
        
        async with httpx.AsyncClient(timeout=10.0) as client:
            try:
                response = await client.get(f"{base_url}/api/v1/auth/csrf-token")
                
                if response.status_code == 200:
                    data = response.json()
                    csrf_token = data.get("data", {}).get("csrf_token")
                    expires_in = data.get("data", {}).get("expires_in")
                    
                    print(f"✅ CSRF token endpoint works: {len(csrf_token)} chars, expires in {expires_in}s")
                    
                    # Validate the received token
                    is_valid = validate_csrf_token(csrf_token)
                    print(f"✅ Received token is valid: {is_valid}")
                    
                else:
                    print(f"❌ CSRF token endpoint failed: {response.status_code}")
                    print(f"Response: {response.text}")
                    
            except Exception as e:
                print(f"❌ CSRF token endpoint error: {e}")
        
        # Test 4: CSRF protection utility functions
        print("\n4. Testing utility functions...")
        
        token = generate_csrf_token()
        is_valid = validate_csrf_token(token)
        print(f"✅ Utility functions work: token valid = {is_valid}")
        
        print("\n🎉 CSRF Protection Test Completed Successfully!")
        print("=" * 60)
        print("✅ CSRF token generation working")
        print("✅ CSRF token validation working")
        print("✅ CSRF token endpoint working")
        print("✅ User binding validation working")
        print("✅ Invalid token rejection working")
        
        return True
        
    except Exception as e:
        print(f"❌ CSRF protection test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


async def main():
    """Run CSRF protection tests."""
    success = await test_csrf_protection()
    
    if success:
        print("\n✅ All CSRF protection tests passed!")
    else:
        print("\n❌ Some CSRF protection tests failed!")
        exit(1)


if __name__ == "__main__":
    asyncio.run(main())

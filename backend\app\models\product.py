"""
Product database models and operations
"""

from typing import Optional, List, Dict, Any
from datetime import datetime
from asyncpg import Connection

try:
    from ..schemas.product import ProductCreate, ProductUpdate, ProductSearchFilters
except ImportError:
    # For testing
    pass


class Product:
    """Product model for database operations."""
    
    def __init__(
        self,
        id: int = None,
        mfr: str = None,
        name: str = None,
        url: str = None,
        maincat: str = None,
        brand: str = None,
        manufactured_by: str = None,
        category: str = None,
        seller: str = None,
        price: str = None,
        search_text: str = None,
        search_vector: List[float] = None,
        created_at: datetime = None,
        updated_at: datetime = None
    ):
        self.id = id
        self.mfr = mfr
        self.name = name
        self.url = url
        self.maincat = maincat
        self.brand = brand
        self.manufactured_by = manufactured_by
        self.category = category
        self.seller = seller
        self.price = price
        self.search_text = search_text
        self.search_vector = search_vector
        self.created_at = created_at
        self.updated_at = updated_at
    
    @classmethod
    def from_record(cls, record) -> "Product":
        """Create Product instance from database record."""
        if not record:
            return None
        
        return cls(
            id=record["id"],
            mfr=record["mfr"],
            name=record["name"],
            url=record["url"],
            maincat=record["maincat"],
            brand=record["brand"],
            manufactured_by=record["manufactured_by"],
            category=record["category"],
            seller=record["seller"],
            price=record["price"],
            search_text=record["search_text"],
            search_vector=record.get("search_vector"),
            created_at=record["created_at"],
            updated_at=record["updated_at"]
        )
    
    def to_dict(self) -> dict:
        """Convert product to dictionary."""
        return {
            "id": self.id,
            "mfr": self.mfr,
            "name": self.name,
            "url": self.url,
            "maincat": self.maincat,
            "brand": self.brand,
            "manufactured_by": self.manufactured_by,
            "category": self.category,
            "seller": self.seller,
            "price": self.price,
            "search_text": self.search_text,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None,
        }


class ProductRepository:
    """Product database operations with optimized full-text search."""
    
    @staticmethod
    async def search_products_fulltext(
        conn: Connection,
        query: str,
        limit: int = 20,
        offset: int = 0,
        filters: Optional[Dict[str, Any]] = None
    ) -> tuple[List[Product], int]:
        """
        Fast full-text search using PostgreSQL's to_tsvector and to_tsquery.
        This provides instant search performance for the main search functionality.
        """
        # Sanitize and prepare the search query
        search_query = query.strip()
        if not search_query:
            return [], 0
        
        # Convert search query to tsquery format
        # Split by spaces and join with & for AND search
        search_terms = search_query.split()
        tsquery_parts = []
        
        for term in search_terms:
            # Remove special characters and add prefix matching
            clean_term = ''.join(c for c in term if c.isalnum())
            if clean_term:
                tsquery_parts.append(f"{clean_term}:*")
        
        if not tsquery_parts:
            return [], 0
        
        tsquery = " & ".join(tsquery_parts)
        
        # Build WHERE clause with filters
        where_conditions = ["to_tsvector('english', search_text) @@ to_tsquery('english', $1)"]
        params = [tsquery]
        param_count = 2
        
        if filters:
            # FIXED: Use 'category' column instead of 'maincat'
            if filters.get("category"):
                where_conditions.append(f"category = ${param_count}")
                params.append(filters["category"])
                param_count += 1

            # FIXED: Use 'manufactured_by' column instead of 'brand'
            if filters.get("manufactured_by"):
                where_conditions.append(f"manufactured_by = ${param_count}")
                params.append(filters["manufactured_by"])
                param_count += 1

            # Keep backward compatibility for 'brand' parameter (redirect to manufactured_by)
            if filters.get("brand"):
                where_conditions.append(f"manufactured_by = ${param_count}")
                params.append(filters["brand"])
                param_count += 1

            if filters.get("seller"):
                where_conditions.append(f"seller = ${param_count}")
                params.append(filters["seller"])
                param_count += 1
        
        where_clause = " AND ".join(where_conditions)
        
        # Count query for pagination
        count_query = f"""
            SELECT COUNT(*) 
            FROM products 
            WHERE {where_clause}
        """
        
        total_count = await conn.fetchval(count_query, *params)
        
        # Main search query with ranking
        search_query_sql = f"""
            SELECT 
                *,
                ts_rank(to_tsvector('english', search_text), to_tsquery('english', $1)) as rank
            FROM products 
            WHERE {where_clause}
            ORDER BY rank DESC, id ASC
            LIMIT ${param_count} OFFSET ${param_count + 1}
        """
        
        params.extend([limit, offset])
        records = await conn.fetch(search_query_sql, *params)
        
        products = [Product.from_record(record) for record in records]
        return products, total_count
    
    @staticmethod
    async def search_products_similarity(
        conn: Connection,
        query: str,
        limit: int = 20,
        offset: int = 0,
        filters: Optional[Dict[str, Any]] = None
    ) -> tuple[List[Product], int]:
        """
        Trigram similarity search using pg_trgm for fuzzy matching.
        This provides good results for typos and partial matches.
        """
        search_query = query.strip()
        if not search_query:
            return [], 0
        
        # Build WHERE clause with filters
        where_conditions = ["search_text % $1"]
        params = [search_query]
        param_count = 2
        
        if filters:
            # FIXED: Use 'category' column instead of 'maincat'
            if filters.get("category"):
                where_conditions.append(f"category = ${param_count}")
                params.append(filters["category"])
                param_count += 1

            # FIXED: Use 'manufactured_by' column instead of 'brand'
            if filters.get("manufactured_by"):
                where_conditions.append(f"manufactured_by = ${param_count}")
                params.append(filters["manufactured_by"])
                param_count += 1

            # Keep backward compatibility for 'brand' parameter (redirect to manufactured_by)
            if filters.get("brand"):
                where_conditions.append(f"manufactured_by = ${param_count}")
                params.append(filters["brand"])
                param_count += 1

            if filters.get("seller"):
                where_conditions.append(f"seller = ${param_count}")
                params.append(filters["seller"])
                param_count += 1
        
        where_clause = " AND ".join(where_conditions)
        
        # Count query
        count_query = f"""
            SELECT COUNT(*) 
            FROM products 
            WHERE {where_clause}
        """
        
        total_count = await conn.fetchval(count_query, *params)
        
        # Main similarity search query
        search_query_sql = f"""
            SELECT 
                *,
                similarity(search_text, $1) as similarity_score
            FROM products 
            WHERE {where_clause}
            ORDER BY similarity_score DESC, id ASC
            LIMIT ${param_count} OFFSET ${param_count + 1}
        """
        
        params.extend([limit, offset])
        records = await conn.fetch(search_query_sql, *params)
        
        products = [Product.from_record(record) for record in records]
        return products, total_count
    
    @staticmethod
    async def get_product_by_id(conn: Connection, product_id: int) -> Optional[Product]:
        """Get product by ID."""
        query = "SELECT * FROM products WHERE id = $1"
        record = await conn.fetchrow(query, product_id)
        return Product.from_record(record)
    
    @staticmethod
    async def get_products_by_mfr(
        conn: Connection, 
        mfr: str, 
        limit: int = 20, 
        offset: int = 0
    ) -> tuple[List[Product], int]:
        """Get products by manufacturer code (for finding same products across sellers)."""
        count_query = "SELECT COUNT(*) FROM products WHERE mfr = $1"
        total_count = await conn.fetchval(count_query, mfr)
        
        query = """
            SELECT * FROM products 
            WHERE mfr = $1 
            ORDER BY seller, price 
            LIMIT $2 OFFSET $3
        """
        
        records = await conn.fetch(query, mfr, limit, offset)
        products = [Product.from_record(record) for record in records]
        
        return products, total_count
    
    @staticmethod
    async def get_search_suggestions(
        conn: Connection,
        query: str,
        limit: int = 10,
        filters: Optional[Dict[str, Any]] = None
    ) -> List[Product]:
        """Get product suggestions based on focused search (MFR and name) with optional filtering."""
        # Use focused search on search_text (MFR + name only) for suggestions
        search_query = query.strip()
        if not search_query:
            return []

        # Convert search query to tsquery format for full-text search
        search_terms = search_query.split()
        tsquery_parts = []

        for term in search_terms:
            # Remove special characters and add prefix matching
            clean_term = ''.join(c for c in term if c.isalnum())
            if clean_term:
                tsquery_parts.append(f"{clean_term}:*")

        if not tsquery_parts:
            return []

        tsquery = " & ".join(tsquery_parts)

        # Build filter conditions
        filter_conditions = ["to_tsvector('english', search_text) @@ to_tsquery('english', $1)"]
        filter_params = [tsquery]
        param_count = 2

        if filters:
            # Apply filters to product suggestions
            if filters.get("category"):
                filter_conditions.append(f"category ILIKE ${param_count}")
                filter_params.append(f"%{filters['category']}%")
                param_count += 1

            if filters.get("manufactured_by"):
                filter_conditions.append(f"manufactured_by ILIKE ${param_count}")
                filter_params.append(f"%{filters['manufactured_by']}%")
                param_count += 1
            elif filters.get("brand"):  # Backward compatibility
                filter_conditions.append(f"manufactured_by ILIKE ${param_count}")
                filter_params.append(f"%{filters['brand']}%")
                param_count += 1

            if filters.get("seller"):
                filter_conditions.append(f"seller ILIKE ${param_count}")
                filter_params.append(f"%{filters['seller']}%")
                param_count += 1

        # Build WHERE clause
        where_clause = " AND ".join(filter_conditions)

        # Get product suggestions using focused search with ranking
        suggestions_query = f"""
            SELECT
                *,
                ts_rank(to_tsvector('english', search_text), to_tsquery('english', $1)) as rank
            FROM products
            WHERE {where_clause}
            ORDER BY rank DESC, id ASC
            LIMIT ${param_count}
        """

        filter_params.append(limit)
        records = await conn.fetch(suggestions_query, *filter_params)

        # Convert to Product objects
        products = [Product.from_record(record) for record in records]
        return products
    
    @staticmethod
    async def get_popular_searches(conn: Connection, limit: int = 10) -> List[Dict[str, Any]]:
        """Get popular search terms based on product categories and brands."""
        query = """
            SELECT category, COUNT(*) as product_count
            FROM (
                SELECT maincat as category FROM products WHERE maincat IS NOT NULL
                UNION ALL
                SELECT brand as category FROM products WHERE brand IS NOT NULL
            ) categories
            GROUP BY category
            ORDER BY product_count DESC
            LIMIT $1
        """
        
        records = await conn.fetch(query, limit)
        return [{"term": record["category"], "count": record["product_count"]} for record in records]

# ProfiDent Database Setup Guide

This guide provides multiple options for setting up PostgreSQL with pgvector extension for the ProfiDent application.

## Option 1: Docker Setup (Recommended)

### Prerequisites
- Docker Desktop installed and running
- Docker Compose available

### Quick Start
```bash
# Start development database
docker-compose -f docker-compose.dev.yml up -d

# Check if services are running
docker-compose -f docker-compose.dev.yml ps

# View logs
docker-compose -f docker-compose.dev.yml logs postgres
```

### Development Environment Details
- **PostgreSQL**: Available at `localhost:5433`
- **Redis**: Available at `localhost:6380`
- **pgAdmin**: Available at `http://localhost:5050`
  - Email: `<EMAIL>`
  - Password: `admin123`

### Database Connection Details
```
Host: localhost
Port: 5433
Database: profident_dev
Username: profident_user
Password: profident_dev_password
```

### Connection String
```
postgresql+asyncpg://profident_user:profident_dev_password@localhost:5433/profident_dev
```

## Option 2: Manual PostgreSQL Installation

### Windows Installation

1. **Download PostgreSQL**
   - Visit: https://www.postgresql.org/download/windows/
   - Download PostgreSQL 16 installer
   - Run installer as Administrator

2. **Install pgvector Extension**
   ```bash
   # Option A: Using pre-built binaries (if available)
   # Download from: https://github.com/pgvector/pgvector/releases
   
   # Option B: Using chocolatey (as Administrator)
   choco install postgresql --version=16.0.0
   
   # Option C: Manual compilation (advanced)
   # Follow: https://github.com/pgvector/pgvector#installation
   ```

3. **Configure PostgreSQL**
   ```sql
   -- Connect to PostgreSQL as superuser
   psql -U postgres
   
   -- Create database and user
   CREATE DATABASE profident;
   CREATE USER profident_user WITH PASSWORD 'profident_password';
   GRANT ALL PRIVILEGES ON DATABASE profident TO profident_user;
   
   -- Connect to profident database
   \c profident
   
   -- Enable extensions
   CREATE EXTENSION vector;
   CREATE EXTENSION pg_trgm;
   CREATE EXTENSION btree_gin;
   ```

### macOS Installation
```bash
# Using Homebrew
brew install postgresql@16
brew install pgvector

# Start PostgreSQL
brew services start postgresql@16

# Create database
createdb profident
psql profident -c "CREATE EXTENSION vector;"
```

### Linux Installation
```bash
# Ubuntu/Debian
sudo apt update
sudo apt install postgresql-16 postgresql-16-pgvector

# CentOS/RHEL
sudo yum install postgresql16-server postgresql16-pgvector

# Start PostgreSQL
sudo systemctl start postgresql
sudo systemctl enable postgresql
```

## Option 3: Cloud Database Services

### Supabase (Recommended for Production)
1. Create account at https://supabase.com
2. Create new project
3. pgvector is pre-installed
4. Use connection string provided

### AWS RDS with pgvector
1. Create RDS PostgreSQL 16 instance
2. Install pgvector extension manually
3. Configure security groups

### Google Cloud SQL
1. Create PostgreSQL 16 instance
2. Enable pgvector extension
3. Configure firewall rules

## Database Schema Initialization

After setting up PostgreSQL, initialize the schema:

```bash
# Using Docker
docker-compose -f docker-compose.dev.yml exec postgres psql -U profident_user -d profident_dev -f /scripts/init-db.sql

# Using local PostgreSQL
psql -U profident_user -d profident -f scripts/init-db.sql
```

## Verification

### Test Database Connection
```bash
# Test connection
psql -U profident_user -d profident_dev -h localhost -p 5433

# Verify extensions
SELECT * FROM pg_extension WHERE extname IN ('vector', 'pg_trgm', 'btree_gin');

# Check tables
\dt

# Verify indexes
\di
```

### Test pgvector Extension
```sql
-- Test vector operations
SELECT '[1,2,3]'::vector <-> '[4,5,6]'::vector AS distance;

-- Should return a numeric distance value
```

## Environment Configuration

Create `.env` file in backend directory:
```bash
# Database
DATABASE_URL=postgresql+asyncpg://profident_user:profident_dev_password@localhost:5433/profident_dev
POSTGRES_USER=profident_user
POSTGRES_PASSWORD=profident_dev_password
POSTGRES_DB=profident_dev
POSTGRES_HOST=localhost
POSTGRES_PORT=5433

# Redis
REDIS_URL=redis://localhost:6380

# Authentication
SECRET_KEY=your-secret-key-here
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

# API Configuration
API_V1_STR=/api/v1
PROJECT_NAME=ProfiDent API
```

## Troubleshooting

### Common Issues

1. **Permission Denied**
   ```bash
   # Fix PostgreSQL permissions
   sudo chown -R postgres:postgres /var/lib/postgresql/
   ```

2. **pgvector Not Found**
   ```sql
   -- Check available extensions
   SELECT * FROM pg_available_extensions WHERE name LIKE '%vector%';
   
   -- Install if missing
   CREATE EXTENSION vector;
   ```

3. **Connection Refused**
   ```bash
   # Check if PostgreSQL is running
   sudo systemctl status postgresql
   
   # Check port availability
   netstat -an | grep 5432
   ```

4. **Docker Issues**
   ```bash
   # Reset Docker containers
   docker-compose -f docker-compose.dev.yml down -v
   docker-compose -f docker-compose.dev.yml up -d
   ```

### Performance Tuning

Add to `postgresql.conf`:
```
# Memory settings
shared_buffers = 256MB
effective_cache_size = 1GB
work_mem = 4MB

# Full-text search settings
default_text_search_config = 'english'

# Connection settings
max_connections = 100
```

## Next Steps

After database setup:
1. Run data migration script to import 339K products
2. Test search functionality
3. Set up backend API connection
4. Configure connection pooling

## Support

For issues with database setup:
1. Check Docker logs: `docker-compose -f docker-compose.dev.yml logs`
2. Verify PostgreSQL status: `docker-compose -f docker-compose.dev.yml ps`
3. Test connection manually using provided connection details

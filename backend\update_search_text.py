#!/usr/bin/env python3
"""
Update search_text field for existing products to include MFR field.
This script ensures all products have MFR included in their search_text for enhanced search functionality.
"""

import asyncio
import asyncpg
import os
import sys
from typing import List, Dict, Any
import time

# Add the app directory to the path to import config
sys.path.append(os.path.join(os.path.dirname(__file__), 'app'))

try:
    from config import settings, get_database_url_sync
except ImportError:
    # Fallback to environment variable
    settings = None

class SearchTextUpdater:
    """Updates search_text field for existing products to include MFR."""

    def __init__(self):
        if settings:
            # Use the configured database URL
            self.db_url = get_database_url_sync()
        else:
            # Fallback to environment variable
            self.db_url = os.getenv('DATABASE_URL')
            if not self.db_url:
                # Use default development database URL
                self.db_url = "postgresql://profident_user:profident_dev_password@localhost:5433/profident_dev"

        print(f"🔗 Using database URL: {self.db_url[:50]}...")

    async def get_connection(self):
        """Get database connection."""
        return await asyncpg.connect(self.db_url)
    
    async def update_search_text_for_products(self):
        """Update search_text field for all products to focus on MFR and name only."""
        print("🔄 Starting focused search_text update for existing products...")
        print("🎯 New search will focus ONLY on MFR and name fields for optimal performance")

        conn = await self.get_connection()
        try:
            # Get all products that need updating
            products = await conn.fetch("""
                SELECT id, mfr, name, search_text
                FROM products
                ORDER BY id
            """)

            print(f"📊 Found {len(products)} products to potentially update")

            updated_count = 0
            batch_size = 1000

            for i in range(0, len(products), batch_size):
                batch = products[i:i + batch_size]

                # Prepare batch update
                update_data = []
                for product in batch:
                    # Create focused search_text with ONLY MFR and name
                    parts = [
                        product['mfr'] or '',   # Primary product identifier
                        product['name'] or ''   # Product name/title
                    ]
                    new_search_text = ' | '.join(filter(None, parts))
                    
                    # Only update if search_text has changed
                    if new_search_text != (product['search_text'] or ''):
                        update_data.append((new_search_text, product['id']))
                
                if update_data:
                    # Batch update
                    await conn.executemany("""
                        UPDATE products 
                        SET search_text = $1, updated_at = NOW()
                        WHERE id = $2
                    """, update_data)
                    
                    updated_count += len(update_data)
                    print(f"✅ Updated batch {i//batch_size + 1}: {len(update_data)} products")
                else:
                    print(f"⏭️ Skipped batch {i//batch_size + 1}: no updates needed")
            
            print(f"🎉 Successfully updated {updated_count} products with enhanced search_text")
            
            # Verify the updates
            sample_products = await conn.fetch("""
                SELECT id, mfr, name, search_text
                FROM products
                WHERE mfr IS NOT NULL AND mfr != ''
                LIMIT 5
            """)
            
            print("\n📋 Sample updated products:")
            for product in sample_products:
                print(f"  ID: {product['id']}")
                print(f"  MFR: {product['mfr']}")
                print(f"  Name: {product['name']}")
                print(f"  Search Text: {product['search_text'][:100]}...")
                print()
        
        finally:
            await conn.close()
    
    async def test_search_with_mfr(self):
        """Test search functionality with MFR field."""
        print("🔍 Testing search functionality with MFR field...")
        
        conn = await self.get_connection()
        try:
            # Get a sample MFR to test with
            sample_mfr = await conn.fetchval("""
                SELECT mfr FROM products 
                WHERE mfr IS NOT NULL AND mfr != ''
                LIMIT 1
            """)
            
            if not sample_mfr:
                print("⚠️ No MFR values found to test with")
                return
            
            print(f"🧪 Testing search with MFR: {sample_mfr}")
            
            # Test full-text search
            start_time = time.time()
            results = await conn.fetch("""
                SELECT id, mfr, name, seller
                FROM products 
                WHERE to_tsvector('english', search_text) @@ to_tsquery('english', $1)
                ORDER BY ts_rank(to_tsvector('english', search_text), to_tsquery('english', $1)) DESC
                LIMIT 10
            """, sample_mfr)
            
            search_time = (time.time() - start_time) * 1000
            
            print(f"✅ Found {len(results)} results in {search_time:.2f}ms")
            print("📋 Sample results:")
            for result in results[:3]:
                print(f"  - MFR: {result['mfr']}, Name: {result['name']}, Seller: {result['seller']}")
        
        finally:
            await conn.close()

async def main():
    """Main function to update search_text fields."""
    updater = SearchTextUpdater()
    
    try:
        await updater.update_search_text_for_products()
        await updater.test_search_with_mfr()
        print("\n🎉 Search text update completed successfully!")
        
    except Exception as e:
        print(f"❌ Error updating search text: {e}")
        raise

if __name__ == "__main__":
    asyncio.run(main())

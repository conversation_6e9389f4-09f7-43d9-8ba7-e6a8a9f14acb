#!/usr/bin/env python3
"""
Test Redis connection functionality.
"""

import asyncio
import sys
import os

# Add the app directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'app'))

async def test_redis_connection():
    """Test Redis connection."""
    print("🔍 Testing Redis Connection")
    print("=" * 40)
    
    try:
        from app.database import get_redis_client
        
        print("1. Getting Redis client...")
        redis_client = await get_redis_client()
        print("✅ Redis client obtained")
        
        print("2. Testing basic Redis operations...")
        
        # Test set/get
        await redis_client.set("test_key", "test_value", ex=10)
        value = await redis_client.get("test_key")
        print(f"✅ Set/Get test: {value}")
        
        # Test delete
        await redis_client.delete("test_key")
        value = await redis_client.get("test_key")
        print(f"✅ Delete test: {value} (should be None)")
        
        print("✅ Redis connection test passed!")
        return True
        
    except Exception as e:
        print(f"❌ Redis connection test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_blacklist_functions():
    """Test blacklist functions."""
    print("\n🔍 Testing Blacklist Functions")
    print("=" * 40)
    
    try:
        from app.utils.security import blacklist_token, is_token_blacklisted, get_token_id
        from app.database import get_redis_client
        
        redis_client = await get_redis_client()
        
        # Test token
        test_token = "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJzdWIiOiJ0ZXN0IiwiZXhwIjoxNjQwOTk1MjAwfQ.test"
        
        print("1. Testing token ID generation...")
        token_id = get_token_id(test_token)
        print(f"✅ Token ID: {token_id}")
        
        print("2. Testing token blacklisting...")
        success = await blacklist_token(redis_client, test_token)
        print(f"✅ Blacklist result: {success}")
        
        print("3. Testing blacklist checking...")
        is_blacklisted = await is_token_blacklisted(redis_client, test_token)
        print(f"✅ Is blacklisted: {is_blacklisted}")
        
        print("4. Cleaning up...")
        token_id = get_token_id(test_token)
        await redis_client.delete(f"blacklist:token:{token_id}")
        
        print("✅ Blacklist functions test passed!")
        return True
        
    except Exception as e:
        print(f"❌ Blacklist functions test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """Run all Redis tests."""
    print("🧪 Redis and Blacklist Test Suite")
    print("=" * 50)
    
    tests = [
        ("Redis Connection", test_redis_connection),
        ("Blacklist Functions", test_blacklist_functions),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n🔍 Running: {test_name}")
        try:
            if await test_func():
                passed += 1
                print(f"✅ {test_name}: PASSED")
            else:
                print(f"❌ {test_name}: FAILED")
        except Exception as e:
            print(f"💥 {test_name}: ERROR - {e}")
    
    print("\n" + "=" * 50)
    print("📊 REDIS TEST SUMMARY")
    print("=" * 50)
    print(f"✅ Passed: {passed}/{total}")
    print(f"❌ Failed: {total - passed}/{total}")
    
    if passed == total:
        print("\n🎉 All Redis tests passed!")
        return True
    else:
        print("\n⚠️ Some tests failed. Please review the results above.")
        return False

if __name__ == "__main__":
    success = asyncio.run(main())
    exit(0 if success else 1)

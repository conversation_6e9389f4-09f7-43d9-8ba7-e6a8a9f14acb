"""
Permission database models and operations for RBAC system
"""

import uuid
from datetime import datetime
from typing import Optional, List
from asyncpg import Connection


class Permission:
    """Permission model for RBAC system."""
    
    def __init__(
        self,
        id: str = None,
        name: str = None,
        resource: str = None,
        action: str = None,
        description: str = None,
        is_system_permission: bool = False,
        created_at: datetime = None
    ):
        self.id = id or str(uuid.uuid4())
        self.name = name
        self.resource = resource
        self.action = action
        self.description = description
        self.is_system_permission = is_system_permission
        self.created_at = created_at
    
    @classmethod
    def from_record(cls, record) -> "Permission":
        """Create Permission instance from database record."""
        if not record:
            return None
        
        return cls(
            id=str(record["id"]),
            name=record["name"],
            resource=record["resource"],
            action=record["action"],
            description=record["description"],
            is_system_permission=record["is_system_permission"],
            created_at=record["created_at"]
        )
    
    def to_dict(self) -> dict:
        """Convert permission to dictionary."""
        return {
            "id": self.id,
            "name": self.name,
            "resource": self.resource,
            "action": self.action,
            "description": self.description,
            "is_system_permission": self.is_system_permission,
            "created_at": self.created_at.isoformat() if self.created_at else None,
        }


class PermissionRepository:
    """Permission database operations."""
    
    @staticmethod
    async def create_permission(
        conn: Connection,
        name: str,
        resource: str,
        action: str,
        description: str = None,
        is_system_permission: bool = False
    ) -> Permission:
        """Create a new permission."""
        permission_id = str(uuid.uuid4())
        
        query = """
            INSERT INTO permissions (id, name, resource, action, description, is_system_permission)
            VALUES ($1, $2, $3, $4, $5, $6)
            RETURNING *
        """
        
        record = await conn.fetchrow(
            query, 
            permission_id, 
            name, 
            resource, 
            action, 
            description, 
            is_system_permission
        )
        return Permission.from_record(record)
    
    @staticmethod
    async def get_permission_by_id(conn: Connection, permission_id: str) -> Optional[Permission]:
        """Get permission by ID."""
        query = "SELECT * FROM permissions WHERE id = $1"
        record = await conn.fetchrow(query, permission_id)
        return Permission.from_record(record)
    
    @staticmethod
    async def get_permission_by_name(conn: Connection, name: str) -> Optional[Permission]:
        """Get permission by name."""
        query = "SELECT * FROM permissions WHERE name = $1"
        record = await conn.fetchrow(query, name)
        return Permission.from_record(record)
    
    @staticmethod
    async def get_permission_by_resource_action(
        conn: Connection, 
        resource: str, 
        action: str
    ) -> Optional[Permission]:
        """Get permission by resource and action."""
        query = "SELECT * FROM permissions WHERE resource = $1 AND action = $2"
        record = await conn.fetchrow(query, resource, action)
        return Permission.from_record(record)
    
    @staticmethod
    async def update_permission(
        conn: Connection,
        permission_id: str,
        description: str = None
    ) -> Optional[Permission]:
        """Update permission information (only description can be updated)."""
        if description is None:
            return await PermissionRepository.get_permission_by_id(conn, permission_id)
        
        query = """
            UPDATE permissions 
            SET description = $1
            WHERE id = $2
            RETURNING *
        """
        
        record = await conn.fetchrow(query, description, permission_id)
        return Permission.from_record(record)
    
    @staticmethod
    async def delete_permission(conn: Connection, permission_id: str) -> bool:
        """Delete permission (only non-system permissions can be deleted)."""
        # Prevent deletion of system permissions
        check_query = "SELECT is_system_permission FROM permissions WHERE id = $1"
        is_system = await conn.fetchval(check_query, permission_id)
        
        if is_system:
            return False  # Cannot delete system permissions
        
        query = "DELETE FROM permissions WHERE id = $1 AND is_system_permission = FALSE"
        result = await conn.execute(query, permission_id)
        return "DELETE 1" in result
    
    @staticmethod
    async def list_permissions(
        conn: Connection,
        resource: str = None,
        action: str = None,
        limit: int = 100,
        offset: int = 0
    ) -> List[Permission]:
        """List permissions with optional filtering."""
        where_conditions = []
        params = []
        param_count = 1
        
        if resource:
            where_conditions.append(f"resource = ${param_count}")
            params.append(resource)
            param_count += 1
        
        if action:
            where_conditions.append(f"action = ${param_count}")
            params.append(action)
            param_count += 1
        
        where_clause = ""
        if where_conditions:
            where_clause = "WHERE " + " AND ".join(where_conditions)
        
        params.extend([limit, offset])
        
        query = f"""
            SELECT * FROM permissions 
            {where_clause}
            ORDER BY resource, action
            LIMIT ${param_count} OFFSET ${param_count + 1}
        """
        
        records = await conn.fetch(query, *params)
        return [Permission.from_record(record) for record in records]
    
    @staticmethod
    async def count_permissions(
        conn: Connection,
        resource: str = None,
        action: str = None
    ) -> int:
        """Count total permissions with optional filtering."""
        where_conditions = []
        params = []
        param_count = 1
        
        if resource:
            where_conditions.append(f"resource = ${param_count}")
            params.append(resource)
            param_count += 1
        
        if action:
            where_conditions.append(f"action = ${param_count}")
            params.append(action)
            param_count += 1
        
        where_clause = ""
        if where_conditions:
            where_clause = "WHERE " + " AND ".join(where_conditions)
        
        query = f"SELECT COUNT(*) FROM permissions {where_clause}"
        return await conn.fetchval(query, *params)
    
    @staticmethod
    async def permission_exists(conn: Connection, name: str) -> bool:
        """Check if permission name already exists."""
        query = "SELECT EXISTS(SELECT 1 FROM permissions WHERE name = $1)"
        return await conn.fetchval(query, name)
    
    @staticmethod
    async def get_permissions_by_resource(conn: Connection, resource: str) -> List[Permission]:
        """Get all permissions for a specific resource."""
        query = "SELECT * FROM permissions WHERE resource = $1 ORDER BY action"
        records = await conn.fetch(query, resource)
        return [Permission.from_record(record) for record in records]
    
    @staticmethod
    async def get_available_resources(conn: Connection) -> List[str]:
        """Get list of all available resources."""
        query = "SELECT DISTINCT resource FROM permissions ORDER BY resource"
        records = await conn.fetch(query)
        return [record['resource'] for record in records]
    
    @staticmethod
    async def get_available_actions(conn: Connection) -> List[str]:
        """Get list of all available actions."""
        query = "SELECT DISTINCT action FROM permissions ORDER BY action"
        records = await conn.fetch(query)
        return [record['action'] for record in records]
    
    @staticmethod
    async def get_permission_roles_count(conn: Connection, permission_id: str) -> int:
        """Get count of roles that have this permission."""
        query = """
            SELECT COUNT(*)
            FROM role_permissions
            WHERE permission_id = $1 AND is_active = TRUE
        """
        return await conn.fetchval(query, permission_id)

    @staticmethod
    async def get_permissions_paginated(
        conn: Connection,
        page: int = 1,
        page_size: int = 20,
        resource: str = None,
        action: str = None,
        search: str = None
    ) -> List[Permission]:
        """Get permissions with pagination and search."""
        where_conditions = []
        params = []
        param_count = 1

        if resource:
            where_conditions.append(f"resource = ${param_count}")
            params.append(resource)
            param_count += 1

        if action:
            where_conditions.append(f"action = ${param_count}")
            params.append(action)
            param_count += 1

        if search:
            where_conditions.append(f"(name ILIKE ${param_count} OR description ILIKE ${param_count})")
            params.append(f"%{search}%")
            param_count += 1

        where_clause = ""
        if where_conditions:
            where_clause = "WHERE " + " AND ".join(where_conditions)

        offset = (page - 1) * page_size
        params.extend([page_size, offset])

        query = f"""
            SELECT * FROM permissions
            {where_clause}
            ORDER BY resource, action, name
            LIMIT ${param_count} OFFSET ${param_count + 1}
        """

        records = await conn.fetch(query, *params)
        return [Permission.from_record(record) for record in records]

    @staticmethod
    async def get_permissions_count(
        conn: Connection,
        resource: str = None,
        action: str = None,
        search: str = None
    ) -> int:
        """Count permissions with filtering and search."""
        where_conditions = []
        params = []
        param_count = 1

        if resource:
            where_conditions.append(f"resource = ${param_count}")
            params.append(resource)
            param_count += 1

        if action:
            where_conditions.append(f"action = ${param_count}")
            params.append(action)
            param_count += 1

        if search:
            where_conditions.append(f"(name ILIKE ${param_count} OR description ILIKE ${param_count})")
            params.append(f"%{search}%")
            param_count += 1

        where_clause = ""
        if where_conditions:
            where_clause = "WHERE " + " AND ".join(where_conditions)

        query = f"SELECT COUNT(*) FROM permissions {where_clause}"
        return await conn.fetchval(query, *params)

    @staticmethod
    async def get_permissions_summary(conn: Connection) -> dict:
        """Get permission summary statistics."""
        query = """
            SELECT
                COUNT(*) as total_permissions,
                COUNT(CASE WHEN is_system_permission = TRUE THEN 1 END) as system_permissions,
                COUNT(CASE WHEN is_system_permission = FALSE THEN 1 END) as custom_permissions,
                COUNT(DISTINCT resource) as total_resources,
                COUNT(DISTINCT action) as total_actions
            FROM permissions
        """

        record = await conn.fetchrow(query)
        return {
            "total_permissions": record["total_permissions"],
            "system_permissions": record["system_permissions"],
            "custom_permissions": record["custom_permissions"],
            "total_resources": record["total_resources"],
            "total_actions": record["total_actions"]
        }

    @staticmethod
    async def assign_permission_to_role(
        conn: Connection,
        role_id: str,
        permission_id: str,
        granted_by: str
    ) -> "RolePermissionAssignment":
        """Assign a permission to a role."""
        from .role import RolePermissionAssignment
        import uuid

        # Check if assignment already exists
        existing_query = """
            SELECT id FROM role_permissions
            WHERE role_id = $1 AND permission_id = $2 AND is_active = TRUE
        """
        existing = await conn.fetchval(existing_query, role_id, permission_id)

        if existing:
            # Return existing assignment
            return await RolePermissionAssignment.get_by_id(conn, existing)

        # Create new assignment
        assignment_id = str(uuid.uuid4())
        query = """
            INSERT INTO role_permissions (id, role_id, permission_id, granted_by, granted_at)
            VALUES ($1, $2, $3, $4, NOW())
            RETURNING *
        """

        record = await conn.fetchrow(query, assignment_id, role_id, permission_id, granted_by)
        return RolePermissionAssignment.from_record(record)

    @staticmethod
    async def bulk_assign_permissions_to_role(
        conn: Connection,
        role_id: str,
        permission_ids: List[str],
        granted_by: str
    ) -> dict:
        """Assign multiple permissions to a role."""
        results = {
            "assigned": 0,
            "skipped": 0,
            "errors": []
        }

        for permission_id in permission_ids:
            try:
                # Check if assignment already exists
                existing_query = """
                    SELECT id FROM role_permissions
                    WHERE role_id = $1 AND permission_id = $2 AND is_active = TRUE
                """
                existing = await conn.fetchval(existing_query, role_id, permission_id)

                if existing:
                    results["skipped"] += 1
                    continue

                # Create new assignment
                import uuid
                assignment_id = str(uuid.uuid4())
                query = """
                    INSERT INTO role_permissions (id, role_id, permission_id, granted_by, granted_at)
                    VALUES ($1, $2, $3, $4, NOW())
                """

                await conn.execute(query, assignment_id, role_id, permission_id, granted_by)
                results["assigned"] += 1

            except Exception as e:
                results["errors"].append(f"Permission {permission_id}: {str(e)}")

        return results

    @staticmethod
    async def get_permission_statistics(conn: Connection) -> dict:
        """Get permission statistics for admin dashboard."""
        stats_query = """
            SELECT
                COUNT(*) as total_permissions,
                COUNT(DISTINCT resource) as unique_resources,
                COUNT(DISTINCT action) as unique_actions,
                COUNT(*) FILTER (WHERE is_system_permission = TRUE) as system_permissions,
                COUNT(*) FILTER (WHERE is_system_permission = FALSE) as custom_permissions,
                COUNT(*) FILTER (WHERE created_at >= NOW() - INTERVAL '30 days') as new_permissions_last_30_days
            FROM permissions
        """

        result = await conn.fetchrow(stats_query)
        return dict(result) if result else {}

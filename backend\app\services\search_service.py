"""
Search service for fast full-text product search
Optimized for instant search performance using PostgreSQL
"""

import time
import json
from typing import List, Dict, Any, Optional, Tuple
from asyncpg import Connection
import redis.asyncio as redis

try:
    from ..models.product import Product, ProductRepository
    from ..schemas.product import (
        ProductSearchRequest, ProductSearchResponse, ProductSearchResult,
        ProductSearchFilters, ProductSuggestion, ProductSuggestionResponse, PopularSearchResponse
    )
    from ..config import settings
except ImportError:
    from models.product import Product, ProductRepository
    from schemas.product import (
        ProductSearchRequest, ProductSearchResponse, ProductSearchResult,
        ProductSearchFilters, ProductSuggestion, ProductSuggestionResponse, PopularSearchResponse
    )
    from config import settings


class SearchService:
    """High-performance search service with caching and optimization."""
    
    @staticmethod
    async def search_products(
        conn: Connection,
        redis_client: redis.Redis,
        search_request: ProductSearchRequest
    ) -> ProductSearchResponse:
        """
        Main product search with caching and performance optimization.
        Provides instant search results using PostgreSQL full-text search.
        """
        start_time = time.time()
        
        # Create cache key
        cache_key = SearchService._create_cache_key(search_request)
        
        # Try to get from cache first
        try:
            cached_result = await redis_client.get(cache_key)
            if cached_result:
                cached_data = json.loads(cached_result)
                cached_data["search_time_ms"] = (time.time() - start_time) * 1000
                cached_data["message"] = "Search completed successfully (cached)"
                return ProductSearchResponse(**cached_data)
        except Exception:
            # Cache miss or error, continue with database search
            pass
        
        # Prepare filters
        filters = {}
        if search_request.category:
            filters["category"] = search_request.category
        if search_request.brand:
            filters["brand"] = search_request.brand
        if search_request.seller:
            filters["seller"] = search_request.seller
        if search_request.manufactured_by:
            filters["manufactured_by"] = search_request.manufactured_by
        
        # Perform search based on type
        if search_request.search_type == "fulltext":
            products, total = await ProductRepository.search_products_fulltext(
                conn, search_request.q, search_request.limit, search_request.offset, filters
            )
        elif search_request.search_type == "similarity":
            products, total = await ProductRepository.search_products_similarity(
                conn, search_request.q, search_request.limit, search_request.offset, filters
            )
        else:  # hybrid
            # Try fulltext first, fallback to similarity if no results
            products, total = await ProductRepository.search_products_fulltext(
                conn, search_request.q, search_request.limit, search_request.offset, filters
            )
            if total == 0:
                products, total = await ProductRepository.search_products_similarity(
                    conn, search_request.q, search_request.limit, search_request.offset, filters
                )
        
        # Convert to response format
        search_results = []
        for product in products:
            result = ProductSearchResult(
                id=product.id,
                mfr=product.mfr,
                name=product.name,
                url=product.url,
                maincat=product.maincat,
                brand=product.brand,
                manufactured_by=product.manufactured_by,
                category=product.category,
                seller=product.seller,
                price=product.price,
                search_text=product.search_text
            )
            search_results.append(result)
        
        # Calculate pagination
        total_pages = (total + search_request.limit - 1) // search_request.limit
        current_page = (search_request.offset // search_request.limit) + 1
        
        pagination = {
            "total": total,
            "page": current_page,
            "per_page": search_request.limit,
            "total_pages": total_pages,
            "has_next": current_page < total_pages,
            "has_prev": current_page > 1
        }
        
        search_time_ms = (time.time() - start_time) * 1000
        
        # Create response
        response_data = {
            "success": True,
            "message": "Search completed successfully",
            "query": search_request.q,
            "search_type": search_request.search_type,
            "total": total,
            "results": [result.dict() for result in search_results],
            "pagination": pagination,
            "filters_applied": filters if filters else None,
            "search_time_ms": search_time_ms
        }
        
        # Cache the result (without search_time_ms)
        try:
            cache_data = response_data.copy()
            cache_data.pop("search_time_ms", None)
            await redis_client.setex(
                cache_key, 
                settings.SEARCH_CACHE_TTL, 
                json.dumps(cache_data, default=str)
            )
        except Exception:
            # Cache error, continue without caching
            pass
        
        return ProductSearchResponse(**response_data)
    
    @staticmethod
    async def get_search_suggestions(
        conn: Connection,
        redis_client: redis.Redis,
        query: str,
        limit: int = 10,
        filters: Optional[Dict[str, Any]] = None
    ) -> ProductSuggestionResponse:
        """Get search suggestions with caching and optional filtering."""
        # Include filters in cache key for filtered suggestions
        filter_key = ""
        if filters:
            filter_items = sorted(filters.items())
            filter_key = ":" + ":".join([f"{k}={v}" for k, v in filter_items if v])

        cache_key = f"suggestions:{query.lower()}:{limit}{filter_key}"

        # Try cache first
        try:
            cached_result = await redis_client.get(cache_key)
            if cached_result:
                cached_suggestions = json.loads(cached_result)
                # Convert cached data back to ProductSuggestion objects
                suggestions = [ProductSuggestion(**suggestion) for suggestion in cached_suggestions]
                return ProductSuggestionResponse(
                    query=query,
                    suggestions=suggestions
                )
        except Exception:
            pass

        # Get product suggestions from database with filters
        product_suggestions = await ProductRepository.get_search_suggestions(conn, query, limit, filters)

        # Convert Product objects to ProductSuggestion objects
        suggestions = []
        for product in product_suggestions:
            suggestion = ProductSuggestion(
                id=product.id,
                mfr=product.mfr,
                name=product.name,
                seller=product.seller,
                price=product.price,
                url=product.url,
                maincat=product.maincat,
                brand=product.brand,
                manufactured_by=product.manufactured_by,
                category=product.category
            )
            suggestions.append(suggestion)

        # Cache suggestions (shorter cache time for filtered suggestions)
        cache_time = 300 if not filters else 180  # 5 minutes vs 3 minutes
        try:
            # Convert ProductSuggestion objects to dict for caching
            suggestions_dict = [suggestion.dict() for suggestion in suggestions]
            await redis_client.setex(cache_key, cache_time, json.dumps(suggestions_dict, default=str))
        except Exception:
            pass

        return ProductSuggestionResponse(
            query=query,
            suggestions=suggestions
        )
    
    @staticmethod
    async def get_popular_searches(
        conn: Connection,
        redis_client: redis.Redis,
        limit: int = 10
    ) -> PopularSearchResponse:
        """Get popular search terms with caching."""
        cache_key = f"popular_searches:{limit}"
        
        # Try cache first
        try:
            cached_result = await redis_client.get(cache_key)
            if cached_result:
                popular_searches = json.loads(cached_result)
                return PopularSearchResponse(popular_searches=popular_searches)
        except Exception:
            pass
        
        # Get from database
        popular_searches = await ProductRepository.get_popular_searches(conn, limit)
        
        # Cache popular searches
        try:
            await redis_client.setex(cache_key, 3600, json.dumps(popular_searches))  # 1 hour
        except Exception:
            pass
        
        return PopularSearchResponse(popular_searches=popular_searches)
    
    @staticmethod
    def _create_cache_key(search_request: ProductSearchRequest) -> str:
        """Create cache key for search request."""
        key_parts = [
            f"search:{search_request.q.lower()}",
            f"type:{search_request.search_type}",
            f"limit:{search_request.limit}",
            f"offset:{search_request.offset}"
        ]
        
        if search_request.category:
            key_parts.append(f"cat:{search_request.category}")
        if search_request.brand:
            key_parts.append(f"brand:{search_request.brand}")
        if search_request.seller:
            key_parts.append(f"seller:{search_request.seller}")
        if search_request.manufactured_by:
            key_parts.append(f"mfr:{search_request.manufactured_by}")
        
        return ":".join(key_parts)
    
    @staticmethod
    async def clear_search_cache(redis_client: redis.Redis, pattern: str = "search:*") -> int:
        """Clear search cache by pattern."""
        try:
            keys = await redis_client.keys(pattern)
            if keys:
                return await redis_client.delete(*keys)
            return 0
        except Exception:
            return 0
    
    @staticmethod
    async def get_search_stats(
        conn: Connection,
        redis_client: redis.Redis
    ) -> Dict[str, Any]:
        """Get search performance statistics."""
        try:
            # Get database stats
            total_products = await conn.fetchval("SELECT COUNT(*) FROM products")
            
            # Get unique values for filters
            categories = await conn.fetchval(
                "SELECT COUNT(DISTINCT maincat) FROM products WHERE maincat IS NOT NULL"
            )
            brands = await conn.fetchval(
                "SELECT COUNT(DISTINCT brand) FROM products WHERE brand IS NOT NULL"
            )
            sellers = await conn.fetchval(
                "SELECT COUNT(DISTINCT seller) FROM products"
            )
            
            # Get cache stats
            cache_keys = await redis_client.keys("search:*")
            cache_size = len(cache_keys) if cache_keys else 0
            
            return {
                "total_products": total_products,
                "unique_categories": categories,
                "unique_brands": brands,
                "unique_sellers": sellers,
                "cache_entries": cache_size,
                "search_types_available": ["fulltext", "similarity", "hybrid"]
            }
        
        except Exception as e:
            return {
                "error": str(e),
                "total_products": 0,
                "cache_entries": 0
            }

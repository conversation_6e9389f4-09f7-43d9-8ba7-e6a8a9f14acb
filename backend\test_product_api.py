#!/usr/bin/env python3
"""
Test script for product matching API
Verifies product matching and price comparison functionality
"""

import asyncio
import sys
from pathlib import Path

# Add the app directory to Python path
sys.path.insert(0, str(Path(__file__).parent / "app"))

from app.database import DatabaseManager
from app.services.product_service import ProductService
from app.schemas.product import ProductMatchRequest


async def test_product_service_import():
    """Test product service imports."""
    print("🔍 Testing product service imports...")
    
    try:
        from app.services.product_service import ProductService
        from app.schemas.product import ProductMatchRequest, ProductMatchResponse
        
        print("✅ Product service imports successful")
        return True
        
    except Exception as e:
        print(f"❌ Product service import failed: {e}")
        return False


async def test_product_matching_functions():
    """Test product matching database functions."""
    print("\n🔍 Testing product matching functions...")
    
    try:
        db = DatabaseManager()
        await db.initialize()
        
        async with db.get_connection() as conn:
            # Test if we have any products
            product_count = await conn.fetchval("SELECT COUNT(*) FROM products")
            print(f"✅ Database connected, products available: {product_count}")
            
            if product_count == 0:
                print("⚠️ No products in database - product matching tests will be limited")
                return True
            
            # Get a sample MFR code for testing
            sample_mfr = await conn.fetchval("SELECT mfr FROM products LIMIT 1")
            if sample_mfr:
                print(f"✅ Sample MFR code for testing: {sample_mfr}")
                
                # Test get_products_by_mfr function
                from app.models.product import ProductRepository
                products, total = await ProductRepository.get_products_by_mfr(
                    conn, sample_mfr, limit=10, offset=0
                )
                print(f"✅ Product matching function working: {total} products for MFR '{sample_mfr}'")
                
                if products:
                    sellers = list(set(p.seller for p in products))
                    print(f"✅ Found products from {len(sellers)} sellers: {sellers}")
            else:
                print("⚠️ No MFR codes found in database")
        
        await db.close()
        return True
        
    except Exception as e:
        print(f"❌ Product matching functions test failed: {e}")
        return False


async def test_product_service_methods():
    """Test product service methods."""
    print("\n🔍 Testing product service methods...")
    
    try:
        db = DatabaseManager()
        await db.initialize()
        
        # Mock Redis client for testing
        class MockRedis:
            async def get(self, key):
                return None
            async def setex(self, key, ttl, value):
                return True
            async def ping(self):
                return True
        
        redis_client = MockRedis()
        
        async with db.get_connection() as conn:
            # Get a sample MFR code
            sample_mfr = await conn.fetchval("SELECT mfr FROM products LIMIT 1")
            
            if sample_mfr:
                # Test product matching service
                match_request = ProductMatchRequest(
                    mfr=sample_mfr,
                    limit=10,
                    offset=0
                )
                
                result = await ProductService.find_product_matches(conn, redis_client, match_request)
                print(f"✅ Product matching service working: {result.total} matches for MFR '{sample_mfr}'")
                print(f"✅ Response format: mfr='{result.mfr}', total={result.total}")
                
                # Test price comparison service
                comparison = await ProductService.get_price_comparison(conn, redis_client, sample_mfr)
                print(f"✅ Price comparison service working: found={comparison.get('found', False)}")
                
                if comparison.get('found') and comparison.get('sellers_with_prices', 0) > 0:
                    stats = comparison.get('price_stats', {})
                    print(f"✅ Price stats: min=${stats.get('min_price', 0):.2f}, max=${stats.get('max_price', 0):.2f}")
            
            # Test categories service
            categories_result = await ProductService.get_product_categories(conn, redis_client, 10)
            print(f"✅ Categories service working: {len(categories_result.categories)} categories")
            
            # Test brands service
            brands_result = await ProductService.get_product_brands(conn, redis_client, 10)
            print(f"✅ Brands service working: {len(brands_result.brands)} brands")
            
            # Test sellers service
            sellers_result = await ProductService.get_product_sellers(conn, redis_client, 10)
            print(f"✅ Sellers service working: {len(sellers_result.sellers)} sellers")
        
        await db.close()
        return True
        
    except Exception as e:
        print(f"❌ Product service methods test failed: {e}")
        return False


async def test_product_api_endpoints():
    """Test product API endpoints."""
    print("\n🔍 Testing product API endpoints...")
    
    try:
        from fastapi.testclient import TestClient
        from app.main import app
        
        with TestClient(app) as client:
            # Test product match endpoint structure
            response = client.get("/api/v1/products/match/TEST123")
            if response.status_code in [200, 401, 404]:  # 401 without auth, 404 if no product
                print("✅ Product match endpoint structure working")
            else:
                print(f"❌ Product match endpoint failed: {response.status_code}")
                return False
            
            # Test categories endpoint
            response = client.get("/api/v1/products/categories/")
            if response.status_code in [200, 503]:  # 503 if database not ready
                print("✅ Categories endpoint structure working")
            else:
                print(f"❌ Categories endpoint failed: {response.status_code}")
                return False
            
            # Test brands endpoint
            response = client.get("/api/v1/products/brands/")
            if response.status_code in [200, 503]:
                print("✅ Brands endpoint structure working")
            else:
                print(f"❌ Brands endpoint failed: {response.status_code}")
                return False
            
            # Test sellers endpoint
            response = client.get("/api/v1/products/sellers/")
            if response.status_code in [200, 503]:
                print("✅ Sellers endpoint structure working")
            else:
                print(f"❌ Sellers endpoint failed: {response.status_code}")
                return False
            
            # Test stats endpoint
            response = client.get("/api/v1/products/stats/")
            if response.status_code in [200, 503]:
                print("✅ Stats endpoint structure working")
            else:
                print(f"❌ Stats endpoint failed: {response.status_code}")
                return False
            
            # Test health endpoint
            response = client.get("/api/v1/products/health/")
            if response.status_code in [200, 503]:
                print("✅ Products health endpoint working")
            else:
                print(f"❌ Products health endpoint failed: {response.status_code}")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ Product API endpoints test failed: {e}")
        return False


async def test_price_comparison_logic():
    """Test price comparison logic."""
    print("\n🔍 Testing price comparison logic...")
    
    try:
        db = DatabaseManager()
        await db.initialize()
        
        class MockRedis:
            async def get(self, key):
                return None
            async def setex(self, key, ttl, value):
                return True
        
        redis_client = MockRedis()
        
        async with db.get_connection() as conn:
            # Find an MFR code with multiple sellers
            mfr_with_multiple_sellers = await conn.fetchval("""
                SELECT mfr 
                FROM products 
                GROUP BY mfr 
                HAVING COUNT(DISTINCT seller) > 1 
                LIMIT 1
            """)
            
            if mfr_with_multiple_sellers:
                print(f"✅ Found MFR with multiple sellers: {mfr_with_multiple_sellers}")
                
                # Test price comparison
                comparison = await ProductService.get_price_comparison(
                    conn, redis_client, mfr_with_multiple_sellers
                )
                
                print(f"✅ Price comparison completed: found={comparison.get('found', False)}")
                print(f"✅ Total sellers: {comparison.get('total_sellers', 0)}")
                print(f"✅ Sellers with prices: {comparison.get('sellers_with_prices', 0)}")
                
                if comparison.get('price_stats'):
                    stats = comparison['price_stats']
                    print(f"✅ Price range: ${stats.get('min_price', 0):.2f} - ${stats.get('max_price', 0):.2f}")
                    print(f"✅ Potential savings: ${stats.get('savings_potential', 0):.2f}")
            else:
                print("⚠️ No products with multiple sellers found - price comparison limited")
        
        await db.close()
        return True
        
    except Exception as e:
        print(f"❌ Price comparison logic test failed: {e}")
        return False


async def cleanup():
    """Clean up resources."""
    print("\n🧹 Cleaning up...")
    
    try:
        # No specific cleanup needed for product tests
        print("✅ Cleanup completed")
    except Exception as e:
        print(f"⚠️ Cleanup warning: {e}")


async def main():
    """Run all product matching API tests."""
    print("🚀 Starting ProfiDent Product Matching API Tests\n")
    print("Testing product matching and price comparison functionality")
    print("=" * 60)
    
    tests = [
        ("Product Service Import", test_product_service_import),
        ("Product Matching Functions", test_product_matching_functions),
        ("Product Service Methods", test_product_service_methods),
        ("Product API Endpoints", test_product_api_endpoints),
        ("Price Comparison Logic", test_price_comparison_logic),
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = await test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} failed with exception: {e}")
            results.append((test_name, False))
    
    # Cleanup
    await cleanup()
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 TEST SUMMARY")
    print("=" * 60)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{status} - {test_name}")
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All product matching API tests passed!")
        print("✅ Product matching and price comparison API is working correctly")
        print("💰 Ready for cross-seller price comparison functionality")
        return True
    else:
        print("❌ Some tests failed. Check product matching API configuration.")
        return False


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)

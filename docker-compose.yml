version: '3.8'

services:
  # PostgreSQL Database with pgvector extension
  postgres:
    image: pgvector/pgvector:pg16
    container_name: profident_postgres
    environment:
      POSTGRES_DB: profident
      POSTGRES_USER: profident_user
      POSTGRES_PASSWORD: profident_password
      POSTGRES_HOST_AUTH_METHOD: trust
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./scripts/init-db.sql:/docker-entrypoint-initdb.d/init-db.sql
    networks:
      - profident_network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U profident_user -d profident"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Redis for caching
  redis:
    image: redis:7-alpine
    container_name: profident_redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - profident_network
    restart: unless-stopped
    command: redis-server --appendonly yes
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5

  # FastAPI Backend (will be added later)
  # backend:
  #   build:
  #     context: ./backend
  #     dockerfile: Dockerfile
  #   container_name: profident_backend
  #   environment:
  #     DATABASE_URL: postgresql+asyncpg://profident_user:profident_password@postgres:5432/profident
  #     REDIS_URL: redis://redis:6379
  #   ports:
  #     - "8000:8000"
  #   depends_on:
  #     postgres:
  #       condition: service_healthy
  #     redis:
  #       condition: service_healthy
  #   networks:
  #     - profident_network
  #   restart: unless-stopped

  # React Frontend (will be added later)
  # frontend:
  #   build:
  #     context: ./frontend
  #     dockerfile: Dockerfile
  #   container_name: profident_frontend
  #   ports:
  #     - "3000:3000"
  #   environment:
  #     VITE_API_BASE_URL: http://localhost:8000
  #   depends_on:
  #     - backend
  #   networks:
  #     - profident_network
  #   restart: unless-stopped

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local

networks:
  profident_network:
    driver: bridge

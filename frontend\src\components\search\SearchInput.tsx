import { useState, useRef, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { Search, X, Clock, TrendingUp, Plus } from "lucide-react";
import { useSearchStore } from "../../stores/searchStore";
import {
  useSearchSuggestions,
  usePopularSearches,
} from "../../hooks/useSearch";
import {
  useShoppingLists,
  useQuickAddProduct,
} from "../../hooks/useShoppingLists";
import { useShoppingListStore } from "../../stores/shoppingListStore";
import { debounce } from "../../lib/utils";
import { cn } from "../../lib/utils";
import { ProductSuggestion } from "../../types";

const SearchInput = () => {
  const navigate = useNavigate();
  const inputRef = useRef<HTMLInputElement>(null);
  const [localQuery, setLocalQuery] = useState("");
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [selectedIndex, setSelectedIndex] = useState(-1);

  const {
    query,
    setQuery,
    recentSearches,
    addRecentSearch,
    suggestions,
    popularSearches,
    filters,
  } = useSearchStore();

  // Prepare filters for suggestions API
  const suggestionFilters = {
    category: filters.category,
    manufactured_by: filters.manufactured_by,
    brand: filters.brand, // For backward compatibility
    seller: filters.seller,
  };

  // Fetch suggestions when user types (with current filters applied)
  const { data: suggestionsData } = useSearchSuggestions(
    localQuery,
    localQuery.length >= 2 && showSuggestions,
    suggestionFilters
  );

  // Fetch popular searches on mount
  const { data: popularData } = usePopularSearches();

  // Get shopping lists for "Add to List" functionality
  const { data: shoppingListsData } = useShoppingLists(1, 10);
  const shoppingLists = shoppingListsData?.data?.items || [];

  // Get shopping list store for animation state
  const { animationInProgress } = useShoppingListStore();

  // Use React Query hook for quick-add functionality
  const quickAddMutation = useQuickAddProduct();

  // Debounced search function
  const debouncedSearch = debounce((searchQuery: string) => {
    if (searchQuery.trim()) {
      setQuery(searchQuery);
      addRecentSearch(searchQuery);
      navigate("/search");
      setShowSuggestions(false);
    }
  }, 300);

  // Handle input change
  const handleInputChange = (value: string) => {
    setLocalQuery(value);
    setSelectedIndex(-1);
    setShowSuggestions(true);
  };

  // Handle form submission
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (localQuery.trim()) {
      debouncedSearch(localQuery);
    }
  };

  // Handle suggestion selection
  const handleSuggestionSelect = (suggestion: string | ProductSuggestion) => {
    // Handle both string suggestions (recent/popular) and ProductSuggestion objects
    const searchQuery =
      typeof suggestion === "string" ? suggestion : suggestion.name;
    setLocalQuery(searchQuery);
    debouncedSearch(searchQuery);
  };

  // Handle adding suggestion to shopping list
  const handleAddToList = async (
    suggestion: ProductSuggestion,
    event: React.MouseEvent
  ) => {
    event.stopPropagation(); // Prevent suggestion selection

    try {
      // Use the React Query mutation for quick-add functionality
      await quickAddMutation.mutateAsync({
        productId: suggestion.id,
        quantity: 1,
        product: suggestion,
      });

      // Show success notification
      window.dispatchEvent(
        new CustomEvent("shopping-list-success", {
          detail: {
            message: `Added "${suggestion.name}" to shopping list`,
            product: suggestion,
          },
        })
      );

      // Clear the search input after successful addition
      setLocalQuery("");
      setQuery("");
      setSuggestions([]);
      setShowSuggestions(false);
    } catch (error) {
      console.error("Failed to add product to shopping list:", error);

      // Show error notification
      window.dispatchEvent(
        new CustomEvent("shopping-list-error", {
          detail: {
            message: `Failed to add "${suggestion.name}" to shopping list`,
            error,
          },
        })
      );
    }
  };

  // Handle keyboard navigation
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (!showSuggestions) return;

    const allSuggestions: (string | ProductSuggestion)[] = [
      ...recentSearches.slice(0, 3),
      ...suggestions.slice(0, 5),
      ...popularSearches.slice(0, 3),
    ];

    switch (e.key) {
      case "ArrowDown":
        e.preventDefault();
        setSelectedIndex((prev) =>
          prev < allSuggestions.length - 1 ? prev + 1 : prev
        );
        break;
      case "ArrowUp":
        e.preventDefault();
        setSelectedIndex((prev) => (prev > -1 ? prev - 1 : -1));
        break;
      case "Enter":
        e.preventDefault();
        if (selectedIndex >= 0 && allSuggestions[selectedIndex]) {
          handleSuggestionSelect(allSuggestions[selectedIndex]);
        } else {
          handleSubmit(e);
        }
        break;
      case "Escape":
        setShowSuggestions(false);
        setSelectedIndex(-1);
        inputRef.current?.blur();
        break;
    }
  };

  // Close suggestions when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        inputRef.current &&
        !inputRef.current.contains(event.target as Node)
      ) {
        setShowSuggestions(false);
        setSelectedIndex(-1);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, []);

  // Initialize with current query
  useEffect(() => {
    if (query && !localQuery) {
      setLocalQuery(query);
    }
  }, [query]); // Remove localQuery from dependencies to prevent infinite loop

  const allSuggestions: (string | ProductSuggestion)[] = [
    ...recentSearches.slice(0, 5),
    ...suggestions.slice(0, 20),
    ...popularSearches.slice(0, 5),
  ];

  return (
    <div className="relative w-full max-w-2xl">
      <form onSubmit={handleSubmit} className="relative">
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
          <input
            ref={inputRef}
            type="text"
            value={localQuery}
            onChange={(e) => handleInputChange(e.target.value)}
            onKeyDown={handleKeyDown}
            onFocus={() => setShowSuggestions(true)}
            placeholder="Search dental products..."
            className="w-full pl-10 pr-10 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent bg-white shadow-sm"
          />
          {localQuery && (
            <button
              type="button"
              onClick={() => {
                setLocalQuery("");
                setShowSuggestions(false);
                inputRef.current?.focus();
              }}
              className="absolute right-3 top-1/2 transform -translate-y-1/2 p-1 text-gray-400 hover:text-gray-600"
            >
              <X className="h-4 w-4" />
            </button>
          )}
        </div>
      </form>

      {/* Suggestions dropdown */}
      {showSuggestions && (
        <div className="absolute top-full left-0 right-0 mt-1 bg-white border border-gray-200 rounded-lg shadow-xl z-[9999] max-h-80 overflow-y-auto">
          {/* Recent searches */}
          {recentSearches.length > 0 && (
            <div className="p-2">
              <div className="flex items-center px-3 py-2 text-xs font-medium text-gray-500 uppercase tracking-wide">
                <Clock className="h-3 w-3 mr-1" />
                Recent
              </div>
              {recentSearches.slice(0, 5).map((search, index) => (
                <button
                  key={`recent-${index}`}
                  onClick={() => handleSuggestionSelect(search)}
                  className={cn(
                    "w-full text-left px-3 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded-md",
                    selectedIndex === index && "bg-primary-50 text-primary-700"
                  )}
                >
                  {search}
                </button>
              ))}
            </div>
          )}

          {/* Search suggestions */}
          {suggestions.length > 0 && (
            <div className="p-2 border-t border-gray-100">
              <div className="flex items-center px-3 py-2 text-xs font-medium text-gray-500 uppercase tracking-wide">
                <Search className="h-3 w-3 mr-1" />
                Suggestions
              </div>
              {suggestions.slice(0, 20).map((suggestion, index) => {
                const adjustedIndex = recentSearches.slice(0, 5).length + index;
                return (
                  <div
                    key={`suggestion-${index}`}
                    className={cn(
                      "flex items-center justify-between px-3 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded-md group",
                      selectedIndex === adjustedIndex &&
                        "bg-primary-50 text-primary-700"
                    )}
                  >
                    <button
                      onClick={() => handleSuggestionSelect(suggestion)}
                      className="flex-1 text-left"
                    >
                      <span className="font-medium">{suggestion.name}</span>
                    </button>
                    <button
                      onClick={(e) => handleAddToList(suggestion, e)}
                      className="opacity-0 group-hover:opacity-100 ml-2 p-1 text-gray-400 hover:text-primary-600 hover:bg-primary-100 rounded transition-all"
                      title="Add to shopping list"
                    >
                      <Plus className="h-4 w-4" />
                    </button>
                  </div>
                );
              })}
            </div>
          )}

          {/* Popular searches */}
          {popularSearches.length > 0 && !localQuery && (
            <div className="p-2 border-t border-gray-100">
              <div className="flex items-center px-3 py-2 text-xs font-medium text-gray-500 uppercase tracking-wide">
                <TrendingUp className="h-3 w-3 mr-1" />
                Popular
              </div>
              {popularSearches.slice(0, 5).map((search, index) => {
                const adjustedIndex =
                  recentSearches.slice(0, 5).length +
                  suggestions.slice(0, 20).length +
                  index;
                return (
                  <button
                    key={`popular-${index}`}
                    onClick={() => handleSuggestionSelect(search)}
                    className={cn(
                      "w-full text-left px-3 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded-md",
                      selectedIndex === adjustedIndex &&
                        "bg-primary-50 text-primary-700"
                    )}
                  >
                    {search}
                  </button>
                );
              })}
            </div>
          )}

          {/* No suggestions */}
          {allSuggestions.length === 0 && localQuery.length >= 2 && (
            <div className="p-4 text-center text-gray-500 text-sm">
              No suggestions found
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default SearchInput;

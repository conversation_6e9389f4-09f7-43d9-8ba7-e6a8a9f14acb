import React, { useState, useEffect } from 'react';
import { X, Plus, Trash2, Shield, Users, CheckCircle, AlertCircle } from 'lucide-react';
import { apiClient } from '../../lib/api';

interface Role {
  id: string;
  name: string;
  display_name: string;
  description: string;
  is_active: boolean;
}

interface UserRole {
  id: string;
  role_id: string;
  role_name: string;
  role_display_name: string;
  assigned_at: string;
  assigned_by: string;
  is_active: boolean;
}

interface User {
  id: string;
  email: string;
  full_name: string;
  is_active: boolean;
  is_superuser: boolean;
  roles?: string[];
}

interface RoleAssignmentModalProps {
  user: User;
  isOpen: boolean;
  onClose: () => void;
  onRoleAssigned: () => void;
}

const RoleAssignmentModal: React.FC<RoleAssignmentModalProps> = ({
  user,
  isOpen,
  onClose,
  onRoleAssigned
}) => {
  const [availableRoles, setAvailableRoles] = useState<Role[]>([]);
  const [userRoles, setUserRoles] = useState<UserRole[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [selectedRoleId, setSelectedRoleId] = useState<string>('');

  useEffect(() => {
    if (isOpen) {
      fetchRoles();
      fetchUserRoles();
    }
  }, [isOpen, user.id]);

  const fetchRoles = async () => {
    try {
      setLoading(true);
      const roles = await apiClient.get('/roles');
      setAvailableRoles(Array.isArray(roles) ? roles : roles.data || []);
    } catch (error) {
      console.error('Failed to fetch roles:', error);
      // Use mock data if API fails
      setAvailableRoles([
        {
          id: '1',
          name: 'admin',
          display_name: 'Administrator',
          description: 'Full administrative access',
          is_active: true
        },
        {
          id: '2',
          name: 'user_manager',
          display_name: 'User Manager',
          description: 'Can manage users and their roles',
          is_active: true
        },
        {
          id: '3',
          name: 'content_manager',
          display_name: 'Content Manager',
          description: 'Can manage content and products',
          is_active: true
        },
        {
          id: '4',
          name: 'viewer',
          display_name: 'Viewer',
          description: 'Read-only access to most features',
          is_active: true
        }
      ]);
    } finally {
      setLoading(false);
    }
  };

  const fetchUserRoles = async () => {
    try {
      const userRolesData = await apiClient.get(`/user-roles/user/${user.id}`);
      setUserRoles(Array.isArray(userRolesData) ? userRolesData : userRolesData.data || []);
    } catch (error) {
      console.error('Failed to fetch user roles:', error);
      setUserRoles([]);
    }
  };

  const assignRole = async () => {
    if (!selectedRoleId) {
      setError('Please select a role to assign');
      return;
    }

    try {
      setLoading(true);
      setError(null);
      
      await apiClient.post('/user-roles/assign', {
        user_id: user.id,
        role_id: selectedRoleId
      });

      setSuccess('Role assigned successfully');
      setSelectedRoleId('');
      await fetchUserRoles();
      onRoleAssigned();
      
      setTimeout(() => setSuccess(null), 3000);
    } catch (error: any) {
      console.error('Failed to assign role:', error);
      setError(error.response?.data?.detail || 'Failed to assign role');
    } finally {
      setLoading(false);
    }
  };

  const revokeRole = async (roleId: string) => {
    try {
      setLoading(true);
      setError(null);
      
      await apiClient.delete(`/user-roles/revoke/${user.id}/${roleId}`);
      
      setSuccess('Role revoked successfully');
      await fetchUserRoles();
      onRoleAssigned();
      
      setTimeout(() => setSuccess(null), 3000);
    } catch (error: any) {
      console.error('Failed to revoke role:', error);
      setError(error.response?.data?.detail || 'Failed to revoke role');
    } finally {
      setLoading(false);
    }
  };

  const getAvailableRolesForAssignment = () => {
    const assignedRoleIds = userRoles.filter(ur => ur.is_active).map(ur => ur.role_id);
    return availableRoles.filter(role => 
      role.is_active && !assignedRoleIds.includes(role.id)
    );
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b">
          <div className="flex items-center space-x-3">
            <Shield className="h-6 w-6 text-blue-600" />
            <div>
              <h2 className="text-xl font-semibold text-gray-900">Manage User Roles</h2>
              <p className="text-sm text-gray-600">{user.full_name} ({user.email})</p>
            </div>
          </div>
          <button
            onClick={onClose}
            className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
          >
            <X className="h-5 w-5 text-gray-500" />
          </button>
        </div>

        {/* Content */}
        <div className="p-6 space-y-6">
          {/* Status Messages */}
          {error && (
            <div className="flex items-center space-x-2 p-3 bg-red-50 border border-red-200 rounded-lg">
              <AlertCircle className="h-5 w-5 text-red-600" />
              <span className="text-red-700">{error}</span>
            </div>
          )}
          
          {success && (
            <div className="flex items-center space-x-2 p-3 bg-green-50 border border-green-200 rounded-lg">
              <CheckCircle className="h-5 w-5 text-green-600" />
              <span className="text-green-700">{success}</span>
            </div>
          )}

          {/* Current Roles */}
          <div>
            <h3 className="text-lg font-medium text-gray-900 mb-3 flex items-center">
              <Users className="h-5 w-5 mr-2" />
              Current Roles
            </h3>
            
            {userRoles.filter(ur => ur.is_active).length === 0 ? (
              <p className="text-gray-500 italic">No roles assigned</p>
            ) : (
              <div className="space-y-2">
                {userRoles.filter(ur => ur.is_active).map((userRole) => (
                  <div
                    key={userRole.id}
                    className="flex items-center justify-between p-3 bg-blue-50 border border-blue-200 rounded-lg"
                  >
                    <div>
                      <span className="font-medium text-blue-900">
                        {userRole.role_display_name || userRole.role_name}
                      </span>
                      <p className="text-sm text-blue-700">
                        Assigned on {new Date(userRole.assigned_at).toLocaleDateString()}
                      </p>
                    </div>
                    <button
                      onClick={() => revokeRole(userRole.role_id)}
                      disabled={loading}
                      className="p-2 text-red-600 hover:bg-red-100 rounded-lg transition-colors disabled:opacity-50"
                      title="Revoke role"
                    >
                      <Trash2 className="h-4 w-4" />
                    </button>
                  </div>
                ))}
              </div>
            )}
          </div>

          {/* Assign New Role */}
          <div>
            <h3 className="text-lg font-medium text-gray-900 mb-3 flex items-center">
              <Plus className="h-5 w-5 mr-2" />
              Assign New Role
            </h3>
            
            <div className="flex space-x-3">
              <select
                value={selectedRoleId}
                onChange={(e) => setSelectedRoleId(e.target.value)}
                className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                disabled={loading}
              >
                <option value="">Select a role...</option>
                {getAvailableRolesForAssignment().map((role) => (
                  <option key={role.id} value={role.id}>
                    {role.display_name} - {role.description}
                  </option>
                ))}
              </select>
              
              <button
                onClick={assignRole}
                disabled={loading || !selectedRoleId}
                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
              >
                {loading ? 'Assigning...' : 'Assign Role'}
              </button>
            </div>
            
            {getAvailableRolesForAssignment().length === 0 && (
              <p className="text-sm text-gray-500 mt-2">
                All available roles have been assigned to this user.
              </p>
            )}
          </div>
        </div>

        {/* Footer */}
        <div className="flex justify-end space-x-3 p-6 border-t bg-gray-50">
          <button
            onClick={onClose}
            className="px-4 py-2 text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
          >
            Close
          </button>
        </div>
      </div>
    </div>
  );
};

export default RoleAssignmentModal;

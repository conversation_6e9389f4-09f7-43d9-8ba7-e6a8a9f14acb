#!/usr/bin/env python3
"""
Simple test script for enhanced input validation in authentication endpoints.
"""

import asyncio
import aiohttp
import json

async def test_validation():
    """Test enhanced input validation."""
    print("🧪 Testing Enhanced Input Validation...")
    print("=" * 50)
    
    base_url = "http://localhost:8000"
    
    async with aiohttp.ClientSession() as session:
        # Test 1: Valid registration with enhanced validation
        print("\n1. Testing valid registration...")
        valid_data = {
            "email": "<EMAIL>",
            "password": "ValidPass123!",
            "full_name": "Valid User"
        }
        
        try:
            async with session.post(f"{base_url}/api/v1/auth/register", json=valid_data) as response:
                if response.status in [201, 400]:  # 400 if user already exists
                    print("✅ Valid registration handled correctly")
                else:
                    print(f"❌ Unexpected status: {response.status}")
                    print(await response.text())
        except Exception as e:
            print(f"❌ Error: {e}")
        
        # Test 2: Invalid email format
        print("\n2. Testing invalid email format...")
        invalid_email_data = {
            "email": "invalid-email",
            "password": "ValidPass123!",
            "full_name": "Test User"
        }
        
        try:
            async with session.post(f"{base_url}/api/v1/auth/register", json=invalid_email_data) as response:
                if response.status == 422:
                    print("✅ Invalid email correctly rejected")
                else:
                    print(f"❌ Expected 422, got {response.status}")
                    print(await response.text())
        except Exception as e:
            print(f"❌ Error: {e}")
        
        # Test 3: Weak password
        print("\n3. Testing weak password...")
        weak_password_data = {
            "email": "<EMAIL>",
            "password": "123",
            "full_name": "Test User"
        }
        
        try:
            async with session.post(f"{base_url}/api/v1/auth/register", json=weak_password_data) as response:
                if response.status == 422:
                    print("✅ Weak password correctly rejected")
                else:
                    print(f"❌ Expected 422, got {response.status}")
                    print(await response.text())
        except Exception as e:
            print(f"❌ Error: {e}")
        
        # Test 4: Login with invalid email
        print("\n4. Testing login with invalid email...")
        invalid_login_data = {
            "email": "invalid-email",
            "password": "ValidPass123!"
        }
        
        try:
            async with session.post(f"{base_url}/api/v1/auth/login", json=invalid_login_data) as response:
                if response.status == 401:
                    print("✅ Invalid login email correctly rejected")
                else:
                    print(f"❌ Expected 401, got {response.status}")
                    print(await response.text())
        except Exception as e:
            print(f"❌ Error: {e}")
        
        # Test 5: Login with valid credentials (if user exists)
        print("\n5. Testing login with valid credentials...")
        valid_login_data = {
            "email": "<EMAIL>",
            "password": "ValidPass123!"
        }
        
        try:
            async with session.post(f"{base_url}/api/v1/auth/login", json=valid_login_data) as response:
                if response.status in [200, 401]:  # 401 if user doesn't exist
                    print("✅ Valid login handled correctly")
                else:
                    print(f"❌ Unexpected status: {response.status}")
                    print(await response.text())
        except Exception as e:
            print(f"❌ Error: {e}")
    
    print("\n✅ Enhanced input validation tests completed!")

if __name__ == "__main__":
    asyncio.run(test_validation())

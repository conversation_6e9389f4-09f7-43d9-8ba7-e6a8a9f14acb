#!/usr/bin/env python3
"""
Account lockout utilities for preventing brute force attacks.

Provides account lockout functionality based on failed login attempts
with configurable thresholds and lockout durations.
"""

import time
import json
from typing import Optional, Dict, Any
from dataclasses import dataclass
from ..config import settings
import redis


@dataclass
class LockoutConfig:
    """Configuration for account lockout."""
    max_attempts: int = 5  # Maximum failed attempts before lockout
    lockout_duration: int = 900  # Lockout duration in seconds (15 minutes)
    attempt_window: int = 300  # Time window for counting attempts in seconds (5 minutes)
    progressive_lockout: bool = True  # Enable progressive lockout durations


@dataclass
class LockoutStatus:
    """Account lockout status information."""
    is_locked: bool
    attempts_count: int
    lockout_expires_at: Optional[int]
    next_attempt_allowed_at: Optional[int]
    remaining_attempts: int


class AccountLockoutManager:
    """Manages account lockout functionality."""
    
    def __init__(self, redis_client: redis.Redis, config: LockoutConfig = None):
        """
        Initialize account lockout manager.
        
        Args:
            redis_client: Redis client for storing lockout data
            config: Lockout configuration
        """
        self.redis = redis_client
        self.config = config or LockoutConfig()
        self.key_prefix = "lockout:"
        self.attempts_prefix = "attempts:"
    
    def _get_lockout_key(self, identifier: str) -> str:
        """Get Redis key for lockout data."""
        return f"{self.key_prefix}{identifier}"
    
    def _get_attempts_key(self, identifier: str) -> str:
        """Get Redis key for attempts data."""
        return f"{self.attempts_prefix}{identifier}"
    
    def _calculate_lockout_duration(self, attempt_count: int) -> int:
        """
        Calculate lockout duration based on attempt count.
        
        Args:
            attempt_count: Number of failed attempts
            
        Returns:
            Lockout duration in seconds
        """
        if not self.config.progressive_lockout:
            return self.config.lockout_duration
        
        # Progressive lockout: increase duration with each lockout
        lockout_multiplier = min(attempt_count // self.config.max_attempts, 5)
        return self.config.lockout_duration * (2 ** lockout_multiplier)
    
    async def record_failed_attempt(self, identifier: str) -> LockoutStatus:
        """
        Record a failed login attempt.
        
        Args:
            identifier: User identifier (email, user_id, or IP address)
            
        Returns:
            Current lockout status
        """
        current_time = int(time.time())
        attempts_key = self._get_attempts_key(identifier)
        lockout_key = self._get_lockout_key(identifier)
        
        try:
            # Get current attempts data
            attempts_data = await self.redis.get(attempts_key)
            if attempts_data:
                attempts_info = json.loads(attempts_data)
                attempts_count = attempts_info.get("count", 0)
                first_attempt_time = attempts_info.get("first_attempt", current_time)
            else:
                attempts_count = 0
                first_attempt_time = current_time
            
            # Check if we're within the attempt window
            if current_time - first_attempt_time > self.config.attempt_window:
                # Reset attempts if outside window
                attempts_count = 0
                first_attempt_time = current_time
            
            # Increment attempt count
            attempts_count += 1
            
            # Store updated attempts data
            attempts_info = {
                "count": attempts_count,
                "first_attempt": first_attempt_time,
                "last_attempt": current_time
            }
            
            await self.redis.setex(
                attempts_key,
                self.config.attempt_window,
                json.dumps(attempts_info)
            )
            
            # Check if account should be locked
            if attempts_count >= self.config.max_attempts:
                lockout_duration = self._calculate_lockout_duration(attempts_count)
                lockout_expires_at = current_time + lockout_duration
                
                # Store lockout data
                lockout_info = {
                    "locked_at": current_time,
                    "expires_at": lockout_expires_at,
                    "attempts_count": attempts_count,
                    "reason": "too_many_failed_attempts"
                }
                
                await self.redis.setex(
                    lockout_key,
                    lockout_duration,
                    json.dumps(lockout_info)
                )
                
                return LockoutStatus(
                    is_locked=True,
                    attempts_count=attempts_count,
                    lockout_expires_at=lockout_expires_at,
                    next_attempt_allowed_at=lockout_expires_at,
                    remaining_attempts=0
                )
            
            # Account not locked yet
            remaining_attempts = self.config.max_attempts - attempts_count
            
            return LockoutStatus(
                is_locked=False,
                attempts_count=attempts_count,
                lockout_expires_at=None,
                next_attempt_allowed_at=None,
                remaining_attempts=remaining_attempts
            )
            
        except Exception as e:
            # If Redis fails, don't block authentication but log the error
            print(f"Account lockout error: {e}")
            return LockoutStatus(
                is_locked=False,
                attempts_count=0,
                lockout_expires_at=None,
                next_attempt_allowed_at=None,
                remaining_attempts=self.config.max_attempts
            )
    
    async def check_lockout_status(self, identifier: str) -> LockoutStatus:
        """
        Check if an account is currently locked.
        
        Args:
            identifier: User identifier (email, user_id, or IP address)
            
        Returns:
            Current lockout status
        """
        current_time = int(time.time())
        lockout_key = self._get_lockout_key(identifier)
        attempts_key = self._get_attempts_key(identifier)
        
        try:
            # Check lockout status
            lockout_data = await self.redis.get(lockout_key)
            if lockout_data:
                lockout_info = json.loads(lockout_data)
                expires_at = lockout_info.get("expires_at", 0)

                if current_time < expires_at:
                    # Account is still locked
                    return LockoutStatus(
                        is_locked=True,
                        attempts_count=lockout_info.get("attempts_count", 0),
                        lockout_expires_at=expires_at,
                        next_attempt_allowed_at=expires_at,
                        remaining_attempts=0
                    )
                else:
                    # Lockout has expired, clean up
                    await self.redis.delete(lockout_key)
                    await self.redis.delete(attempts_key)
            
            # Get current attempts count
            attempts_data = await self.redis.get(attempts_key)
            if attempts_data:
                attempts_info = json.loads(attempts_data)
                attempts_count = attempts_info.get("count", 0)
                first_attempt_time = attempts_info.get("first_attempt", current_time)

                # Check if attempts are within window
                if current_time - first_attempt_time > self.config.attempt_window:
                    attempts_count = 0
                    await self.redis.delete(attempts_key)
            else:
                attempts_count = 0
            
            remaining_attempts = max(0, self.config.max_attempts - attempts_count)
            
            return LockoutStatus(
                is_locked=False,
                attempts_count=attempts_count,
                lockout_expires_at=None,
                next_attempt_allowed_at=None,
                remaining_attempts=remaining_attempts
            )
            
        except Exception as e:
            # If Redis fails, don't block authentication but log the error
            print(f"Account lockout check error: {e}")
            return LockoutStatus(
                is_locked=False,
                attempts_count=0,
                lockout_expires_at=None,
                next_attempt_allowed_at=None,
                remaining_attempts=self.config.max_attempts
            )
    
    async def clear_lockout(self, identifier: str) -> bool:
        """
        Clear lockout for an account (admin function).
        
        Args:
            identifier: User identifier to clear lockout for
            
        Returns:
            True if lockout was cleared, False otherwise
        """
        try:
            lockout_key = self._get_lockout_key(identifier)
            attempts_key = self._get_attempts_key(identifier)
            
            # Delete both lockout and attempts data
            deleted_count = await self.redis.delete(lockout_key, attempts_key)
            
            return deleted_count > 0
            
        except Exception as e:
            print(f"Clear lockout error: {e}")
            return False
    
    async def reset_attempts(self, identifier: str) -> bool:
        """
        Reset failed attempts count for successful login.
        
        Args:
            identifier: User identifier to reset attempts for
            
        Returns:
            True if attempts were reset, False otherwise
        """
        try:
            attempts_key = self._get_attempts_key(identifier)
            deleted_count = await self.redis.delete(attempts_key)
            
            return deleted_count > 0
            
        except Exception as e:
            print(f"Reset attempts error: {e}")
            return False


# Global lockout manager instance (will be initialized with Redis client)
lockout_manager: Optional[AccountLockoutManager] = None


def initialize_lockout_manager(redis_client: redis.Redis, config: LockoutConfig = None):
    """Initialize the global lockout manager."""
    global lockout_manager
    lockout_manager = AccountLockoutManager(redis_client, config)


def get_lockout_manager() -> AccountLockoutManager:
    """Get the global lockout manager instance."""
    if lockout_manager is None:
        raise RuntimeError("Account lockout manager not initialized")
    return lockout_manager

#!/usr/bin/env python3
"""
Test script to run RBAC migrations and verify database schema
"""

import asyncio
import asyncpg
import os
import sys
from pathlib import Path

# Add the app directory to Python path
sys.path.append(str(Path(__file__).parent))

from app.config import settings

async def run_rbac_migrations():
    """Run RBAC migrations and verify database schema."""
    
    print("🚀 Starting RBAC Migration Test")
    print("=" * 50)
    
    try:
        # Connect to database
        # Convert SQLAlchemy URL to asyncpg format
        db_url = settings.DATABASE_URL.replace("postgresql+asyncpg://", "postgresql://")
        print(f"📡 Connecting to database: {db_url}")
        conn = await asyncpg.connect(db_url)
        print("✅ Database connection successful")
        
        # Check existing tables before migration
        print("\n📋 Checking existing tables...")
        existing_tables = await conn.fetch("""
            SELECT table_name 
            FROM information_schema.tables 
            WHERE table_schema = 'public' 
            ORDER BY table_name
        """)
        
        print("Existing tables:")
        for row in existing_tables:
            print(f"  - {row['table_name']}")
        
        # Get list of RBAC migration files
        migrations_dir = Path("migrations")
        rbac_migrations = sorted([
            f for f in migrations_dir.glob("*.sql") 
            if f.name.startswith(('006_', '007_', '008_', '009_', '010_'))
        ])
        
        print(f"\n🔄 Found {len(rbac_migrations)} RBAC migration files")
        
        # Run each migration
        for migration_file in rbac_migrations:
            print(f"\n📝 Running migration: {migration_file.name}")
            
            with open(migration_file, 'r', encoding='utf-8') as f:
                migration_sql = f.read()
            
            try:
                await conn.execute(migration_sql)
                print(f"✅ Successfully executed {migration_file.name}")
            except Exception as e:
                print(f"❌ Error executing {migration_file.name}: {e}")
                # Continue with other migrations to see what works
                continue
        
        # Verify new tables were created
        print("\n🔍 Verifying new RBAC tables...")
        new_tables = await conn.fetch("""
            SELECT table_name 
            FROM information_schema.tables 
            WHERE table_schema = 'public' 
            AND table_name IN ('roles', 'permissions', 'user_roles', 'role_permissions')
            ORDER BY table_name
        """)
        
        expected_tables = {'roles', 'permissions', 'user_roles', 'role_permissions'}
        found_tables = {row['table_name'] for row in new_tables}
        
        print("RBAC tables found:")
        for table in found_tables:
            print(f"  ✅ {table}")
        
        missing_tables = expected_tables - found_tables
        if missing_tables:
            print("Missing tables:")
            for table in missing_tables:
                print(f"  ❌ {table}")
        
        # Test data integrity - check existing users
        print("\n👥 Checking existing user data...")
        users = await conn.fetch("SELECT id, email, is_superuser FROM users LIMIT 5")
        print(f"Found {len(users)} existing users (showing first 5):")
        for user in users:
            print(f"  - {user['email']} (superuser: {user['is_superuser']})")
        
        # Check if roles and permissions were seeded
        if 'roles' in found_tables:
            print("\n🎭 Checking seeded roles...")
            roles = await conn.fetch("SELECT name, display_name FROM roles ORDER BY name")
            print(f"Found {len(roles)} roles:")
            for role in roles:
                print(f"  - {role['name']}: {role['display_name']}")
        
        if 'permissions' in found_tables:
            print("\n🔐 Checking seeded permissions...")
            permissions = await conn.fetch("SELECT name, resource, action FROM permissions ORDER BY resource, action")
            print(f"Found {len(permissions)} permissions:")
            for perm in permissions[:10]:  # Show first 10
                print(f"  - {perm['name']} ({perm['resource']}.{perm['action']})")
            if len(permissions) > 10:
                print(f"  ... and {len(permissions) - 10} more")
        
        # Check user role assignments
        if 'user_roles' in found_tables:
            print("\n👤 Checking user role assignments...")
            user_roles = await conn.fetch("""
                SELECT u.email, r.name as role_name
                FROM user_roles ur
                JOIN users u ON ur.user_id = u.id
                JOIN roles r ON ur.role_id = r.id
                WHERE ur.is_active = TRUE
                ORDER BY u.email, r.name
            """)
            print(f"Found {len(user_roles)} active user role assignments:")
            for ur in user_roles[:10]:  # Show first 10
                print(f"  - {ur['email']} → {ur['role_name']}")
            if len(user_roles) > 10:
                print(f"  ... and {len(user_roles) - 10} more")
        
        # Test the new User model methods
        print("\n🧪 Testing User model with RBAC...")
        if users:
            test_user_id = users[0]['id']
            
            # Import and test the enhanced User model
            from app.models.user import UserRepository
            
            user_with_roles = await UserRepository.get_user_with_roles_and_permissions(conn, test_user_id)
            if user_with_roles:
                print(f"✅ User {user_with_roles.email} loaded with roles: {user_with_roles.roles}")
                print(f"   Permissions: {list(user_with_roles.permissions)[:5]}...")  # Show first 5
                print(f"   Has 'products.read' permission: {user_with_roles.has_permission('products.read')}")
                print(f"   Is admin: {user_with_roles.is_admin()}")
            else:
                print("❌ Failed to load user with roles and permissions")
        
        print("\n🎉 RBAC Migration Test Completed Successfully!")
        print("=" * 50)
        
        return True
        
    except Exception as e:
        print(f"\n❌ RBAC Migration Test Failed: {e}")
        import traceback
        traceback.print_exc()
        return False
        
    finally:
        if 'conn' in locals():
            await conn.close()
            print("📡 Database connection closed")

if __name__ == "__main__":
    success = asyncio.run(run_rbac_migrations())
    sys.exit(0 if success else 1)

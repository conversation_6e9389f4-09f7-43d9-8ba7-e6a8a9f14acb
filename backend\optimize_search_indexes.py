#!/usr/bin/env python3
"""
Search Index Optimization Script
Analyzes and optimizes PostgreSQL indexes for maximum full-text search performance
"""

import asyncio
import sys
import time
from pathlib import Path

# Add the app directory to Python path
sys.path.insert(0, str(Path(__file__).parent / "app"))

from app.database import DatabaseManager


class SearchIndexOptimizer:
    """Handles search index analysis and optimization."""
    
    def __init__(self):
        self.db = None
    
    async def initialize(self):
        """Initialize database connection."""
        self.db = DatabaseManager()
        await self.db.initialize()
        print("✅ Database connection initialized")
    
    async def close(self):
        """Close database connection."""
        if self.db:
            await self.db.close()
            print("✅ Database connection closed")
    
    async def analyze_current_indexes(self):
        """Analyze current index performance and usage."""
        print("🔍 Analyzing current search indexes...")
        
        async with self.db.get_connection() as conn:
            # Get index information
            indexes = await conn.fetch("""
                SELECT 
                    indexname,
                    indexdef,
                    pg_size_pretty(pg_relation_size(indexname::regclass)) as size
                FROM pg_indexes 
                WHERE tablename = 'products' 
                AND indexname LIKE '%search%'
                ORDER BY indexname
            """)
            
            print("\n📊 Current Search Indexes:")
            for idx in indexes:
                print(f"  - {idx['indexname']}: {idx['size']}")
                print(f"    Definition: {idx['indexdef']}")
            
            # Check index usage statistics
            stats = await conn.fetch("""
                SELECT
                    indexrelname as indexname,
                    idx_scan,
                    idx_tup_read,
                    idx_tup_fetch
                FROM pg_stat_user_indexes
                WHERE relname = 'products'
                AND indexrelname LIKE '%search%'
                ORDER BY idx_scan DESC
            """)
            
            print("\n📈 Index Usage Statistics:")
            for stat in stats:
                print(f"  - {stat['indexname']}: {stat['idx_scan']:,} scans, {stat['idx_tup_read']:,} tuples read")
    
    async def test_search_performance(self):
        """Test search performance with different query types."""
        print("\n⚡ Testing search performance...")
        
        test_queries = [
            ("Simple word", "dental"),
            ("Multiple words", "dental & equipment"),
            ("Phrase search", "dental & equipment & sterilization"),
            ("Brand search", "keystone"),
            ("Category search", "endodontics"),
            ("Complex query", "dental & (equipment | instrument)")
        ]
        
        async with self.db.get_connection() as conn:
            for query_name, query in test_queries:
                # Test full-text search
                start_time = time.time()
                results = await conn.fetch("""
                    SELECT COUNT(*) as count
                    FROM products
                    WHERE to_tsvector('english', search_text) @@ to_tsquery('english', $1)
                """, query)
                ft_time = (time.time() - start_time) * 1000
                ft_count = results[0]['count']
                
                # Test trigram search
                start_time = time.time()
                results = await conn.fetch("""
                    SELECT COUNT(*) as count
                    FROM products 
                    WHERE search_text % $1
                """, query.split()[0])  # Use first word for trigram
                trgm_time = (time.time() - start_time) * 1000
                trgm_count = results[0]['count']
                
                print(f"  {query_name:20} | FT: {ft_count:6,} results in {ft_time:6.2f}ms | Trigram: {trgm_count:6,} results in {trgm_time:6.2f}ms")
    
    async def create_additional_indexes(self):
        """Create additional optimized indexes for better performance."""
        print("\n🔧 Creating additional optimized indexes...")
        
        async with self.db.get_connection() as conn:
            # Check if additional indexes already exist
            existing_indexes = await conn.fetch("""
                SELECT indexname FROM pg_indexes 
                WHERE tablename = 'products'
            """)
            existing_names = [idx['indexname'] for idx in existing_indexes]
            
            # Additional indexes to create
            additional_indexes = [
                {
                    'name': 'idx_products_name_gin',
                    'sql': "CREATE INDEX CONCURRENTLY idx_products_name_gin ON products USING gin(to_tsvector('english', name))",
                    'description': 'GIN index on product name for fast name-based searches'
                },
                {
                    'name': 'idx_products_brand_maincat',
                    'sql': "CREATE INDEX CONCURRENTLY idx_products_brand_maincat ON products (brand, maincat)",
                    'description': 'Composite index for brand + category filtering'
                },
                {
                    'name': 'idx_products_seller_maincat',
                    'sql': "CREATE INDEX CONCURRENTLY idx_products_seller_maincat ON products (seller, maincat)",
                    'description': 'Composite index for seller + category filtering'
                },
                {
                    'name': 'idx_products_mfr_seller',
                    'sql': "CREATE INDEX CONCURRENTLY idx_products_mfr_seller ON products (mfr, seller) WHERE mfr IS NOT NULL",
                    'description': 'Partial index for MFR + seller (excluding NULL MFR)'
                }
            ]
            
            for idx in additional_indexes:
                if idx['name'] not in existing_names:
                    print(f"  Creating {idx['name']}...")
                    try:
                        await conn.execute(idx['sql'])
                        print(f"  ✅ {idx['name']}: {idx['description']}")
                    except Exception as e:
                        print(f"  ❌ Failed to create {idx['name']}: {e}")
                else:
                    print(f"  ✅ {idx['name']} already exists")
    
    async def optimize_existing_indexes(self):
        """Optimize existing indexes."""
        print("\n🔧 Optimizing existing indexes...")
        
        async with self.db.get_connection() as conn:
            # Update statistics
            print("  Updating table statistics...")
            await conn.execute("ANALYZE products")
            
            # Reindex search indexes for optimal performance
            search_indexes = [
                'idx_products_search_text_gin',
                'idx_products_search_text_trgm'
            ]
            
            for idx_name in search_indexes:
                print(f"  Reindexing {idx_name}...")
                try:
                    await conn.execute(f"REINDEX INDEX CONCURRENTLY {idx_name}")
                    print(f"  ✅ {idx_name} reindexed")
                except Exception as e:
                    print(f"  ⚠️ {idx_name} reindex warning: {e}")
    
    async def configure_search_settings(self):
        """Configure PostgreSQL settings for optimal search performance."""
        print("\n⚙️ Configuring search settings...")
        
        async with self.db.get_connection() as conn:
            # Check current settings
            settings_to_check = [
                'shared_preload_libraries',
                'default_text_search_config',
                'work_mem',
                'maintenance_work_mem'
            ]
            
            print("  Current PostgreSQL settings:")
            for setting in settings_to_check:
                try:
                    result = await conn.fetchval(f"SHOW {setting}")
                    print(f"    {setting}: {result}")
                except Exception as e:
                    print(f"    {setting}: Unable to check ({e})")
            
            # Set optimal search configuration
            try:
                await conn.execute("SET default_text_search_config = 'pg_catalog.english'")
                print("  ✅ Set default text search config to English")
            except Exception as e:
                print(f"  ⚠️ Could not set text search config: {e}")
    
    async def generate_search_performance_report(self):
        """Generate a comprehensive search performance report."""
        print("\n📊 Generating search performance report...")
        
        async with self.db.get_connection() as conn:
            # Database size information
            db_size = await conn.fetchval("""
                SELECT pg_size_pretty(pg_database_size('profident_dev'))
            """)
            
            table_size = await conn.fetchval("""
                SELECT pg_size_pretty(pg_total_relation_size('products'))
            """)
            
            index_sizes = await conn.fetch("""
                SELECT 
                    indexname,
                    pg_size_pretty(pg_relation_size(indexname::regclass)) as size
                FROM pg_indexes 
                WHERE tablename = 'products'
                ORDER BY pg_relation_size(indexname::regclass) DESC
            """)
            
            # Product statistics
            product_stats = await conn.fetchrow("""
                SELECT 
                    COUNT(*) as total_products,
                    COUNT(DISTINCT seller) as unique_sellers,
                    COUNT(DISTINCT brand) as unique_brands,
                    COUNT(DISTINCT maincat) as unique_categories,
                    COUNT(mfr) as products_with_mfr,
                    COUNT(*) - COUNT(mfr) as products_without_mfr
                FROM products
            """)
            
            print("\n" + "=" * 60)
            print("📊 SEARCH PERFORMANCE REPORT")
            print("=" * 60)
            print(f"Database size: {db_size}")
            print(f"Products table size: {table_size}")
            print(f"Total products: {product_stats['total_products']:,}")
            print(f"Products with MFR: {product_stats['products_with_mfr']:,}")
            print(f"Products without MFR: {product_stats['products_without_mfr']:,}")
            print(f"Unique sellers: {product_stats['unique_sellers']:,}")
            print(f"Unique brands: {product_stats['unique_brands']:,}")
            print(f"Unique categories: {product_stats['unique_categories']:,}")
            
            print("\n📈 Index Sizes:")
            for idx in index_sizes:
                print(f"  {idx['indexname']}: {idx['size']}")
            
            print("\n✅ Search optimization completed!")


async def main():
    """Main function to run search index optimization."""
    print("🚀 ProfiDent Search Index Optimization")
    print("=" * 50)
    
    optimizer = SearchIndexOptimizer()
    
    try:
        await optimizer.initialize()
        
        # Run optimization steps
        await optimizer.analyze_current_indexes()
        await optimizer.test_search_performance()
        await optimizer.create_additional_indexes()
        await optimizer.optimize_existing_indexes()
        await optimizer.configure_search_settings()
        await optimizer.generate_search_performance_report()
        
        return True
        
    except Exception as e:
        print(f"❌ Optimization failed: {e}")
        return False
        
    finally:
        await optimizer.close()


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)

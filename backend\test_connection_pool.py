#!/usr/bin/env python3
"""
Test script for PostgreSQL connection pooling
Verifies that connection pooling is working correctly
"""

import asyncio
import sys
import time
from pathlib import Path

# Add the app directory to Python path
sys.path.insert(0, str(Path(__file__).parent / "app"))

from app.database import <PERSON>Manager, db_manager
from app.config import settings


async def test_single_connection():
    """Test single database connection."""
    print("🔍 Testing single database connection...")
    
    try:
        db = DatabaseManager()
        await db.initialize()
        
        # Test basic query
        result = await db.execute_scalar("SELECT version()")
        print(f"✅ Database version: {result[:50]}...")
        
        # Test pool status
        status = await db.get_pool_status()
        print(f"✅ Pool status: {status}")
        
        await db.close()
        return True
        
    except Exception as e:
        print(f"❌ Single connection test failed: {e}")
        return False


async def test_concurrent_connections():
    """Test multiple concurrent connections."""
    print("\n🔍 Testing concurrent database connections...")
    
    try:
        db = DatabaseManager()
        await db.initialize()
        
        async def query_task(task_id: int):
            """Individual query task."""
            start_time = time.time()
            result = await db.execute_scalar(f"SELECT {task_id} as task_id, pg_backend_pid() as pid")
            end_time = time.time()
            return {
                "task_id": task_id,
                "result": result,
                "duration": end_time - start_time,
                "pid": result  # pg_backend_pid() returns the process ID
            }
        
        # Create 15 concurrent tasks (more than min pool size)
        num_tasks = 15
        print(f"Creating {num_tasks} concurrent database tasks...")
        
        start_time = time.time()
        tasks = [query_task(i) for i in range(num_tasks)]
        results = await asyncio.gather(*tasks)
        end_time = time.time()
        
        # Analyze results
        total_duration = end_time - start_time
        unique_pids = set(r["pid"] for r in results)
        avg_task_duration = sum(r["duration"] for r in results) / len(results)
        
        print(f"✅ Completed {len(results)} concurrent queries")
        print(f"✅ Total duration: {total_duration:.3f}s")
        print(f"✅ Average task duration: {avg_task_duration:.3f}s")
        print(f"✅ Unique database connections used: {len(unique_pids)}")
        print(f"✅ Connection reuse efficiency: {(num_tasks - len(unique_pids)) / num_tasks * 100:.1f}%")
        
        # Check pool status after concurrent operations
        status = await db.get_pool_status()
        print(f"✅ Final pool status: {status}")
        
        await db.close()
        return True
        
    except Exception as e:
        print(f"❌ Concurrent connection test failed: {e}")
        return False


async def test_connection_recovery():
    """Test connection recovery after errors."""
    print("\n🔍 Testing connection recovery...")
    
    try:
        db = DatabaseManager()
        await db.initialize()
        
        # Test normal query
        result1 = await db.execute_scalar("SELECT 'normal_query' as test")
        print(f"✅ Normal query result: {result1}")
        
        # Test query with error (should not break the pool)
        try:
            await db.execute_scalar("SELECT * FROM non_existent_table")
        except Exception as e:
            print(f"✅ Expected error handled: {type(e).__name__}")
        
        # Test that pool still works after error
        result2 = await db.execute_scalar("SELECT 'recovery_query' as test")
        print(f"✅ Recovery query result: {result2}")
        
        await db.close()
        return True
        
    except Exception as e:
        print(f"❌ Connection recovery test failed: {e}")
        return False


async def test_health_check():
    """Test database health check functionality."""
    print("\n🔍 Testing database health check...")
    
    try:
        db = DatabaseManager()
        await db.initialize()
        
        health = await db.health_check()
        print(f"✅ Health check results:")
        for service, status in health.items():
            status_icon = "✅" if status["status"] == "healthy" else "❌"
            print(f"  {status_icon} {service}: {status['status']}")
            if status.get("error"):
                print(f"    Error: {status['error']}")
        
        await db.close()
        return all(status["status"] == "healthy" for status in health.values())
        
    except Exception as e:
        print(f"❌ Health check test failed: {e}")
        return False


async def test_redis_connection():
    """Test Redis connection."""
    print("\n🔍 Testing Redis connection...")
    
    try:
        db = DatabaseManager()
        await db.initialize()
        
        if db.redis_client:
            # Test Redis operations
            await db.redis_client.set("test_key", "test_value", ex=60)
            result = await db.redis_client.get("test_key")
            print(f"✅ Redis test result: {result}")
            
            # Clean up
            await db.redis_client.delete("test_key")
            print("✅ Redis cleanup completed")
        else:
            print("❌ Redis client not initialized")
            return False
        
        await db.close()
        return True
        
    except Exception as e:
        print(f"❌ Redis connection test failed: {e}")
        return False


async def main():
    """Run all connection pool tests."""
    print("🚀 Starting ProfiDent Connection Pool Tests\n")
    print(f"Database URL: {settings.DATABASE_URL}")
    print(f"Redis URL: {settings.REDIS_URL}")
    print(f"Pool config: min={settings.DB_POOL_MIN_SIZE}, max={settings.DB_POOL_MAX_SIZE}")
    print("=" * 60)
    
    tests = [
        ("Single Connection", test_single_connection),
        ("Concurrent Connections", test_concurrent_connections),
        ("Connection Recovery", test_connection_recovery),
        ("Health Check", test_health_check),
        ("Redis Connection", test_redis_connection),
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = await test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} failed with exception: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 TEST SUMMARY")
    print("=" * 60)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{status} - {test_name}")
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All connection pool tests passed!")
        print("✅ Connection pooling is working correctly")
        return True
    else:
        print("❌ Some tests failed. Check configuration and database connectivity.")
        return False


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)

#!/usr/bin/env python3
"""
Real ProfiDent API Server with Database Connection
Connects to the actual PostgreSQL database with 339K products
"""

import sys
import os
import time
import asyncio
from pathlib import Path

# Add current directory to Python path
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))
sys.path.insert(0, str(current_dir / "app"))

# Set environment variables
os.environ.setdefault("PYTHONPATH", str(current_dir))

try:
    import uvicorn
    import asyncpg
    from fastapi import FastAPI, HTTPException, Depends, Query
    from fastapi.middleware.cors import CORSMiddleware
    from fastapi.security import HTTPBearer, HTTPAuthorizationCredentials
    from pydantic import BaseModel
    from typing import List, Optional
    import json
    
    # Create FastAPI app
    app = FastAPI(
        title="ProfiDent API",
        description="Dental Product Price Comparison Platform with Real Database",
        version="1.0.0"
    )
    
    # Add CORS middleware
    app.add_middleware(
        CORSMiddleware,
        allow_origins=["http://localhost:3000", "http://localhost:5173", "http://127.0.0.1:3000", "http://127.0.0.1:5173"],
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )
    
    # Security
    security = HTTPBearer(auto_error=False)
    
    # Database connection
    DATABASE_URL = "postgresql://profident_user:profident_dev_password@localhost:5433/profident_dev"
    db_pool = None
    
    # Models
    class User(BaseModel):
        id: int
        email: str
        full_name: str
        created_at: str
    
    class LoginRequest(BaseModel):
        email: str
        password: str
    
    class RegisterRequest(BaseModel):
        email: str
        password: str
        full_name: str
    
    class AuthResponse(BaseModel):
        user: User
        token: dict
    
    class Product(BaseModel):
        id: int
        name: str
        seller: str
        price: Optional[str] = None
        maincat: Optional[str] = None
        brand: Optional[str] = None
        description: Optional[str] = None
    
    class SearchResponse(BaseModel):
        results: List[Product]
        total: int
        page: int
        limit: int
        search_time_ms: float
    
    # Mock users for testing
    mock_users = {}
    mock_user_counter = 1
    
    async def init_db():
        """Initialize database connection pool."""
        global db_pool
        try:
            db_pool = await asyncpg.create_pool(
                DATABASE_URL,
                min_size=5,
                max_size=20,
                command_timeout=30
            )
            print("✅ Database connection pool created")
            
            # Test connection and get product count
            async with db_pool.acquire() as conn:
                count = await conn.fetchval("SELECT COUNT(*) FROM products")
                print(f"✅ Connected to database with {count:,} products")
                
        except Exception as e:
            print(f"❌ Database connection failed: {e}")
            db_pool = None
    
    async def close_db():
        """Close database connection pool."""
        global db_pool
        if db_pool:
            await db_pool.close()
            print("✅ Database connection pool closed")
    
    def get_current_user(credentials: HTTPAuthorizationCredentials = Depends(security)):
        if not credentials:
            raise HTTPException(status_code=401, detail="Not authenticated")
        # Mock authentication - in real app, verify JWT token
        return {"id": 1, "email": "<EMAIL>", "full_name": "Test User"}

    def get_current_user_optional(credentials: Optional[HTTPAuthorizationCredentials] = Depends(security)):
        if not credentials:
            return None  # Return None if no authentication provided
        # Mock authentication - in real app, verify JWT token
        return {"id": 1, "email": "<EMAIL>", "full_name": "Test User"}
    
    # Startup and shutdown events
    @app.on_event("startup")
    async def startup_event():
        await init_db()
    
    @app.on_event("shutdown")
    async def shutdown_event():
        await close_db()
    
    # Health endpoints
    @app.get("/")
    async def root():
        return {"message": "ProfiDent API with Real Database is running!", "status": "healthy"}
    
    @app.get("/health")
    async def health():
        status = "healthy" if db_pool else "unhealthy"
        return {"status": status, "message": f"API server is {status}", "database": "connected" if db_pool else "disconnected"}
    
    @app.get("/api/v1/health")
    async def api_health():
        if not db_pool:
            raise HTTPException(status_code=503, detail="Database not available")
        
        try:
            async with db_pool.acquire() as conn:
                count = await conn.fetchval("SELECT COUNT(*) FROM products")
                return {
                    "status": "healthy", 
                    "message": "API v1 is running with database", 
                    "product_count": count
                }
        except Exception as e:
            raise HTTPException(status_code=503, detail=f"Database error: {e}")
    
    # Auth endpoints (mock for testing)
    @app.post("/api/v1/auth/register", response_model=AuthResponse)
    async def register(request: RegisterRequest):
        global mock_user_counter
        
        if request.email in mock_users:
            raise HTTPException(status_code=400, detail="Email already registered")
        
        user = User(
            id=mock_user_counter,
            email=request.email,
            full_name=request.full_name,
            created_at=time.strftime("%Y-%m-%dT%H:%M:%SZ")
        )
        
        mock_users[request.email] = user
        mock_user_counter += 1
        
        token = {
            "access_token": f"mock_token_{user.id}",
            "token_type": "bearer",
            "expires_in": 3600
        }
        
        return AuthResponse(user=user, token=token)
    
    @app.post("/api/v1/auth/login", response_model=AuthResponse)
    async def login(request: LoginRequest):
        if request.email not in mock_users:
            raise HTTPException(status_code=401, detail="Invalid credentials")
        
        user = mock_users[request.email]
        token = {
            "access_token": f"mock_token_{user.id}",
            "token_type": "bearer",
            "expires_in": 3600
        }
        
        return AuthResponse(user=user, token=token)
    
    @app.get("/api/v1/auth/me", response_model=User)
    async def get_current_user_info(current_user: dict = Depends(get_current_user)):
        return User(
            id=current_user["id"],
            email=current_user["email"],
            full_name=current_user["full_name"],
            created_at=time.strftime("%Y-%m-%dT%H:%M:%SZ")
        )
    
    # Search endpoints with real database
    @app.get("/api/v1/search/", response_model=SearchResponse)
    async def search_products(
        q: str = Query("", description="Search query"),
        limit: int = Query(20, ge=1, le=100, description="Number of results per page"),
        offset: int = Query(0, ge=0, description="Number of results to skip"),
        category: Optional[str] = Query(None, description="Filter by category"),
        brand: Optional[str] = Query(None, description="Filter by brand (deprecated, use manufactured_by)"),
        manufactured_by: Optional[str] = Query(None, description="Filter by manufacturer"),
        seller: Optional[str] = Query(None, description="Filter by seller"),
        current_user: Optional[dict] = Depends(get_current_user_optional)
    ):
        if not db_pool:
            raise HTTPException(status_code=503, detail="Database not available")
        
        start_time = time.time()
        
        try:
            async with db_pool.acquire() as conn:
                # Build the search query
                where_conditions = []
                params = []
                param_count = 0
                
                # Full-text search on search_text field
                if q.strip():
                    param_count += 1
                    where_conditions.append(f"to_tsvector('english', search_text) @@ to_tsquery('english', ${param_count})")
                    # Format query for PostgreSQL full-text search
                    formatted_query = ' & '.join(q.strip().split())
                    params.append(formatted_query)
                
                # Category filter - FIXED: Use 'category' column instead of 'maincat'
                if category:
                    param_count += 1
                    where_conditions.append(f"category ILIKE ${param_count}")
                    params.append(f"%{category}%")

                # Manufacturer filter - FIXED: Use 'manufactured_by' column instead of 'brand'
                if manufactured_by:
                    param_count += 1
                    where_conditions.append(f"manufactured_by ILIKE ${param_count}")
                    params.append(f"%{manufactured_by}%")

                # Brand filter (backward compatibility) - redirect to manufactured_by
                if brand and not manufactured_by:
                    param_count += 1
                    where_conditions.append(f"manufactured_by ILIKE ${param_count}")
                    params.append(f"%{brand}%")

                # Seller filter
                if seller:
                    param_count += 1
                    where_conditions.append(f"seller ILIKE ${param_count}")
                    params.append(f"%{seller}%")
                
                # Build WHERE clause
                where_clause = ""
                if where_conditions:
                    where_clause = "WHERE " + " AND ".join(where_conditions)
                
                # Count total results
                count_query = f"SELECT COUNT(*) FROM products {where_clause}"
                total = await conn.fetchval(count_query, *params)
                
                # Get paginated results
                order_clause = ""
                if q.strip():
                    # Order by relevance for text search
                    order_clause = f"ORDER BY ts_rank(to_tsvector('english', search_text), to_tsquery('english', $1)) DESC"
                else:
                    # Default order by id
                    order_clause = "ORDER BY id"

                results_query = f"""
                    SELECT id, name, seller, price, maincat, brand, mfr, category, url
                    FROM products
                    {where_clause}
                    {order_clause}
                    LIMIT {limit} OFFSET {offset}
                """

                rows = await conn.fetch(results_query, *params)

                # Convert to Product objects
                products = []
                for row in rows:
                    products.append(Product(
                        id=row['id'],
                        name=row['name'],
                        seller=row['seller'] or "",
                        price=row['price'] or "",
                        maincat=row['maincat'] or "",
                        brand=row['brand'] or "",
                        description=f"MFR: {row['mfr'] or ''} | Category: {row['category'] or ''}"
                    ))
                
                search_time = (time.time() - start_time) * 1000
                
                return SearchResponse(
                    results=products,
                    total=total,
                    page=(offset // limit) + 1,
                    limit=limit,
                    search_time_ms=round(search_time, 2)
                )
                
        except Exception as e:
            print(f"Search error: {e}")
            raise HTTPException(status_code=500, detail=f"Search failed: {str(e)}")

    @app.get("/api/v1/search/suggestions")
    async def get_search_suggestions(
        q: str = Query("", description="Search query for suggestions"),
        category: Optional[str] = Query(None, description="Filter suggestions by category"),
        manufactured_by: Optional[str] = Query(None, description="Filter suggestions by manufacturer"),
        brand: Optional[str] = Query(None, description="Filter suggestions by brand (deprecated, use manufactured_by)"),
        seller: Optional[str] = Query(None, description="Filter suggestions by seller"),
        current_user: Optional[dict] = Depends(get_current_user_optional)
    ):
        """Get filtered search suggestions based on active filters."""
        if not db_pool:
            raise HTTPException(status_code=503, detail="Database not available")

        if not q.strip():
            return {"suggestions": []}

        try:
            async with db_pool.acquire() as conn:
                # Build filter conditions for suggestions
                filter_conditions = []
                filter_params = []
                param_count = 1

                # Always include the search query
                filter_params.append(f"{q}%")

                # Add filter conditions - FIXED: Use correct database columns
                if category:
                    param_count += 1
                    filter_conditions.append(f"category ILIKE ${param_count}")
                    filter_params.append(f"%{category}%")

                if manufactured_by:
                    param_count += 1
                    filter_conditions.append(f"manufactured_by ILIKE ${param_count}")
                    filter_params.append(f"%{manufactured_by}%")
                elif brand:  # Backward compatibility
                    param_count += 1
                    filter_conditions.append(f"manufactured_by ILIKE ${param_count}")
                    filter_params.append(f"%{brand}%")

                if seller:
                    param_count += 1
                    filter_conditions.append(f"seller ILIKE ${param_count}")
                    filter_params.append(f"%{seller}%")

                # Build WHERE clause
                where_clause = "WHERE name ILIKE $1"
                if filter_conditions:
                    where_clause += " AND " + " AND ".join(filter_conditions)

                # Get filtered suggestions from product names
                suggestions_query = f"""
                    SELECT DISTINCT name
                    FROM products
                    {where_clause}
                    ORDER BY name
                    LIMIT 10
                """

                rows = await conn.fetch(suggestions_query, *filter_params)
                suggestions = [row['name'] for row in rows]

                return {"suggestions": suggestions}

        except Exception as e:
            print(f"Suggestions error: {e}")
            return {"suggestions": []}

    @app.get("/api/v1/products/match/{mfr}")
    async def get_products_by_mfr(
        mfr: str,
        limit: int = Query(20, ge=1, le=100, description="Maximum number of products"),
        current_user: Optional[dict] = Depends(get_current_user_optional)
    ):
        """Get all products with the same MFR code across different sellers."""
        if not db_pool:
            raise HTTPException(status_code=503, detail="Database not available")

        try:
            async with db_pool.acquire() as conn:
                # Search for products with the same MFR code
                query = """
                    SELECT id, name, mfr, price, seller, manufactured_by, category, url
                    FROM products
                    WHERE mfr = $1
                    ORDER BY seller, price
                    LIMIT $2
                """

                rows = await conn.fetch(query, mfr, limit)
                products = []

                for row in rows:
                    product = {
                        "id": row['id'],
                        "name": row['name'],
                        "mfr": row['mfr'],
                        "price": row['price'],
                        "seller": row['seller'],
                        "manufactured_by": row['manufactured_by'],
                        "category": row['category'],
                        "url": row['url']
                    }
                    products.append(product)

                return {
                    "mfr": mfr,
                    "total": len(products),
                    "products": products,
                    "pagination": {
                        "page": 1,
                        "limit": limit,
                        "total": len(products),
                        "has_next": False,
                        "has_prev": False,
                    }
                }

        except Exception as e:
            print(f"Product matching error: {e}")
            return {
                "mfr": mfr,
                "total": 0,
                "products": [],
                "pagination": {
                    "page": 1,
                    "limit": limit,
                    "total": 0,
                    "has_next": False,
                    "has_prev": False,
                }
            }

    @app.get("/api/v1/search/popular")
    async def get_popular_searches(current_user: Optional[dict] = Depends(get_current_user_optional)):
        if not db_pool:
            raise HTTPException(status_code=503, detail="Database not available")

        try:
            async with db_pool.acquire() as conn:
                # Get most common categories as popular searches
                popular_query = """
                    SELECT maincat, COUNT(*) as count
                    FROM products
                    WHERE maincat IS NOT NULL
                    GROUP BY maincat
                    ORDER BY count DESC
                    LIMIT 10
                """

                rows = await conn.fetch(popular_query)
                popular_searches = [row['maincat'] for row in rows if row['maincat']]

                return {"popular_searches": popular_searches}

        except Exception as e:
            print(f"Popular searches error: {e}")
            return {"popular_searches": ["dental", "implant", "composite", "crown", "orthodontic"]}

    # Filter endpoints for categories, brands, and sellers
    @app.get("/api/v1/products/categories/")
    async def get_categories(
        limit: int = Query(100, ge=1, le=500, description="Maximum number of categories"),
        search: Optional[str] = Query(None, description="Search within categories"),
        current_user: Optional[dict] = Depends(get_current_user_optional)
    ):
        """Get unique product categories with counts."""
        if not db_pool:
            raise HTTPException(status_code=503, detail="Database not available")

        try:
            async with db_pool.acquire() as conn:
                # Build query with optional search filter - FIXED: Use 'category' column instead of 'maincat'
                where_clause = "WHERE category IS NOT NULL AND category != ''"
                params = []

                if search and search.strip():
                    where_clause += " AND category ILIKE $1"
                    params.append(f"%{search.strip()}%")

                query = f"""
                    SELECT category as name, COUNT(*) as count
                    FROM products
                    {where_clause}
                    GROUP BY category
                    ORDER BY count DESC, category ASC
                    LIMIT {limit}
                """

                rows = await conn.fetch(query, *params)
                categories = [{"name": row['name'], "count": row['count']} for row in rows]

                return {"categories": categories}

        except Exception as e:
            print(f"Categories error: {e}")
            # Fallback to mock data
            return {
                "categories": [
                    {"name": "Implants", "count": 150},
                    {"name": "Restorative", "count": 200},
                    {"name": "Orthodontics", "count": 100},
                    {"name": "Endodontics", "count": 80},
                    {"name": "Prosthetics", "count": 120},
                ]
            }

    @app.get("/api/v1/products/manufacturers/")
    async def get_manufacturers(
        limit: int = Query(100, ge=1, le=500, description="Maximum number of manufacturers"),
        search: Optional[str] = Query(None, description="Search within manufacturers"),
        current_user: Optional[dict] = Depends(get_current_user_optional)
    ):
        """Get unique product manufacturers with counts."""
        if not db_pool:
            raise HTTPException(status_code=503, detail="Database not available")

        try:
            async with db_pool.acquire() as conn:
                # Build query with optional search filter - FIXED: Use 'manufactured_by' column instead of 'brand'
                where_clause = "WHERE manufactured_by IS NOT NULL AND manufactured_by != ''"
                params = []

                if search and search.strip():
                    where_clause += " AND manufactured_by ILIKE $1"
                    params.append(f"%{search.strip()}%")

                query = f"""
                    SELECT manufactured_by as name, COUNT(*) as count
                    FROM products
                    {where_clause}
                    GROUP BY manufactured_by
                    ORDER BY count DESC, manufactured_by ASC
                    LIMIT {limit}
                """

                rows = await conn.fetch(query, *params)
                manufacturers = [{"name": row['name'], "count": row['count']} for row in rows]

                return {"manufacturers": manufacturers}

        except Exception as e:
            print(f"Manufacturers error: {e}")
            # Fallback to mock data
            return {
                "manufacturers": [
                    {"name": "Hu-Friedy", "count": 150},
                    {"name": "Dentsply Sirona", "count": 200},
                    {"name": "3M", "count": 100},
                    {"name": "Kerr", "count": 80},
                    {"name": "Ivoclar Vivadent", "count": 120},
                ]
            }

    # Keep brands endpoint for backward compatibility during transition
    @app.get("/api/v1/products/brands/")
    async def get_brands(
        limit: int = Query(100, ge=1, le=500, description="Maximum number of brands"),
        search: Optional[str] = Query(None, description="Search within brands"),
        current_user: Optional[dict] = Depends(get_current_user_optional)
    ):
        """Get unique product brands with counts. DEPRECATED: Use /manufacturers/ instead."""
        # Redirect to manufacturers endpoint
        manufacturers_result = await get_manufacturers(limit, search, current_user)
        # Convert format for backward compatibility
        if "manufacturers" in manufacturers_result:
            brands = [{"name": item["name"], "count": item["count"]} for item in manufacturers_result["manufacturers"]]
            return {"brands": brands}
        return {"brands": []}



    @app.get("/api/v1/products/sellers/")
    async def get_sellers(
        limit: int = Query(50, ge=1, le=200, description="Maximum number of sellers"),
        search: Optional[str] = Query(None, description="Search within sellers"),
        current_user: Optional[dict] = Depends(get_current_user_optional)
    ):
        """Get unique product sellers with counts."""
        if not db_pool:
            raise HTTPException(status_code=503, detail="Database not available")

        try:
            async with db_pool.acquire() as conn:
                # Build query with optional search filter
                where_clause = "WHERE seller IS NOT NULL AND seller != ''"
                params = []

                if search and search.strip():
                    where_clause += " AND seller ILIKE $1"
                    params.append(f"%{search.strip()}%")

                query = f"""
                    SELECT seller as name, COUNT(*) as count
                    FROM products
                    {where_clause}
                    GROUP BY seller
                    ORDER BY count DESC, seller ASC
                    LIMIT {limit}
                """

                rows = await conn.fetch(query, *params)
                sellers = [{"name": row['name'], "count": row['count']} for row in rows]

                return {"sellers": sellers}

        except Exception as e:
            print(f"Sellers error: {e}")
            # Fallback to mock data
            return {
                "sellers": [
                    {"name": "darby", "count": 15000},
                    {"name": "midwest", "count": 12000},
                    {"name": "henry schein", "count": 8000},
                    {"name": "patterson", "count": 6000},
                ]
            }
    
    if __name__ == "__main__":
        print("🚀 Starting ProfiDent API Server with Real Database...")
        print("📍 Server will be available at: http://localhost:8000")
        print("📖 API Documentation: http://localhost:8000/docs")
        print("🔧 Health Check: http://localhost:8000/health")
        
        uvicorn.run(
            app,
            host="0.0.0.0",
            port=8000,
            reload=False,
            log_level="info"
        )

except ImportError as e:
    print(f"❌ Import error: {e}")
    print("Installing required packages...")
    
    import subprocess
    packages = [
        "fastapi",
        "uvicorn[standard]",
        "asyncpg",
        "pydantic"
    ]
    
    for package in packages:
        try:
            subprocess.check_call([sys.executable, "-m", "pip", "install", package])
        except subprocess.CalledProcessError:
            print(f"Failed to install {package}")
    
    print("Please run the script again after installing packages.")
    sys.exit(1)

except Exception as e:
    print(f"❌ Error starting server: {e}")
    sys.exit(1)

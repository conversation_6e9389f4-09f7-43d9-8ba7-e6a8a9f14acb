"""
Authentication service for user management and JWT handling
"""

import time
from typing import Op<PERSON>, <PERSON><PERSON>
from asyncpg import Connection
from fastapi import HTTPEx<PERSON>, status

try:
    from ..models.user import User, UserRepository
    from ..schemas.auth import UserCreate, UserLogin, UserUpdate, AuthResponse, Token
    from ..utils.security import (
        create_token_response,
        verify_token,
        verify_token_not_blacklisted,
        blacklist_token,
        verify_password,
        is_valid_email
    )
    from ..utils.account_lockout import get_lockout_manager
    from ..utils.audit_logger import get_audit_logger, AuditEventType, AuditSeverity
except ImportError:
    from models.user import User, UserRepository
    from schemas.auth import UserCreate, UserLogin, UserUpdate, AuthResponse, Token
    from utils.security import (
        create_token_response,
        verify_token,
        verify_token_not_blacklisted,
        blacklist_token,
        verify_password,
        is_valid_email
    )
    from utils.account_lockout import get_lockout_manager
    from utils.audit_logger import get_audit_logger, AuditEventType, AuditSeverity


class AuthService:
    """Authentication service for user operations."""
    
    @staticmethod
    async def register_user(conn: Connection, user_data: UserCreate) -> Tuple[User, dict]:
        """Register a new user."""
        # Check if email already exists
        if await UserRepository.email_exists(conn, user_data.email):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Email already registered"
            )
        
        # Validate email format
        if not is_valid_email(user_data.email):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invalid email format"
            )
        
        try:
            # Create user
            user = await UserRepository.create_user(
                conn=conn,
                email=user_data.email,
                password=user_data.password,
                full_name=user_data.full_name
            )

            # Log successful user registration
            try:
                audit_logger = get_audit_logger()
                await audit_logger.log_authentication_event(
                    event_type=AuditEventType.REGISTRATION,
                    user_id=user.id,
                    user_email=user.email,
                    success=True,
                    message=f"User {user.email} registered successfully",
                    details={
                        "user_id": user.id,
                        "full_name": user_data.full_name,
                        "registration_method": "email_password"
                    },
                    severity=AuditSeverity.MEDIUM
                )
            except RuntimeError:
                # Audit logger not initialized, continue without logging
                pass

            # Assign default "user" role to new user
            role_assigned = await UserRepository.assign_role_to_user_by_name(
                conn=conn,
                user_id=user.id,
                role_name="user",
                assigned_by=None  # System assignment
            )

            if not role_assigned:
                # Log warning but don't fail registration - user can be assigned role later
                print(f"Warning: Failed to assign default 'user' role to new user {user.email}")
                print("This could be because the 'user' role doesn't exist in the database.")

            # Create default shopping list for new user
            from ..models.shopping_list import ShoppingListRepository
            await ShoppingListRepository.create_shopping_list(
                conn=conn,
                user_id=user.id,
                name="My Shopping List",
                description="Default shopping list for quick additions",
                is_default=True
            )

            # Load user with roles and permissions for token generation
            user_with_roles = await UserRepository.get_user_with_roles_and_permissions(conn, user.id)
            if not user_with_roles:
                user_with_roles = user  # Fallback to basic user if RBAC loading fails

            # Generate tokens with role information
            tokens = create_token_response(
                user_id=user_with_roles.id,
                email=user_with_roles.email,
                roles=user_with_roles.roles,
                permissions=list(user_with_roles.permissions),
                is_superuser=user_with_roles.is_superuser
            )

            return user_with_roles, tokens
            
        except Exception as e:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Failed to create user: {str(e)}"
            )
    
    @staticmethod
    async def authenticate_user(conn: Connection, login_data: UserLogin) -> Tuple[User, dict]:
        """Authenticate user and return tokens with account lockout protection."""
        # Check account lockout status before attempting authentication
        try:
            lockout_manager = get_lockout_manager()
            lockout_status = await lockout_manager.check_lockout_status(login_data.email)

            if lockout_status.is_locked:
                remaining_time = lockout_status.lockout_expires_at - int(time.time()) if lockout_status.lockout_expires_at else 0
                raise HTTPException(
                    status_code=status.HTTP_423_LOCKED,
                    detail={
                        "error": "Account temporarily locked",
                        "message": f"Account is locked due to too many failed login attempts. Try again in {remaining_time} seconds.",
                        "locked_until": lockout_status.lockout_expires_at,
                        "error_code": "ACCOUNT_LOCKED"
                    }
                )
        except RuntimeError:
            # Lockout manager not initialized, continue without lockout protection
            pass

        user = await UserRepository.authenticate_user(
            conn=conn,
            email=login_data.email,
            password=login_data.password
        )

        if not user:
            # Log failed login attempt
            try:
                audit_logger = get_audit_logger()
                await audit_logger.log_authentication_event(
                    event_type=AuditEventType.LOGIN_FAILURE,
                    user_email=login_data.email,
                    success=False,
                    message=f"Failed login attempt for {login_data.email}",
                    details={
                        "email": login_data.email,
                        "reason": "invalid_credentials"
                    },
                    severity=AuditSeverity.MEDIUM
                )
            except RuntimeError:
                # Audit logger not initialized, continue without logging
                pass

            # Record failed attempt for account lockout
            try:
                lockout_manager = get_lockout_manager()
                lockout_status = await lockout_manager.record_failed_attempt(login_data.email)

                if lockout_status.is_locked:
                    remaining_time = lockout_status.lockout_expires_at - int(time.time()) if lockout_status.lockout_expires_at else 0
                    raise HTTPException(
                        status_code=status.HTTP_423_LOCKED,
                        detail={
                            "error": "Account locked",
                            "message": f"Too many failed attempts. Account locked for {remaining_time} seconds.",
                            "locked_until": lockout_status.lockout_expires_at,
                            "error_code": "ACCOUNT_LOCKED"
                        }
                    )
                else:
                    # Include remaining attempts in error message
                    detail_msg = "Incorrect email or password"
                    if lockout_status.remaining_attempts <= 2:
                        detail_msg += f". {lockout_status.remaining_attempts} attempts remaining before account lockout."

                    raise HTTPException(
                        status_code=status.HTTP_401_UNAUTHORIZED,
                        detail=detail_msg,
                        headers={"WWW-Authenticate": "Bearer"},
                    )
            except RuntimeError:
                # Lockout manager not initialized, use standard error
                pass

            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Incorrect email or password",
                headers={"WWW-Authenticate": "Bearer"},
            )
        
        if not user.is_active:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Inactive user account"
            )
        
        # Load user with roles and permissions for token generation
        user_with_roles = await UserRepository.get_user_with_roles_and_permissions(conn, user.id)
        if not user_with_roles:
            user_with_roles = user  # Fallback to basic user if RBAC loading fails

        # Validate user has at least one active role (unless superuser)
        if not user_with_roles.is_superuser and not user_with_roles.roles:
            # Try to assign default "user" role if user has no roles
            role_assigned = await UserRepository.assign_role_to_user_by_name(
                conn=conn,
                user_id=user.id,
                role_name="user",
                assigned_by=None  # System assignment
            )

            if role_assigned:
                # Reload user with newly assigned role
                user_with_roles = await UserRepository.get_user_with_roles_and_permissions(conn, user.id)
                if not user_with_roles:
                    user_with_roles = user  # Fallback if reload fails
            else:
                # If role assignment fails, log warning but allow login
                print(f"Warning: User {user.email} has no roles and default role assignment failed")

        # Reset failed attempts on successful login
        try:
            lockout_manager = get_lockout_manager()
            await lockout_manager.reset_attempts(login_data.email)
        except RuntimeError:
            # Lockout manager not initialized, continue without reset
            pass

        # Generate tokens with role information
        tokens = create_token_response(
            user_id=user_with_roles.id,
            email=user_with_roles.email,
            roles=user_with_roles.roles,
            permissions=list(user_with_roles.permissions),
            is_superuser=user_with_roles.is_superuser
        )

        # Log successful login
        try:
            audit_logger = get_audit_logger()
            await audit_logger.log_authentication_event(
                event_type=AuditEventType.LOGIN_SUCCESS,
                user_id=user_with_roles.id,
                user_email=user_with_roles.email,
                success=True,
                message=f"User {user_with_roles.email} logged in successfully",
                details={
                    "user_id": user_with_roles.id,
                    "roles": user_with_roles.roles,
                    "is_superuser": user_with_roles.is_superuser,
                    "login_method": "email_password"
                },
                severity=AuditSeverity.LOW
            )
        except RuntimeError:
            # Audit logger not initialized, continue without logging
            pass

        return user_with_roles, tokens
    
    @staticmethod
    async def refresh_access_token(conn: Connection, refresh_token: str) -> dict:
        """Refresh access token using refresh token."""
        payload = verify_token(refresh_token)
        
        if not payload:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid refresh token",
                headers={"WWW-Authenticate": "Bearer"},
            )
        
        # Check if it's a refresh token
        if payload.get("type") != "refresh":
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid token type",
                headers={"WWW-Authenticate": "Bearer"},
            )
        
        user_id = payload.get("sub")
        email = payload.get("email")
        
        if not user_id or not email:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid token payload",
                headers={"WWW-Authenticate": "Bearer"},
            )
        
        # Verify user still exists and is active
        user = await UserRepository.get_user_by_id(conn, user_id)
        if not user or not user.is_active:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="User not found or inactive",
                headers={"WWW-Authenticate": "Bearer"},
            )
        
        # Load user with roles and permissions for token generation
        user_with_roles = await UserRepository.get_user_with_roles_and_permissions(conn, user.id)
        if not user_with_roles:
            user_with_roles = user  # Fallback to basic user if RBAC loading fails

        # Validate user has at least one active role (unless superuser)
        if not user_with_roles.is_superuser and not user_with_roles.roles:
            # Try to assign default "user" role if user has no roles
            role_assigned = await UserRepository.assign_role_to_user_by_name(
                conn=conn,
                user_id=user.id,
                role_name="user",
                assigned_by=None  # System assignment
            )

            if role_assigned:
                # Reload user with newly assigned role
                user_with_roles = await UserRepository.get_user_with_roles_and_permissions(conn, user.id)
                if not user_with_roles:
                    user_with_roles = user  # Fallback if reload fails
            else:
                # If role assignment fails, log warning but allow token refresh
                print(f"Warning: User {user.email} has no roles and default role assignment failed")

        # Generate new tokens with role information
        tokens = create_token_response(
            user_id=user_with_roles.id,
            email=user_with_roles.email,
            roles=user_with_roles.roles,
            permissions=list(user_with_roles.permissions),
            is_superuser=user_with_roles.is_superuser
        )

        return tokens
    
    @staticmethod
    async def get_current_user(conn: Connection, token: str) -> User:
        """Get current user from JWT token (without blacklist checking)."""
        payload = verify_token(token)

        if not payload:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Could not validate credentials",
                headers={"WWW-Authenticate": "Bearer"},
            )

        user_id = payload.get("sub")
        if not user_id:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Could not validate credentials",
                headers={"WWW-Authenticate": "Bearer"},
            )

        # Load user with roles and permissions
        user = await UserRepository.get_user_with_roles_and_permissions(conn, user_id)
        if not user:
            # Fallback to basic user if RBAC loading fails
            user = await UserRepository.get_user_by_id(conn, user_id)
            if not user:
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="User not found",
                    headers={"WWW-Authenticate": "Bearer"},
                )

        return user

    @staticmethod
    async def get_current_user_with_blacklist_check(conn: Connection, redis_client, token: str) -> User:
        """Get current user from JWT token with blacklist checking."""
        payload = await verify_token_not_blacklisted(redis_client, token)

        if not payload:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Could not validate credentials or token has been revoked",
                headers={"WWW-Authenticate": "Bearer"},
            )

        user_id = payload.get("sub")
        if not user_id:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Could not validate credentials",
                headers={"WWW-Authenticate": "Bearer"},
            )

        # Load user with roles and permissions
        user = await UserRepository.get_user_with_roles_and_permissions(conn, user_id)
        if not user:
            # Fallback to basic user if RBAC loading fails
            user = await UserRepository.get_user_by_id(conn, user_id)
            if not user:
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="User not found",
                    headers={"WWW-Authenticate": "Bearer"},
                )

        return user

    @staticmethod
    async def logout_user(redis_client, token: str) -> bool:
        """Logout user by blacklisting their token."""
        try:
            # Verify token to get expiration
            payload = verify_token(token)
            if not payload:
                # Token is already invalid, consider logout successful
                return True

            # Get token expiration
            exp_timestamp = payload.get("exp")
            expires_at = None
            if exp_timestamp:
                expires_at = datetime.fromtimestamp(exp_timestamp)

            # Blacklist the token
            success = await blacklist_token(redis_client, token, expires_at)

            # Log successful logout
            if success:
                try:
                    audit_logger = get_audit_logger()
                    await audit_logger.log_authentication_event(
                        event_type=AuditEventType.LOGOUT,
                        user_id=payload.get("sub"),
                        user_email=payload.get("email"),
                        success=True,
                        message=f"User {payload.get('email', 'unknown')} logged out successfully",
                        details={
                            "user_id": payload.get("sub"),
                            "logout_method": "token_blacklist"
                        },
                        severity=AuditSeverity.LOW
                    )
                except RuntimeError:
                    # Audit logger not initialized, continue without logging
                    pass

            return success

        except Exception as e:
            print(f"Error during logout: {e}")
            return False
    
    @staticmethod
    async def update_user_profile(
        conn: Connection, 
        user_id: str, 
        update_data: UserUpdate
    ) -> User:
        """Update user profile."""
        # Check if email is being changed and if it already exists
        if update_data.email:
            existing_user = await UserRepository.get_user_by_email(conn, update_data.email)
            if existing_user and existing_user.id != user_id:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Email already in use"
                )
        
        user = await UserRepository.update_user(
            conn=conn,
            user_id=user_id,
            email=update_data.email,
            full_name=update_data.full_name,
            is_active=update_data.is_active
        )
        
        if not user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="User not found"
            )
        
        return user
    
    @staticmethod
    async def change_password(
        conn: Connection,
        user_id: str,
        current_password: str,
        new_password: str
    ) -> bool:
        """Change user password."""
        user = await UserRepository.get_user_by_id(conn, user_id)
        if not user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="User not found"
            )
        
        # Verify current password
        if not verify_password(current_password, user.hashed_password):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Incorrect current password"
            )
        
        # Update password
        success = await UserRepository.update_password(conn, user_id, new_password)
        if not success:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to update password"
            )
        
        return True
    
    @staticmethod
    async def deactivate_user(conn: Connection, user_id: str) -> bool:
        """Deactivate user account."""
        success = await UserRepository.delete_user(conn, user_id)
        if not success:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="User not found"
            )
        
        return True

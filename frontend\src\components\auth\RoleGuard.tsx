import React from "react";
import { useHasAccess } from "../../hooks/useRoles";

interface RoleGuardProps {
  children: React.ReactNode;
  roles?: string[];
  permissions?: string[];
  requireAll?: boolean;
  allowSuperAdmin?: boolean;
  fallback?: React.ReactNode;
  inverse?: boolean; // Show content when user DOESN'T have access
}

/**
 * RoleGuard component for conditional rendering based on user roles and permissions
 *
 * @param children - Content to render when access is granted
 * @param roles - Array of required roles
 * @param permissions - Array of required permissions
 * @param requireAll - Whether all roles/permissions are required (default: false)
 * @param allowSuperAdmin - Whether superadmin bypasses checks (default: true)
 * @param fallback - Content to render when access is denied
 * @param inverse - Show content when user DOESN'T have access (default: false)
 */
export const RoleGuard: React.FC<RoleGuardProps> = ({
  children,
  roles = [],
  permissions = [],
  requireAll = false,
  allowSuperAdmin = true,
  fallback = null,
  inverse = false,
}) => {
  const hasAccess = useHasAccess({
    roles,
    permissions,
    requireAll,
    allowSuperAdmin,
  });

  // Inverse logic: show content when user <PERSON><PERSON><PERSON>'T have access
  if (inverse) {
    return hasAccess
      ? (fallback as React.ReactElement)
      : (children as React.ReactElement);
  }

  // Normal logic: show content when user HAS access
  return hasAccess
    ? (children as React.ReactElement)
    : (fallback as React.ReactElement);
};

/**
 * Convenience component for superadmin-only content
 */
export const SuperAdminOnly: React.FC<{
  children: React.ReactNode;
  fallback?: React.ReactNode;
}> = ({ children, fallback = null }) => (
  <RoleGuard
    permissions={["superadmin"]}
    allowSuperAdmin={true}
    fallback={fallback}
  >
    {children}
  </RoleGuard>
);

/**
 * Convenience component for authenticated users only
 */
export const AuthenticatedOnly: React.FC<{
  children: React.ReactNode;
  fallback?: React.ReactNode;
}> = ({ children, fallback = null }) => {
  const hasAccess = useHasAccess({
    roles: [],
    permissions: [],
    allowSuperAdmin: true,
  });

  return hasAccess
    ? (children as React.ReactElement)
    : (fallback as React.ReactElement);
};

/**
 * Convenience component for admin users (superadmin or user management permission)
 */
export const AdminOnly: React.FC<{
  children: React.ReactNode;
  fallback?: React.ReactNode;
}> = ({ children, fallback = null }) => (
  <RoleGuard
    permissions={["user:manage", "role:manage", "system:access"]}
    requireAll={false}
    allowSuperAdmin={true}
    fallback={fallback}
  >
    {children}
  </RoleGuard>
);

/**
 * Convenience component for users with search access
 */
export const SearchAccessOnly: React.FC<{
  children: React.ReactNode;
  fallback?: React.ReactNode;
}> = ({ children, fallback = null }) => (
  <RoleGuard
    permissions={["products.search"]}
    allowSuperAdmin={true}
    fallback={fallback}
  >
    {children}
  </RoleGuard>
);

/**
 * Convenience component for users with shopping list access
 */
export const ShoppingListAccessOnly: React.FC<{
  children: React.ReactNode;
  fallback?: React.ReactNode;
}> = ({ children, fallback = null }) => (
  <RoleGuard
    permissions={["shopping_lists.read"]}
    allowSuperAdmin={true}
    fallback={fallback}
  >
    {children}
  </RoleGuard>
);

/**
 * Convenience component for users with product view access
 */
export const ProductAccessOnly: React.FC<{
  children: React.ReactNode;
  fallback?: React.ReactNode;
}> = ({ children, fallback = null }) => (
  <RoleGuard
    permissions={["products.read"]}
    allowSuperAdmin={true}
    fallback={fallback}
  >
    {children}
  </RoleGuard>
);

/**
 * Convenience component for user management access
 */
export const UserManagementOnly: React.FC<{
  children: React.ReactNode;
  fallback?: React.ReactNode;
}> = ({ children, fallback = null }) => (
  <RoleGuard
    permissions={["user:manage"]}
    allowSuperAdmin={true}
    fallback={fallback}
  >
    {children}
  </RoleGuard>
);

/**
 * Convenience component for role management access
 */
export const RoleManagementOnly: React.FC<{
  children: React.ReactNode;
  fallback?: React.ReactNode;
}> = ({ children, fallback = null }) => (
  <RoleGuard
    permissions={["role:manage"]}
    allowSuperAdmin={true}
    fallback={fallback}
  >
    {children}
  </RoleGuard>
);

export default RoleGuard;

import { test, expect } from "@playwright/test";

const BASE_URL = "http://localhost:3000";

test.describe("Product Detail Fix Verification", () => {
  test.beforeEach(async ({ page }) => {
    // Navigate to search page
    await page.goto(`${BASE_URL}/search`);
    await page.waitForLoadState("networkidle");
  });

  test("should display product details when clicking on product suggestions", async ({
    page,
  }) => {
    // Search for products
    await page.getByRole("textbox", { name: "Search dental products..." }).fill("bur");
    
    // Wait for suggestions to load
    await page.waitForTimeout(2000);
    
    // Click on the first product suggestion
    await page.getByRole("button", { name: /Tungsten Vanadium Steel Bur/ }).first().click();
    
    // Verify product details are displayed (not error)
    await expect(page.getByRole("heading", { name: "Product Details" })).toBeVisible();
    await expect(page.getByText("Product Not Found")).not.toBeVisible();
    
    // Verify product information is displayed
    await expect(page.getByText("Tungsten Vanadium Steel Bur")).toBeVisible();
    await expect(page.getByText("MFR:")).toBeVisible();
    await expect(page.getByText("Product ID:")).toBeVisible();
    
    // Take screenshot for verification
    await page.screenshot({ path: "product-detail-success.png" });
  });

  test("should handle product suggestions with ID-based lookup", async ({
    page,
  }) => {
    // Search for products
    await page.getByRole("textbox", { name: "Search dental products..." }).fill("bur");
    
    // Wait for suggestions to load
    await page.waitForTimeout(2000);
    
    // Verify suggestions are displayed
    await expect(page.getByRole("heading", { name: "Suggestions" })).toBeVisible();
    
    // Click on a different product suggestion
    await page.getByRole("button", { name: /Bur Wrench/ }).first().click();
    
    // Verify product details are displayed
    await expect(page.getByRole("heading", { name: "Product Details" })).toBeVisible();
    await expect(page.getByText("Product Not Found")).not.toBeVisible();
    
    // Verify we can close the product details
    await page.getByRole("button", { name: "Close product details" }).click();
    
    // Verify we're back to search interface
    await expect(page.getByRole("heading", { name: "Search Products" })).toBeVisible();
  });

  test("should preserve search functionality after fix", async ({ page }) => {
    // Search for products
    await page.getByRole("textbox", { name: "Search dental products..." }).fill("instrument");
    
    // Wait for suggestions
    await page.waitForTimeout(2000);
    
    // Verify suggestions are displayed
    await expect(page.getByRole("heading", { name: "Suggestions" })).toBeVisible();
    
    // Verify multiple suggestions are shown
    const suggestions = page.locator('[role="button"]').filter({ hasText: /instrument/i });
    await expect(suggestions.first()).toBeVisible();
    
    // Verify add to list buttons are present
    const addButtons = page.locator('button:has-text("+")');
    await expect(addButtons.first()).toBeVisible();
  });
});

import { Link } from "react-router-dom";
import {
  Search,
  TrendingUp,
  ShoppingCart,
  Zap,
  Database,
  Shield,
} from "lucide-react";
import UnifiedSearchInterface from "../components/search/UnifiedSearchInterface";
import ErrorBoundary from "../components/ErrorBoundary";
import LoadingOverlay from "../components/LoadingOverlay";
import { useInitialLoading } from "../hooks/useInitialLoading";
import { usePopularSearches } from "../hooks/useSearch";
import { useSearchStore } from "../stores/searchStore";

const HomePage = () => {
  const { data: popularData } = usePopularSearches();
  const { addRecentSearch, setQuery } = useSearchStore();

  // Initial loading state
  const { isLoading, progress, message } = useInitialLoading();

  const popularSearches = popularData?.popular_searches || [];

  const handlePopularSearchClick = (term: string) => {
    setQuery(term);
    addRecentSearch(term);
  };

  const features = [
    {
      icon: Zap,
      title: "Lightning Fast Search",
      description:
        "Search through 339K+ dental products in milliseconds with our optimized full-text search.",
    },
    {
      icon: Database,
      title: "Comprehensive Database",
      description:
        "214 categories, 3.3K+ brands across 10 major dental suppliers in one place.",
    },
    {
      icon: ShoppingCart,
      title: "Smart Shopping Lists",
      description:
        "Create and manage shopping lists with price analysis and supplier comparison.",
    },
    {
      icon: Shield,
      title: "Secure & Reliable",
      description:
        "Your data is protected with enterprise-grade security and reliable uptime.",
    },
  ];

  const stats = [
    { label: "Products", value: "339K+" },
    { label: "Categories", value: "214" },
    { label: "Brands", value: "3.3K+" },
    { label: "Suppliers", value: "10" },
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50">
      {/* Initial Loading Overlay */}
      <LoadingOverlay
        isVisible={isLoading}
        message={message}
        progress={progress}
      />
      {/* Hero Section */}
      <div className="relative overflow-hidden">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pt-20 pb-16">
          <div className="text-center">
            <h1 className="text-4xl sm:text-5xl lg:text-6xl font-bold text-gray-900 mb-6">
              Find the Right
              <span className="text-primary-600 block">Dental Products</span>
            </h1>
            <p className="text-xl text-gray-600 mb-8 max-w-3xl mx-auto">
              Search, compare, and manage dental products from multiple
              suppliers. Save time and money with our comprehensive product
              database.
            </p>

            {/* Unified Search Interface */}
            <div className="max-w-4xl mx-auto mb-8">
              <ErrorBoundary>
                <UnifiedSearchInterface />
              </ErrorBoundary>
            </div>

            {/* Quick Actions */}
            <div className="flex flex-wrap justify-center gap-4 mb-12">
              <Link
                to="/search"
                className="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 transition-colors"
              >
                <Search className="h-5 w-5 mr-2" />
                Advanced Search
              </Link>
              <Link
                to="/shopping-lists"
                className="inline-flex items-center px-6 py-3 border border-gray-300 text-base font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 transition-colors"
              >
                <ShoppingCart className="h-5 w-5 mr-2" />
                My Lists
              </Link>
            </div>
          </div>
        </div>
      </div>

      {/* Stats Section */}
      <div className="bg-white py-12 border-y border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
            {stats.map((stat) => (
              <div key={stat.label} className="text-center">
                <div className="text-3xl font-bold text-primary-600 mb-2">
                  {stat.value}
                </div>
                <div className="text-sm text-gray-600 uppercase tracking-wide">
                  {stat.label}
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Features Section */}
      <div className="py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">
              Why Choose ProfiDent?
            </h2>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              Built specifically for dental professionals who need fast,
              reliable access to product information.
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            {features.map((feature) => (
              <div key={feature.title} className="text-center">
                <div className="inline-flex items-center justify-center w-12 h-12 bg-primary-100 rounded-lg mb-4">
                  <feature.icon className="h-6 w-6 text-primary-600" />
                </div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2">
                  {feature.title}
                </h3>
                <p className="text-gray-600">{feature.description}</p>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Popular Searches */}
      {popularSearches.length > 0 && (
        <div className="bg-gray-50 py-16">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-8">
              <h2 className="text-2xl font-bold text-gray-900 mb-4">
                <TrendingUp className="inline h-6 w-6 mr-2" />
                Popular Searches
              </h2>
              <p className="text-gray-600">
                See what other dental professionals are searching for
              </p>
            </div>

            <div className="flex flex-wrap justify-center gap-3">
              {popularSearches.slice(0, 12).map((search, index) => (
                <button
                  key={`popular-${index}-${search.term}`}
                  onClick={() => handlePopularSearchClick(search.term)}
                  className="inline-flex items-center px-4 py-2 bg-white border border-gray-200 rounded-full text-sm text-gray-700 hover:bg-gray-50 hover:border-gray-300 transition-colors"
                >
                  {search.term}
                </button>
              ))}
            </div>
          </div>
        </div>
      )}

      {/* CTA Section */}
      <div className="bg-primary-600 py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl font-bold text-white mb-4">
            Ready to streamline your dental supply management?
          </h2>
          <p className="text-xl text-primary-100 mb-8 max-w-2xl mx-auto">
            Join thousands of dental professionals who trust ProfiDent for their
            product search needs.
          </p>
          <Link
            to="/search"
            className="inline-flex items-center px-8 py-4 border border-transparent text-lg font-medium rounded-md text-primary-600 bg-white hover:bg-gray-50 transition-colors"
          >
            Start Searching Now
            <Search className="ml-2 h-5 w-5" />
          </Link>
        </div>
      </div>
    </div>
  );
};

export default HomePage;

#!/usr/bin/env python3
"""
Integration Tests for ProfiDent API
Tests the complete API workflows and database interactions
"""

import asyncio
import sys
import time
from pathlib import Path
from fastapi.testclient import TestClient

# Add the app directory to Python path
sys.path.insert(0, str(Path(__file__).parent / "app"))

from app.main import app
from app.database import DatabaseManager


class TestIntegration:
    """Integration tests for the complete API."""

    def __init__(self):
        self.client = TestClient(app)
        self.test_user_email = None
        self.test_user_token = None
        self.test_shopping_list_id = None
    
    def test_complete_user_workflow(self):
        """Test complete user registration, login, and usage workflow."""
        print("🧪 Testing complete user workflow...")

        # Generate unique email for this test
        unique_email = f"integration_test_{int(time.time())}@example.com"
        self.test_user_email = unique_email

        # 1. Register user
        register_data = {
            "email": unique_email,
            "password": "TestPassword123!",
            "full_name": "Integration Test User"
        }

        response = self.client.post("/api/v1/auth/register", json=register_data)
        assert response.status_code == 201, f"Registration failed: {response.status_code} - {response.text}"

        register_result = response.json()
        assert "user" in register_result, "No user in registration response"
        assert "token" in register_result, "No token in registration response"
        assert register_result["user"]["email"] == unique_email, "Wrong email in response"

        self.test_user_token = register_result["token"]["access_token"]
        headers = {"Authorization": f"Bearer {self.test_user_token}"}

        print("  ✅ User registration successful")

        # 2. Test authenticated endpoints
        response = self.client.get("/api/v1/auth/me", headers=headers)
        assert response.status_code == 200, f"Get current user failed: {response.status_code}"
        user_data = response.json()
        assert user_data["email"] == unique_email, "Wrong user data returned"

        print("  ✅ User authentication working")
        
        # 3. Test search functionality
        response = client.get("/api/v1/search/?q=dental", headers=headers)
        assert response.status_code == 200
        search_result = response.json()
        assert "results" in search_result
        assert search_result["total"] > 0
        
        # 4. Test product endpoints
        response = client.get("/api/v1/products/categories/", headers=headers)
        assert response.status_code == 200
        categories_result = response.json()
        assert "categories" in categories_result
        
        # 5. Test shopping list creation
        list_data = {
            "name": "Integration Test List",
            "description": "Created during integration testing"
        }
        response = client.post("/api/v1/shopping-lists/", json=list_data, headers=headers)
        assert response.status_code == 201
        list_result = response.json()
        assert list_result["name"] == list_data["name"]
        
        shopping_list_id = list_result["id"]
        
        # 6. Test adding item to shopping list
        # First get a product ID
        response = client.get("/api/v1/search/?q=dental&limit=1", headers=headers)
        search_result = response.json()
        if search_result["results"]:
            product_id = search_result["results"][0]["id"]
            
            item_data = {
                "product_id": product_id,
                "quantity": 2,
                "notes": "Integration test item"
            }
            response = client.post(f"/api/v1/shopping-lists/{shopping_list_id}/items", 
                                 json=item_data, headers=headers)
            assert response.status_code == 201
            item_result = response.json()
            assert item_result["product_id"] == product_id
            assert item_result["quantity"] == 2
        
        # 7. Test shopping list analysis
        response = client.get(f"/api/v1/shopping-lists/{shopping_list_id}/analysis", headers=headers)
        assert response.status_code == 200
        analysis_result = response.json()
        assert "analysis" in analysis_result
        
        print("✅ Complete user workflow test passed")
    
    def test_search_performance_and_accuracy(self, client):
        """Test search performance and result accuracy."""
        # Create a test user for authenticated requests
        import time
        unique_email = f"search_test_{int(time.time())}@example.com"
        
        register_data = {
            "email": unique_email,
            "password": "TestPassword123!",
            "full_name": "Search Test User"
        }
        
        response = client.post("/api/v1/auth/register", json=register_data)
        assert response.status_code == 201
        
        access_token = response.json()["token"]["access_token"]
        headers = {"Authorization": f"Bearer {access_token}"}
        
        # Test various search scenarios
        search_tests = [
            {"query": "dental", "min_results": 1000},
            {"query": "implant", "min_results": 100},
            {"query": "crown", "min_results": 100},
            {"query": "orthodontic", "min_results": 50},
            {"query": "endodontic", "min_results": 50},
        ]
        
        for test in search_tests:
            response = client.get(f"/api/v1/search/?q={test['query']}", headers=headers)
            assert response.status_code == 200
            
            result = response.json()
            assert result["total"] >= test["min_results"], f"Query '{test['query']}' returned {result['total']} results, expected at least {test['min_results']}"
            assert result["search_time_ms"] < 1000, f"Search took {result['search_time_ms']}ms, should be under 1000ms"
            
            # Verify result structure
            assert "results" in result
            assert len(result["results"]) > 0
            
            # Check first result has required fields
            first_result = result["results"][0]
            assert "id" in first_result
            assert "name" in first_result
            assert "seller" in first_result
            
            print(f"✅ Search '{test['query']}': {result['total']} results in {result['search_time_ms']}ms")
    
    def test_data_consistency(self, client):
        """Test data consistency across different endpoints."""
        # Create test user
        import time
        unique_email = f"consistency_test_{int(time.time())}@example.com"
        
        register_data = {
            "email": unique_email,
            "password": "TestPassword123!",
            "full_name": "Consistency Test User"
        }
        
        response = client.post("/api/v1/auth/register", json=register_data)
        access_token = response.json()["token"]["access_token"]
        headers = {"Authorization": f"Bearer {access_token}"}
        
        # Get categories from products endpoint
        response = client.get("/api/v1/products/categories/", headers=headers)
        categories_result = response.json()
        categories = {cat["name"]: cat["count"] for cat in categories_result["categories"]}
        
        # Verify category counts by searching
        for category_name, expected_count in list(categories.items())[:5]:  # Test first 5 categories
            response = client.get(f"/api/v1/search/?category={category_name}&limit=1", headers=headers)
            search_result = response.json()
            
            # The total should match the category count
            assert search_result["total"] == expected_count, f"Category '{category_name}' count mismatch: search={search_result['total']}, categories={expected_count}"
            
            print(f"✅ Category '{category_name}' consistency verified: {expected_count} products")
        
        # Test seller consistency
        response = client.get("/api/v1/products/sellers/", headers=headers)
        sellers_result = response.json()
        sellers = {seller["name"]: seller["count"] for seller in sellers_result["sellers"]}
        
        for seller_name, expected_count in list(sellers.items())[:3]:  # Test first 3 sellers
            response = client.get(f"/api/v1/search/?seller={seller_name}&limit=1", headers=headers)
            search_result = response.json()
            
            assert search_result["total"] == expected_count, f"Seller '{seller_name}' count mismatch: search={search_result['total']}, sellers={expected_count}"
            
            print(f"✅ Seller '{seller_name}' consistency verified: {expected_count} products")
    
    def test_error_handling(self, client):
        """Test error handling across the API."""
        # Test unauthenticated requests
        response = client.get("/api/v1/auth/me")
        assert response.status_code == 401
        
        response = client.get("/api/v1/search/?q=dental")
        assert response.status_code == 401
        
        # Test invalid authentication
        headers = {"Authorization": "Bearer invalid_token"}
        response = client.get("/api/v1/auth/me", headers=headers)
        assert response.status_code == 401
        
        # Create valid user for further tests
        import time
        unique_email = f"error_test_{int(time.time())}@example.com"
        
        register_data = {
            "email": unique_email,
            "password": "TestPassword123!",
            "full_name": "Error Test User"
        }
        
        response = client.post("/api/v1/auth/register", json=register_data)
        access_token = response.json()["token"]["access_token"]
        headers = {"Authorization": f"Bearer {access_token}"}
        
        # Test invalid product ID
        response = client.get("/api/v1/products/999999999", headers=headers)
        assert response.status_code == 404
        
        # Test invalid shopping list ID
        response = client.get("/api/v1/shopping-lists/999999999", headers=headers)
        assert response.status_code == 404
        
        # Test invalid search parameters
        response = client.get("/api/v1/search/?q=", headers=headers)
        assert response.status_code == 422  # Empty query should be rejected
        
        # Test duplicate user registration
        response = client.post("/api/v1/auth/register", json=register_data)
        assert response.status_code == 400  # Should reject duplicate email
        
        print("✅ Error handling tests passed")
    
    def test_pagination_and_limits(self, client):
        """Test pagination and limit enforcement."""
        # Create test user
        import time
        unique_email = f"pagination_test_{int(time.time())}@example.com"
        
        register_data = {
            "email": unique_email,
            "password": "TestPassword123!",
            "full_name": "Pagination Test User"
        }
        
        response = client.post("/api/v1/auth/register", json=register_data)
        access_token = response.json()["token"]["access_token"]
        headers = {"Authorization": f"Bearer {access_token}"}
        
        # Test search pagination
        response = client.get("/api/v1/search/?q=dental&limit=10&offset=0", headers=headers)
        assert response.status_code == 200
        result = response.json()
        assert len(result["results"]) <= 10
        
        # Test limit enforcement
        response = client.get("/api/v1/search/?q=dental&limit=200", headers=headers)
        assert response.status_code == 422  # Should reject limit > 100
        
        # Test shopping lists pagination
        response = client.get("/api/v1/shopping-lists/?page=1&per_page=5", headers=headers)
        assert response.status_code == 200
        result = response.json()
        assert len(result["data"]["items"]) <= 5
        
        print("✅ Pagination and limits tests passed")


def run_integration_tests():
    """Run all integration tests."""
    print("🚀 Running ProfiDent Integration Tests")
    print("=" * 50)
    
    # Run pytest with this file
    import subprocess
    result = subprocess.run([
        sys.executable, "-m", "pytest", __file__, "-v", "--tb=short"
    ], capture_output=True, text=True)
    
    print(result.stdout)
    if result.stderr:
        print("STDERR:", result.stderr)
    
    return result.returncode == 0


if __name__ == "__main__":
    success = run_integration_tests()
    sys.exit(0 if success else 1)

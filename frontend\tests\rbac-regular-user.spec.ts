import { test, expect } from "@playwright/test";

/**
 * Regular User Role-Based Access Control Tests
 * 
 * Tests the frontend RBAC for regular users (non-admin) including:
 * - Limited access to admin features
 * - Basic user functionality access
 * - Proper permission restrictions
 * - UI element visibility based on user role
 */

const REGULAR_USER_CREDENTIALS = {
  email: "<EMAIL>",
  password: "Demo123!",
};

const BASE_URL = "http://localhost:3000";

test.describe.configure({ mode: "serial" });

test.describe("Regular User RBAC", () => {
  test.beforeEach(async ({ page }) => {
    // Clear any existing authentication
    await page.goto(BASE_URL);
    await page.evaluate(() => {
      localStorage.clear();
      sessionStorage.clear();
    });
    await page.waitForLoadState("networkidle");
  });

  test.describe("Regular User Access", () => {
    test.beforeEach(async ({ page }) => {
      // Login as regular user
      await page.goto(`${BASE_URL}/login`);
      await page.waitForLoadState("networkidle");

      await page
        .getByRole("textbox", { name: "Email address" })
        .fill(REGULAR_USER_CREDENTIALS.email);
      await page
        .getByRole("textbox", { name: "Password" })
        .fill(REGULAR_USER_CREDENTIALS.password);

      await Promise.all([
        page.waitForURL(`${BASE_URL}/`, { timeout: 15000 }),
        page.getByRole("button", { name: "Sign in" }).click(),
      ]);

      await page.waitForLoadState("networkidle");
    });

    test("should have access to basic user routes", async ({ page }) => {
      // Test search page access
      await page.goto(`${BASE_URL}/search`);
      await expect(page).toHaveURL(`${BASE_URL}/search`);
      await expect(
        page.getByRole("heading", { name: "Search Products" })
      ).toBeVisible();

      // Test shopping lists access
      await page.goto(`${BASE_URL}/shopping-lists`);
      await expect(page).toHaveURL(`${BASE_URL}/shopping-lists`);
      await expect(
        page.getByRole("heading", { name: "Shopping Lists" })
      ).toBeVisible();

      // Test profile access
      await page.goto(`${BASE_URL}/profile`);
      await expect(page).toHaveURL(`${BASE_URL}/profile`);
    });

    test("should NOT have access to admin routes", async ({ page }) => {
      // Test admin dashboard - should redirect or show access denied
      await page.goto(`${BASE_URL}/admin`);
      // Should either redirect to home or show access denied
      const currentUrl = page.url();
      expect(currentUrl === `${BASE_URL}/` || currentUrl === `${BASE_URL}/login`).toBeTruthy();

      // Test user management - should redirect or show access denied
      await page.goto(`${BASE_URL}/admin/users`);
      const userMgmtUrl = page.url();
      expect(userMgmtUrl === `${BASE_URL}/` || userMgmtUrl === `${BASE_URL}/login`).toBeTruthy();

      // Test role management - should redirect or show access denied
      await page.goto(`${BASE_URL}/admin/roles`);
      const roleMgmtUrl = page.url();
      expect(roleMgmtUrl === `${BASE_URL}/` || roleMgmtUrl === `${BASE_URL}/login`).toBeTruthy();
    });

    test("should NOT display admin navigation items", async ({ page }) => {
      // Check that admin navigation items are not visible
      await expect(
        page.getByRole("link", { name: "Admin Dashboard" })
      ).not.toBeVisible();
      await expect(
        page.getByRole("link", { name: "User Management" })
      ).not.toBeVisible();
      await expect(
        page.getByRole("link", { name: "Role Management" })
      ).not.toBeVisible();

      // Administration section should not be visible
      await expect(page.getByText("Administration")).not.toBeVisible();
    });

    test("should display regular user role in header dropdown", async ({ page }) => {
      // Open user dropdown menu
      await page.locator("header button").nth(2).click();

      // Check user information
      await expect(page.getByText("Demo User")).toBeVisible();
      await expect(page.getByText("<EMAIL>")).toBeVisible();

      // Check role badge - should show "User" or similar
      await expect(page.getByText("User")).toBeVisible();

      // Should NOT see admin navigation in dropdown
      await expect(
        page
          .locator(".absolute.right-0.mt-2")
          .getByRole("link", { name: "Admin Dashboard" })
      ).not.toBeVisible();
    });

    test("should display limited permissions in profile", async ({ page }) => {
      await page.goto(`${BASE_URL}/profile`);
      await page.waitForLoadState("networkidle");

      // Click on Roles & Permissions tab
      await page.getByRole("button", { name: "Roles & Permissions" }).click();
      await page.waitForTimeout(1000);

      // Check roles section
      await expect(
        page.getByRole("heading", { name: "Your Roles" })
      ).toBeVisible();
      await expect(page.getByText("User")).toBeVisible();

      // Check permission summary
      await expect(
        page.getByRole("heading", { name: "Permission Summary" })
      ).toBeVisible();

      // Should have basic permissions
      await expect(page.getByText("Search Products")).toBeVisible();
      await expect(page.getByText("View Product Details")).toBeVisible();
      await expect(page.getByText("View Lists")).toBeVisible();
      await expect(page.getByText("Create Lists")).toBeVisible();

      // Should NOT have administrative permissions
      await expect(
        page.locator("span").filter({ hasText: "User Management" })
      ).not.toBeVisible();
      await expect(
        page.locator("span").filter({ hasText: "Role Management" })
      ).not.toBeVisible();
      await expect(
        page.locator("span").filter({ hasText: "System Access" })
      ).not.toBeVisible();
    });

    test("should have functional search and shopping list features", async ({ page }) => {
      // Test search functionality
      await page.goto(`${BASE_URL}/search`);
      await expect(
        page.getByRole("textbox", { name: "Search dental products..." })
      ).toBeVisible();

      // Test shopping list functionality
      await page.goto(`${BASE_URL}/shopping-lists`);
      await expect(
        page.getByRole("button", { name: "New List" })
      ).toBeVisible();
    });

    test("should allow logout", async ({ page }) => {
      // Open user dropdown and logout
      await page.locator("header button").nth(2).click();
      await page.getByRole("button", { name: "Sign out" }).click();

      await page.waitForTimeout(1000);

      // Should stay on current page but be unauthenticated
      await expect(page).toHaveURL(`${BASE_URL}/`);

      // Should not be able to access protected routes after logout
      await page.goto(`${BASE_URL}/search`);
      await expect(page).toHaveURL(`${BASE_URL}/login`);
    });
  });
});

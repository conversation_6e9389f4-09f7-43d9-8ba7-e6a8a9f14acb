{"name": "net32-null-filler", "version": "1.0.0", "description": "Fill null values in Net32 data using Puppeteer with stealth", "main": "fill_null_values.js", "scripts": {"start": "node fill_null_values.js", "test": "echo \"Error: no test specified\" && exit 1"}, "dependencies": {"@angular/animations": "^20.1.3", "@angular/cdk": "^20.1.3", "@angular/material": "^20.1.3", "puppeteer": "^24.15.0", "puppeteer-extra": "^3.3.6", "puppeteer-extra-plugin-stealth": "^2.11.2"}, "keywords": ["puppeteer", "scraping", "stealth", "net32"], "author": "", "license": "ISC", "devDependencies": {"supabase": "^2.33.5"}}
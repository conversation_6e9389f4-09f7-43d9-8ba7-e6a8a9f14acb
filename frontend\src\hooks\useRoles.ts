import { useAuthStore } from "../stores/authStore";

/**
 * Custom hooks for role-based access control
 */

/**
 * Check if the current user has a specific role
 * @param role - The role name to check
 * @returns boolean - True if user has the role or is superadmin
 */
export const useHasRole = (role: string): boolean => {
  return useAuthStore((state) => state.hasRole(role));
};

/**
 * Check if the current user has any of the specified roles
 * @param roles - Array of role names to check
 * @returns boolean - True if user has any of the roles or is superadmin
 */
export const useHasAnyRole = (roles: string[]): boolean => {
  return useAuthStore((state) => state.hasAnyRole(roles));
};

/**
 * Check if the current user has a specific permission
 * @param permission - The permission name to check
 * @returns boolean - True if user has the permission or is superadmin
 */
export const useHasPermission = (permission: string): boolean => {
  return useAuthStore((state) => state.hasPermission(permission));
};

/**
 * Check if the current user has any of the specified permissions
 * @param permissions - Array of permission names to check
 * @returns boolean - True if user has any of the permissions or is superadmin
 */
export const useHasAnyPermission = (permissions: string[]): boolean => {
  return useAuthStore((state) => state.hasAnyPermission(permissions));
};

/**
 * Check if the current user is a superadmin
 * @returns boolean - True if user is superadmin
 */
export const useIsSuperAdmin = (): boolean => {
  return useAuthStore((state) => state.isSuperAdmin);
};

/**
 * Get the current user's roles
 * @returns string[] - Array of role names
 */
export const useUserRoles = (): string[] => {
  return useAuthStore((state) => state.userRoles);
};

/**
 * Get the current user's permissions
 * @returns string[] - Array of permission names
 */
export const useUserPermissions = (): string[] => {
  return useAuthStore((state) => state.userPermissions);
};

/**
 * Check if user is authenticated
 * @returns boolean - True if user is authenticated
 */
export const useIsAuthenticated = (): boolean => {
  return useAuthStore((state) => state.isAuthenticated);
};

/**
 * Get current user data
 * @returns User | null - Current user or null if not authenticated
 */
export const useCurrentUser = () => {
  return useAuthStore((state) => state.user);
};

/**
 * Composite hook for common admin checks
 * @returns object with common admin permission checks
 */
export const useAdminPermissions = () => {
  const isSuperAdmin = useIsSuperAdmin();
  const hasUserManagement = useHasPermission("user:manage");
  const hasRoleManagement = useHasPermission("role:manage");
  const hasSystemAccess = useHasPermission("system:access");

  return {
    isSuperAdmin,
    hasUserManagement,
    hasRoleManagement,
    hasSystemAccess,
    canAccessAdmin:
      isSuperAdmin || hasUserManagement || hasRoleManagement || hasSystemAccess,
  };
};

/**
 * Hook for search-related permissions
 * @returns object with search permission checks
 */
export const useSearchPermissions = () => {
  const isSuperAdmin = useIsSuperAdmin();
  const hasProductSearch = useHasPermission("products.search");
  const hasProductRead = useHasPermission("products.read");

  return {
    isSuperAdmin,
    hasProductSearch,
    hasProductRead,
    canSearch: isSuperAdmin || hasProductSearch,
    canViewProducts: isSuperAdmin || hasProductRead,
  };
};

/**
 * Hook for shopping list permissions
 * @returns object with shopping list permission checks
 */
export const useShoppingListPermissions = () => {
  const isSuperAdmin = useIsSuperAdmin();
  const hasRead = useHasPermission("shopping_lists.read");
  const hasCreate = useHasPermission("shopping_lists.create");
  const hasUpdate = useHasPermission("shopping_lists.update");
  const hasDelete = useHasPermission("shopping_lists.delete");

  return {
    isSuperAdmin,
    hasRead,
    hasCreate,
    hasUpdate,
    hasDelete,
    canRead: isSuperAdmin || hasRead,
    canCreate: isSuperAdmin || hasCreate,
    canUpdate: isSuperAdmin || hasUpdate,
    canDelete: isSuperAdmin || hasDelete,
    canManage: isSuperAdmin || hasCreate || hasUpdate || hasDelete,
  };
};

/**
 * Hook for checking multiple role/permission combinations
 * @param config - Configuration object for role/permission checks
 * @returns boolean - True if any of the conditions are met
 */
export const useHasAccess = (config: {
  roles?: string[];
  permissions?: string[];
  requireAll?: boolean;
  allowSuperAdmin?: boolean;
}): boolean => {
  const {
    roles = [],
    permissions = [],
    requireAll = false,
    allowSuperAdmin = true,
  } = config;

  const isSuperAdmin = useIsSuperAdmin();
  const userRoles = useUserRoles();
  const userPermissions = useUserPermissions();

  // Superadmin bypass
  if (allowSuperAdmin && isSuperAdmin) {
    return true;
  }

  // Check roles
  const hasRequiredRoles = requireAll
    ? roles.every((role) => userRoles.includes(role))
    : roles.some((role) => userRoles.includes(role));

  // Check permissions
  const hasRequiredPermissions = requireAll
    ? permissions.every((permission) => userPermissions.includes(permission))
    : permissions.some((permission) => userPermissions.includes(permission));

  // If both roles and permissions are specified
  if (roles.length > 0 && permissions.length > 0) {
    return requireAll
      ? hasRequiredRoles && hasRequiredPermissions
      : hasRequiredRoles || hasRequiredPermissions;
  }

  // If only roles are specified
  if (roles.length > 0) {
    return hasRequiredRoles;
  }

  // If only permissions are specified
  if (permissions.length > 0) {
    return hasRequiredPermissions;
  }

  // No requirements specified
  return true;
};

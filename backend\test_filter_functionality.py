#!/usr/bin/env python3
"""
Test Filter Functionality
Test the new homepage filters and API endpoints
"""

import asyncio
from playwright.async_api import async_playwright


async def test_filter_functionality():
    """Test the new filter functionality."""
    print("🔧 Testing Filter Functionality")
    
    async with async_playwright() as p:
        browser = await p.chromium.launch(headless=False, slow_mo=300)
        page = await browser.new_page()
        
        # Track network requests
        network_requests = []
        page.on("response", lambda response: print(f"🌐 {response.status} {response.url}"))
        
        try:
            # Navigate to homepage
            print("📍 Loading homepage...")
            await page.goto("http://localhost:3000", wait_until="networkidle")
            await page.wait_for_timeout(3000)
            
            # Take screenshot of homepage
            await page.screenshot(path="debug_screenshots/homepage_with_filters.png")
            
            # Check if Advanced Filters section is visible
            filters_section = page.locator('h2:has-text("Find Exactly What You Need")')
            if await filters_section.is_visible():
                print("✅ Advanced Filters section found on homepage")
            else:
                print("❌ Advanced Filters section not found")
                return False
            
            # Test Categories dropdown
            print("🔍 Testing Categories dropdown...")
            categories_dropdown = page.locator('button:has-text("Categories")')
            if await categories_dropdown.is_visible():
                await categories_dropdown.click()
                await page.wait_for_timeout(2000)
                
                # Check if dropdown opened and has options
                dropdown_options = page.locator('button:has-text("Instruments")')
                if await dropdown_options.is_visible():
                    print("✅ Categories dropdown working - found 'Instruments' option")
                    await dropdown_options.click()
                    await page.wait_for_timeout(1000)
                else:
                    print("❌ Categories dropdown not showing options")
            else:
                print("❌ Categories dropdown not found")
            
            # Test Brands dropdown
            print("🔍 Testing Brands dropdown...")
            brands_dropdown = page.locator('button:has-text("Brands")')
            if await brands_dropdown.is_visible():
                await brands_dropdown.click()
                await page.wait_for_timeout(2000)
                
                # Check if dropdown opened and has options
                dropdown_options = page.locator('button:has-text("Hu-Friedy")')
                if await dropdown_options.is_visible():
                    print("✅ Brands dropdown working - found 'Hu-Friedy' option")
                    await dropdown_options.click()
                    await page.wait_for_timeout(1000)
                else:
                    print("❌ Brands dropdown not showing options")
            else:
                print("❌ Brands dropdown not found")
            
            # Test search with filters
            print("🔍 Testing search with filters...")
            search_button = page.locator('button:has-text("Search with Filters")')
            if await search_button.is_visible():
                await search_button.click()
                await page.wait_for_load_state("networkidle")
                await page.wait_for_timeout(3000)
                
                # Check if we're on search page with filters
                current_url = page.url
                print(f"📍 Current URL after filter search: {current_url}")
                
                if "/search" in current_url and ("category=" in current_url or "brand=" in current_url):
                    print("✅ Filter search working - navigated to search page with filters")
                    
                    # Take screenshot of search results
                    await page.screenshot(path="debug_screenshots/search_with_filters.png")
                    
                    # Check if results are displayed
                    results_text = page.locator('text=results')
                    if await results_text.is_visible():
                        print("✅ Search results displayed with filters")
                        return True
                    else:
                        print("❌ Search results not displayed")
                        return False
                else:
                    print("❌ Filter search not working properly")
                    return False
            else:
                print("❌ Search button not found")
                return False
                
        except Exception as e:
            print(f"❌ Test failed: {e}")
            await page.screenshot(path="debug_screenshots/filter_test_error.png")
            import traceback
            traceback.print_exc()
            return False
        finally:
            await browser.close()


if __name__ == "__main__":
    success = asyncio.run(test_filter_functionality())
    if success:
        print("\n🎉 Filter functionality working!")
    else:
        print("\n❌ Filter functionality has issues")

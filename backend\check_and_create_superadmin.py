#!/usr/bin/env python3
"""
Check existing superadmin users and create the requested "superyzn" superadmin.
"""

import asyncio
import asyncpg
from app.config import settings
from app.services.auth_service import AuthService
from app.schemas.auth import UserCreate
from app.models.user import UserRepository


async def check_and_create_superadmin():
    """Check existing superadmin users and create superyzn superadmin."""
    print("🔍 Checking Existing Superadmin Users and Creating 'superyzn' Superadmin")
    print("=" * 70)
    
    try:
        # Connect to database
        db_url = settings.DATABASE_URL.replace('postgresql+asyncpg://', 'postgresql://')
        conn = await asyncpg.connect(db_url)
        print("✅ Database connection successful")
        
        # Check existing superadmin users
        print("\n🔍 Checking existing superadmin users...")
        
        superadmin_users = await conn.fetch("""
            SELECT id, email, full_name, is_superuser, is_active, created_at
            FROM users 
            WHERE is_superuser = TRUE
            ORDER BY created_at
        """)
        
        if superadmin_users:
            print(f"✅ Found {len(superadmin_users)} existing superadmin user(s):")
            for user in superadmin_users:
                status = "Active" if user['is_active'] else "Inactive"
                print(f"  - {user['email']} ({user['full_name']}) - {status} - Created: {user['created_at']}")
        else:
            print("ℹ️  No existing superadmin users found")
        
        # Check if superyzn already exists
        print("\n🔍 Checking if 'superyzn' user already exists...")

        existing_superyzn = await conn.fetchrow("""
            SELECT id, email, full_name, is_superuser, is_active
            FROM users
            WHERE email = '<EMAIL>'
        """)

        if existing_superyzn:
            print(f"ℹ️  User '<EMAIL>' already exists:")
            print(f"   - Email: {existing_superyzn['email']}")
            print(f"   - Full Name: {existing_superyzn['full_name']}")
            print(f"   - Is Superuser: {existing_superyzn['is_superuser']}")
            print(f"   - Is Active: {existing_superyzn['is_active']}")

            if not existing_superyzn['is_superuser']:
                print("\n🔧 Updating existing user to superadmin...")
                await conn.execute("""
                    UPDATE users
                    SET is_superuser = TRUE, updated_at = NOW()
                    WHERE email = '<EMAIL>'
                """)
                print("✅ User updated to superadmin status")
            else:
                print("✅ User is already a superadmin")
        else:
            print("ℹ️  User 'superyzn' does not exist, creating new superadmin...")

            # Create superyzn superadmin user
            user_data = UserCreate(
                email="<EMAIL>",
                password="SuperYzn123!",  # Strong password
                full_name="Super YZN Admin"
            )
            
            try:
                # Register the user first
                user, tokens = await AuthService.register_user(conn, user_data)
                print(f"✅ User created: {user.email}")
                
                # Update to superadmin
                await conn.execute("""
                    UPDATE users 
                    SET is_superuser = TRUE, updated_at = NOW()
                    WHERE id = $1
                """, user.id)
                print("✅ User updated to superadmin status")
                
                # Assign superadmin role
                role_assigned = await UserRepository.assign_role_to_user_by_name(
                    conn=conn,
                    user_id=user.id,
                    role_name="superadmin",
                    assigned_by=None  # System assignment
                )
                
                if role_assigned:
                    print("✅ Superadmin role assigned successfully")
                else:
                    print("⚠️  Warning: Failed to assign superadmin role")
                
                print(f"\n🎉 Superadmin 'superyzn' created successfully!")
                print(f"   - Email: <EMAIL>")
                print(f"   - Password: SuperYzn123!")
                print(f"   - Full Name: Super YZN Admin")
                print(f"   - User ID: {user.id}")
                
            except Exception as e:
                print(f"❌ Failed to create superyzn user: {e}")
                return False
        
        # Verify superadmin roles and permissions
        print("\n🔍 Verifying superadmin roles and permissions...")
        
        superyzn_user = await conn.fetchrow("""
            SELECT id, email, full_name, is_superuser
            FROM users
            WHERE email = '<EMAIL>'
        """)
        
        if superyzn_user:
            user_id = superyzn_user['id']
            
            # Check roles
            user_roles = await conn.fetch("""
                SELECT r.name as role_name, ur.is_active
                FROM user_roles ur
                JOIN roles r ON ur.role_id = r.id
                WHERE ur.user_id = $1 AND ur.is_active = TRUE
                ORDER BY r.name
            """, user_id)
            
            print(f"✅ User roles: {[role['role_name'] for role in user_roles]}")
            
            # Check permissions (through roles)
            user_permissions = await conn.fetch("""
                SELECT DISTINCT p.name as permission_name
                FROM user_roles ur
                JOIN role_permissions rp ON ur.role_id = rp.role_id
                JOIN permissions p ON rp.permission_id = p.id
                WHERE ur.user_id = $1 AND ur.is_active = TRUE AND rp.is_active = TRUE
                ORDER BY p.name
            """, user_id)
            
            print(f"✅ User permissions: {len(user_permissions)} permissions")
            for perm in user_permissions[:10]:  # Show first 10
                print(f"   - {perm['permission_name']}")
            if len(user_permissions) > 10:
                print(f"   ... and {len(user_permissions) - 10} more")
        
        # Final summary
        print("\n📊 Final Superadmin Summary:")
        final_superadmins = await conn.fetch("""
            SELECT email, full_name, is_superuser, is_active
            FROM users 
            WHERE is_superuser = TRUE AND is_active = TRUE
            ORDER BY email
        """)
        
        print(f"✅ Total active superadmin users: {len(final_superadmins)}")
        for admin in final_superadmins:
            print(f"   - {admin['email']} ({admin['full_name']})")
        
        await conn.close()
        print("\n📡 Database connection closed")
        
        return True
        
    except Exception as e:
        print(f"❌ Operation failed with error: {e}")
        return False


if __name__ == "__main__":
    success = asyncio.run(check_and_create_superadmin())
    exit(0 if success else 1)

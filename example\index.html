<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ProfiDent - Product Search & Comparison</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <!-- Loading Screen -->
    <div id="loading-screen" class="loading-screen">
        <div class="loading-content">
            <div class="loading-spinner"></div>
            <h2>Loading Product Database</h2>
            <p id="loading-status">Initializing...</p>
            <div class="loading-progress">
                <div class="progress-bar" id="progress-bar"></div>
            </div>
        </div>
    </div>

    <!-- Main Application -->
    <div id="app" class="app hidden">
        <!-- Header -->
        <header class="header">
            <div class="container">
                <div class="header-content">
                    <h1 class="logo">
                        <i class="fas fa-tooth"></i>
                        ProfiDent
                    </h1>
                    <p class="tagline">Search & Compare Dental Products Across 10 Sellers</p>
                </div>
            </div>
        </header>

        <!-- Search Section -->
        <section class="search-section">
            <div class="container">
                <div class="search-container">
                    <div class="search-input-wrapper">
                        <i class="fas fa-search search-icon"></i>
                        <input 
                            type="text" 
                            id="search-input" 
                            class="search-input" 
                            placeholder="Search for dental products..."
                            autocomplete="off"
                        >
                        <button id="clear-search" class="clear-search hidden">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                    
                    <!-- Search Stats -->
                    <div id="search-stats" class="search-stats hidden">
                        <span id="stats-text"></span>
                    </div>
                </div>
            </div>
        </section>

        <!-- Search Results -->
        <section class="results-section">
            <div class="container">
                <!-- Search Suggestions -->
                <div id="search-suggestions" class="search-suggestions hidden">
                    <div class="suggestions-header">
                        <h3>Search Results</h3>
                        <span id="results-count"></span>
                    </div>
                    <div id="suggestions-list" class="suggestions-list"></div>
                </div>

                <!-- Product Detail View -->
                <div id="product-detail" class="product-detail hidden">
                    <div class="detail-header">
                        <button id="back-to-search" class="back-button">
                            <i class="fas fa-arrow-left"></i>
                            Back to Search
                        </button>
                        <h2>Product Details</h2>
                    </div>
                    
                    <div class="detail-content">
                        <!-- Selected Product -->
                        <div id="selected-product" class="selected-product"></div>
                        
                        <!-- Comparison Products -->
                        <div id="comparison-section" class="comparison-section">
                            <h3 class="comparison-title">
                                <i class="fas fa-balance-scale"></i>
                                Available from Other Sellers
                            </h3>
                            <div id="comparison-grid" class="comparison-grid"></div>
                        </div>
                    </div>
                </div>

                <!-- No Results -->
                <div id="no-results" class="no-results hidden">
                    <div class="no-results-content">
                        <i class="fas fa-search"></i>
                        <h3>No products found</h3>
                        <p>Try adjusting your search terms or check for typos.</p>
                    </div>
                </div>
            </div>
        </section>

        <!-- Database Stats -->
        <section class="stats-section">
            <div class="container">
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-number" id="total-products">-</div>
                        <div class="stat-label">Total Products</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number" id="unique-products">-</div>
                        <div class="stat-label">Unique Products</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number" id="sellers-count">10</div>
                        <div class="stat-label">Sellers</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number" id="overlap-percentage">-</div>
                        <div class="stat-label">Multi-Seller Products</div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Footer -->
        <footer class="footer">
            <div class="container">
                <p>&copy; 2024 ProfiDent. Product search and comparison tool.</p>
            </div>
        </footer>
    </div>

    <script src="app.js"></script>
</body>
</html>

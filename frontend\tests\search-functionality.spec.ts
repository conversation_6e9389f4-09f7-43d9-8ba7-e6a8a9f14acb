import { test, expect } from '@playwright/test';

test.describe('Search Functionality Tests', () => {
  test.beforeEach(async ({ page }) => {
    // Navigate to the homepage
    await page.goto('http://localhost:3000');
    
    // Wait for the page to load
    await page.waitForLoadState('networkidle');
  });

  test('should display search suggestions when typing', async ({ page }) => {
    console.log('Testing search suggestions...');
    
    // Find the search input
    const searchInput = page.locator('input[placeholder*="Search"]').first();
    await expect(searchInput).toBeVisible();
    
    // Type in the search input
    await searchInput.fill('dental');
    
    // Wait for suggestions to appear
    await page.waitForTimeout(1000);
    
    // Take a screenshot
    await page.screenshot({ path: 'debug_screenshots/search_suggestions.png', fullPage: true });
    
    // Check if suggestions are visible
    const suggestions = page.locator('[data-testid="search-suggestions"], .suggestion, .search-suggestion').first();
    
    // Wait a bit more for suggestions to load
    await page.waitForTimeout(2000);
    
    // Take another screenshot
    await page.screenshot({ path: 'debug_screenshots/search_suggestions_after_wait.png', fullPage: true });
    
    console.log('Search suggestions test completed');
  });

  test('should perform search and show results', async ({ page }) => {
    console.log('Testing search results...');
    
    // Find the search input
    const searchInput = page.locator('input[placeholder*="Search"]').first();
    await expect(searchInput).toBeVisible();
    
    // Type and press Enter
    await searchInput.fill('dental');
    await searchInput.press('Enter');
    
    // Wait for navigation to search results page
    await page.waitForURL('**/search**');
    await page.waitForLoadState('networkidle');
    
    // Take a screenshot
    await page.screenshot({ path: 'debug_screenshots/search_results_page.png', fullPage: true });
    
    // Check if results are displayed
    const resultsContainer = page.locator('[data-testid="search-results"], .search-results, .results').first();
    
    // Wait for results to load
    await page.waitForTimeout(3000);
    
    // Take final screenshot
    await page.screenshot({ path: 'debug_screenshots/search_results_final.png', fullPage: true });
    
    console.log('Search results test completed');
  });

  test('should test API endpoints directly', async ({ page }) => {
    console.log('Testing API endpoints...');
    
    // Test search suggestions API
    const suggestionsResponse = await page.request.get('http://localhost:8000/api/v1/search/suggestions?q=dental');
    expect(suggestionsResponse.status()).toBe(200);
    
    const suggestionsData = await suggestionsResponse.json();
    console.log('Suggestions API response:', suggestionsData);
    expect(suggestionsData.suggestions).toBeDefined();
    expect(Array.isArray(suggestionsData.suggestions)).toBe(true);
    
    // Test search results API
    const searchResponse = await page.request.get('http://localhost:8000/api/v1/search/?q=dental&limit=10&offset=0');
    expect(searchResponse.status()).toBe(200);
    
    const searchData = await searchResponse.json();
    console.log('Search API response:', searchData);
    expect(searchData.results).toBeDefined();
    expect(Array.isArray(searchData.results)).toBe(true);
    expect(searchData.total).toBeGreaterThan(0);
    
    // Test popular searches API
    const popularResponse = await page.request.get('http://localhost:8000/api/v1/search/popular?limit=5');
    expect(popularResponse.status()).toBe(200);
    
    const popularData = await popularResponse.json();
    console.log('Popular searches API response:', popularData);
    expect(popularData.popular_searches).toBeDefined();
    expect(Array.isArray(popularData.popular_searches)).toBe(true);
    
    console.log('API endpoints test completed successfully');
  });

  test('should test filter functionality', async ({ page }) => {
    console.log('Testing filter functionality...');
    
    // Test categories API
    const categoriesResponse = await page.request.get('http://localhost:8000/api/v1/products/categories/');
    expect(categoriesResponse.status()).toBe(200);
    
    const categoriesData = await categoriesResponse.json();
    console.log('Categories API response:', categoriesData);
    expect(categoriesData.categories).toBeDefined();
    expect(Array.isArray(categoriesData.categories)).toBe(true);
    
    // Test manufacturers API
    const manufacturersResponse = await page.request.get('http://localhost:8000/api/v1/products/manufacturers/');
    expect(manufacturersResponse.status()).toBe(200);
    
    const manufacturersData = await manufacturersResponse.json();
    console.log('Manufacturers API response:', manufacturersData);
    expect(manufacturersData.manufacturers).toBeDefined();
    expect(Array.isArray(manufacturersData.manufacturers)).toBe(true);
    
    // Test sellers API
    const sellersResponse = await page.request.get('http://localhost:8000/api/v1/products/sellers/');
    expect(sellersResponse.status()).toBe(200);
    
    const sellersData = await sellersResponse.json();
    console.log('Sellers API response:', sellersData);
    expect(sellersData.sellers).toBeDefined();
    expect(Array.isArray(sellersData.sellers)).toBe(true);
    
    console.log('Filter functionality test completed successfully');
  });

  test('should test complete search workflow', async ({ page }) => {
    console.log('Testing complete search workflow...');
    
    // Navigate to homepage
    await page.goto('http://localhost:3000');
    await page.waitForLoadState('networkidle');
    
    // Take initial screenshot
    await page.screenshot({ path: 'debug_screenshots/workflow_01_homepage.png', fullPage: true });
    
    // Find and interact with search input
    const searchInput = page.locator('input[placeholder*="Search"]').first();
    await expect(searchInput).toBeVisible();
    
    // Type search query
    await searchInput.fill('dental implant');
    await page.waitForTimeout(1000);
    
    // Take screenshot after typing
    await page.screenshot({ path: 'debug_screenshots/workflow_02_typed_query.png', fullPage: true });
    
    // Press Enter to search
    await searchInput.press('Enter');
    
    // Wait for search results page
    await page.waitForURL('**/search**');
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(2000);
    
    // Take final screenshot
    await page.screenshot({ path: 'debug_screenshots/workflow_03_search_results.png', fullPage: true });
    
    console.log('Complete search workflow test completed');
  });
});

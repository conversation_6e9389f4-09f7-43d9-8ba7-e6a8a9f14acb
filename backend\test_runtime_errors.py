#!/usr/bin/env python3
"""
Test Runtime Errors
Capture runtime errors from the browser console
"""

import asyncio
from playwright.async_api import async_playwright


async def test_runtime_errors():
    """Test for runtime errors."""
    print("🐛 Testing for Runtime Errors")
    
    async with async_playwright() as p:
        browser = await p.chromium.launch(headless=False, slow_mo=500)
        page = await browser.new_page()
        
        # Capture console messages
        console_messages = []
        page.on("console", lambda msg: console_messages.append(f"{msg.type}: {msg.text}"))
        page.on("pageerror", lambda error: print(f"❌ PAGE ERROR: {error}"))
        
        try:
            # Navigate to homepage
            print("📍 Loading homepage...")
            await page.goto("http://localhost:3000", wait_until="networkidle")
            await page.wait_for_timeout(5000)
            
            # Take screenshot
            await page.screenshot(path="debug_screenshots/runtime_error_check.png")
            
            # Check for error overlay
            error_overlay = page.locator('vite-error-overlay')
            if await error_overlay.is_visible():
                print("❌ Vite error overlay detected")
                
                # Try to get error details
                try:
                    error_content = await error_overlay.inner_text()
                    print(f"📝 Error overlay content: {error_content[:500]}...")
                except:
                    print("📝 Could not read error overlay content")
            else:
                print("✅ No error overlay detected")
            
            # Print console messages
            print("\n📋 Console Messages:")
            for msg in console_messages[-10:]:  # Last 10 messages
                print(f"  {msg}")
            
            # Check if homepage components are working
            print("\n🔍 Checking Homepage Components:")
            
            # Check if search input is working
            search_input = page.locator('input[placeholder*="search" i]')
            if await search_input.is_visible():
                print("✅ Search input visible")
                try:
                    await search_input.click()
                    await search_input.type("test")
                    print("✅ Search input functional")
                except Exception as e:
                    print(f"❌ Search input error: {e}")
            else:
                print("❌ Search input not visible")
            
            # Check if filter section is working
            filter_section = page.locator('h2:has-text("Find Exactly What You Need")')
            if await filter_section.is_visible():
                print("✅ Filter section visible")
            else:
                print("❌ Filter section not visible")
            
            # Check if filter dropdowns are present
            categories_button = page.locator('text=Categories')
            brands_button = page.locator('text=Brands')
            sellers_button = page.locator('text=Sellers')
            
            categories_visible = await categories_button.is_visible()
            brands_visible = await brands_button.is_visible()
            sellers_visible = await sellers_button.is_visible()
            
            print(f"📊 Filter dropdowns: Categories={categories_visible}, Brands={brands_visible}, Sellers={sellers_visible}")
            
            return not await error_overlay.is_visible()
            
        except Exception as e:
            print(f"❌ Test failed: {e}")
            await page.screenshot(path="debug_screenshots/runtime_test_error.png")
            import traceback
            traceback.print_exc()
            return False
        finally:
            await browser.close()


if __name__ == "__main__":
    success = asyncio.run(test_runtime_errors())
    if success:
        print("\n✅ No runtime errors detected!")
    else:
        print("\n❌ Runtime errors found")

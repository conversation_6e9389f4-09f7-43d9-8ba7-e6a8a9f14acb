"""
Performance monitoring and optimization service
Tracks API performance, database queries, and system metrics
"""

import time
import asyncio
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
from collections import defaultdict, deque
import psutil
import asyncpg

from ..config import get_settings

settings = get_settings()


class PerformanceMonitor:
    """Monitors and tracks system performance metrics."""
    
    def __init__(self):
        self.request_times = deque(maxlen=1000)  # Last 1000 requests
        self.db_query_times = deque(maxlen=1000)  # Last 1000 DB queries
        self.endpoint_stats = defaultdict(lambda: {
            'count': 0,
            'total_time': 0,
            'avg_time': 0,
            'min_time': float('inf'),
            'max_time': 0,
            'errors': 0
        })
        self.slow_queries = deque(maxlen=100)  # Last 100 slow queries
        self.error_log = deque(maxlen=500)  # Last 500 errors
        
    def record_request(self, endpoint: str, method: str, duration: float, status_code: int):
        """Record API request performance."""
        key = f"{method} {endpoint}"
        stats = self.endpoint_stats[key]
        
        stats['count'] += 1
        stats['total_time'] += duration
        stats['avg_time'] = stats['total_time'] / stats['count']
        stats['min_time'] = min(stats['min_time'], duration)
        stats['max_time'] = max(stats['max_time'], duration)
        
        if status_code >= 400:
            stats['errors'] += 1
        
        self.request_times.append({
            'endpoint': key,
            'duration': duration,
            'status_code': status_code,
            'timestamp': datetime.now()
        })
        
        # Log slow requests (>1 second)
        if duration > 1.0:
            self.slow_queries.append({
                'type': 'api_request',
                'endpoint': key,
                'duration': duration,
                'timestamp': datetime.now()
            })
    
    def record_db_query(self, query: str, duration: float, result_count: Optional[int] = None):
        """Record database query performance."""
        self.db_query_times.append({
            'query': query[:100] + '...' if len(query) > 100 else query,
            'duration': duration,
            'result_count': result_count,
            'timestamp': datetime.now()
        })
        
        # Log slow queries (>500ms)
        if duration > 0.5:
            self.slow_queries.append({
                'type': 'db_query',
                'query': query[:200] + '...' if len(query) > 200 else query,
                'duration': duration,
                'result_count': result_count,
                'timestamp': datetime.now()
            })
    
    def record_error(self, error_type: str, message: str, endpoint: Optional[str] = None):
        """Record system errors."""
        self.error_log.append({
            'type': error_type,
            'message': message,
            'endpoint': endpoint,
            'timestamp': datetime.now()
        })
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """Get comprehensive performance statistics."""
        now = datetime.now()
        last_hour = now - timedelta(hours=1)
        last_minute = now - timedelta(minutes=1)
        
        # Recent request stats
        recent_requests = [r for r in self.request_times if r['timestamp'] > last_hour]
        recent_db_queries = [q for q in self.db_query_times if q['timestamp'] > last_hour]
        
        # Calculate averages
        avg_request_time = sum(r['duration'] for r in recent_requests) / len(recent_requests) if recent_requests else 0
        avg_db_time = sum(q['duration'] for q in recent_db_queries) / len(recent_db_queries) if recent_db_queries else 0
        
        # Error rates
        recent_errors = [e for e in self.error_log if e['timestamp'] > last_hour]
        error_rate = len(recent_errors) / len(recent_requests) * 100 if recent_requests else 0
        
        # System metrics
        system_stats = self.get_system_metrics()
        
        return {
            'timestamp': now.isoformat(),
            'requests': {
                'total_last_hour': len(recent_requests),
                'avg_response_time_ms': round(avg_request_time * 1000, 2),
                'requests_per_minute': len([r for r in recent_requests if r['timestamp'] > last_minute]),
                'error_rate_percent': round(error_rate, 2)
            },
            'database': {
                'queries_last_hour': len(recent_db_queries),
                'avg_query_time_ms': round(avg_db_time * 1000, 2),
                'slow_queries_count': len([q for q in self.slow_queries if q['timestamp'] > last_hour])
            },
            'endpoints': dict(self.endpoint_stats),
            'system': system_stats,
            'slow_queries': list(self.slow_queries)[-10:],  # Last 10 slow queries
            'recent_errors': list(self.error_log)[-10:]  # Last 10 errors
        }
    
    def get_system_metrics(self) -> Dict[str, Any]:
        """Get system resource metrics."""
        try:
            cpu_percent = psutil.cpu_percent(interval=1)
            memory = psutil.virtual_memory()
            disk = psutil.disk_usage('/')
            
            return {
                'cpu_percent': cpu_percent,
                'memory': {
                    'total_gb': round(memory.total / (1024**3), 2),
                    'used_gb': round(memory.used / (1024**3), 2),
                    'percent': memory.percent
                },
                'disk': {
                    'total_gb': round(disk.total / (1024**3), 2),
                    'used_gb': round(disk.used / (1024**3), 2),
                    'percent': round(disk.used / disk.total * 100, 2)
                }
            }
        except Exception as e:
            return {'error': f'Failed to get system metrics: {e}'}
    
    def get_health_status(self) -> Dict[str, Any]:
        """Get overall system health status."""
        stats = self.get_performance_stats()
        
        # Health thresholds
        health_status = 'healthy'
        issues = []
        
        # Check response times
        if stats['requests']['avg_response_time_ms'] > 1000:
            health_status = 'degraded'
            issues.append('High API response times')
        
        # Check error rates
        if stats['requests']['error_rate_percent'] > 5:
            health_status = 'unhealthy'
            issues.append('High error rate')
        
        # Check database performance
        if stats['database']['avg_query_time_ms'] > 500:
            health_status = 'degraded'
            issues.append('Slow database queries')
        
        # Check system resources
        if 'system' in stats and 'error' not in stats['system']:
            if stats['system']['memory']['percent'] > 90:
                health_status = 'degraded'
                issues.append('High memory usage')
            
            if stats['system']['cpu_percent'] > 90:
                health_status = 'degraded'
                issues.append('High CPU usage')
        
        return {
            'status': health_status,
            'issues': issues,
            'timestamp': datetime.now().isoformat(),
            'uptime_hours': self.get_uptime_hours()
        }
    
    def get_uptime_hours(self) -> float:
        """Get application uptime in hours."""
        # This would typically track from application start time
        # For now, return a placeholder
        return round(time.time() / 3600, 2)


# Global performance monitor instance
performance_monitor = PerformanceMonitor()


class PerformanceMiddleware:
    """Middleware to track API performance."""
    
    def __init__(self, app):
        self.app = app
    
    async def __call__(self, scope, receive, send):
        if scope["type"] == "http":
            start_time = time.time()
            
            # Extract endpoint info
            path = scope.get("path", "")
            method = scope.get("method", "")
            
            status_code = 200
            
            async def send_wrapper(message):
                nonlocal status_code
                if message["type"] == "http.response.start":
                    status_code = message["status"]
                await send(message)
            
            try:
                await self.app(scope, receive, send_wrapper)
            except Exception as e:
                status_code = 500
                performance_monitor.record_error("middleware_error", str(e), path)
                raise
            finally:
                duration = time.time() - start_time
                performance_monitor.record_request(path, method, duration, status_code)
        else:
            await self.app(scope, receive, send)


async def db_query_timer(query: str, execute_func, *args, **kwargs):
    """Timer wrapper for database queries."""
    start_time = time.time()
    try:
        result = await execute_func(*args, **kwargs)
        duration = time.time() - start_time
        
        # Count results if it's a fetch operation
        result_count = None
        if hasattr(result, '__len__'):
            result_count = len(result)
        elif hasattr(result, '__iter__'):
            # For async iterators, we can't count without consuming
            result_count = None
        
        performance_monitor.record_db_query(query, duration, result_count)
        return result
        
    except Exception as e:
        duration = time.time() - start_time
        performance_monitor.record_db_query(query, duration, None)
        performance_monitor.record_error("db_query_error", str(e))
        raise


def get_performance_stats() -> Dict[str, Any]:
    """Get current performance statistics."""
    return performance_monitor.get_performance_stats()


def get_health_status() -> Dict[str, Any]:
    """Get current system health status."""
    return performance_monitor.get_health_status()

"""
Rate limiting middleware for authentication endpoints.
Prevents brute force attacks on login, registration, and password reset endpoints.
"""

import time
import redis
from typing import Dict, Optional, Tuple
from fastapi import Request, HTTPException, status
from fastapi.responses import J<PERSON>NResponse
from starlette.middleware.base import BaseHTTPMiddleware
import json
import hashlib
from ..config import settings


class RateLimiter:
    """
    Redis-based rate limiter for authentication endpoints.
    Implements sliding window rate limiting with different limits for different endpoints.
    """
    
    def __init__(self, redis_client: redis.Redis):
        self.redis = redis_client
        
        # Rate limiting configurations for different endpoints
        self.limits = {
            # Authentication endpoints - stricter limits
            "auth_login": {"requests": 5, "window": 300},  # 5 attempts per 5 minutes
            "auth_register": {"requests": 3, "window": 3600},  # 3 registrations per hour
            "auth_refresh": {"requests": 10, "window": 300},  # 10 refreshes per 5 minutes
            
            # Admin endpoints - moderate limits
            "admin_endpoints": {"requests": 20, "window": 300},  # 20 requests per 5 minutes
            
            # Search endpoints - generous limits
            "search_endpoints": {"requests": 100, "window": 60},  # 100 searches per minute
            
            # Shopping list endpoints - moderate limits
            "shopping_list": {"requests": 50, "window": 300},  # 50 operations per 5 minutes
            
            # Default fallback
            "default": {"requests": 30, "window": 300}  # 30 requests per 5 minutes
        }
    
    def get_client_identifier(self, request: Request) -> str:
        """
        Get unique identifier for the client (IP address + User-Agent hash).
        This helps prevent simple IP rotation attacks.
        """
        # Get client IP (handle proxy headers)
        client_ip = request.headers.get("X-Forwarded-For", "").split(",")[0].strip()
        if not client_ip:
            client_ip = request.headers.get("X-Real-IP", "")
        if not client_ip:
            client_ip = request.client.host if request.client else "unknown"
        
        # Get User-Agent for additional fingerprinting
        user_agent = request.headers.get("User-Agent", "")
        user_agent_hash = hashlib.md5(user_agent.encode()).hexdigest()[:8]
        
        return f"{client_ip}:{user_agent_hash}"
    
    def get_endpoint_category(self, path: str, method: str) -> str:
        """
        Categorize endpoint for rate limiting based on path and method.
        """
        path_lower = path.lower()
        
        # Authentication endpoints
        if "/auth/login" in path_lower:
            return "auth_login"
        elif "/auth/register" in path_lower:
            return "auth_register"
        elif "/auth/refresh" in path_lower:
            return "auth_refresh"
        
        # Admin endpoints
        elif "/admin/" in path_lower:
            return "admin_endpoints"
        
        # Search endpoints
        elif "/search/" in path_lower:
            return "search_endpoints"
        
        # Shopping list endpoints
        elif "/shopping-lists/" in path_lower:
            return "shopping_list"
        
        # Default category
        else:
            return "default"
    
    async def is_rate_limited(self, request: Request) -> Tuple[bool, Dict]:
        """
        Check if request should be rate limited.
        Returns (is_limited, rate_limit_info)
        """
        try:
            # Get client identifier and endpoint category
            client_id = self.get_client_identifier(request)
            endpoint_category = self.get_endpoint_category(request.url.path, request.method)
            
            # Get rate limit configuration
            limit_config = self.limits.get(endpoint_category, self.limits["default"])
            max_requests = limit_config["requests"]
            window_seconds = limit_config["window"]
            
            # Create Redis key
            redis_key = f"rate_limit:{endpoint_category}:{client_id}"
            
            # Get current time
            current_time = int(time.time())
            window_start = current_time - window_seconds
            
            # Use Redis pipeline for atomic operations
            pipe = self.redis.pipeline()
            
            # Remove old entries outside the window
            pipe.zremrangebyscore(redis_key, 0, window_start)
            
            # Count current requests in window
            pipe.zcard(redis_key)
            
            # Add current request
            pipe.zadd(redis_key, {str(current_time): current_time})
            
            # Set expiration for cleanup
            pipe.expire(redis_key, window_seconds + 60)
            
            # Execute pipeline
            results = pipe.execute()
            current_count = results[1]  # Count after removing old entries
            
            # Check if rate limited
            is_limited = current_count >= max_requests
            
            # Calculate reset time
            reset_time = current_time + window_seconds
            
            # Rate limit info for headers
            rate_limit_info = {
                "limit": max_requests,
                "remaining": max(0, max_requests - current_count - 1),
                "reset": reset_time,
                "window": window_seconds,
                "category": endpoint_category
            }
            
            return is_limited, rate_limit_info
            
        except Exception as e:
            # If Redis is down, don't block requests but log the error
            print(f"Rate limiting error: {e}")
            return False, {
                "limit": 0,
                "remaining": 0,
                "reset": 0,
                "window": 0,
                "category": "error"
            }
    
    async def get_rate_limit_status(self, request: Request) -> Dict:
        """
        Get current rate limit status without incrementing counter.
        Useful for checking status without consuming a request.
        """
        try:
            client_id = self.get_client_identifier(request)
            endpoint_category = self.get_endpoint_category(request.url.path, request.method)
            
            limit_config = self.limits.get(endpoint_category, self.limits["default"])
            max_requests = limit_config["requests"]
            window_seconds = limit_config["window"]
            
            redis_key = f"rate_limit:{endpoint_category}:{client_id}"
            
            current_time = int(time.time())
            window_start = current_time - window_seconds
            
            # Clean old entries and count current
            pipe = self.redis.pipeline()
            pipe.zremrangebyscore(redis_key, 0, window_start)
            pipe.zcard(redis_key)
            results = pipe.execute()
            
            current_count = results[1]
            
            return {
                "limit": max_requests,
                "remaining": max(0, max_requests - current_count),
                "reset": current_time + window_seconds,
                "window": window_seconds,
                "category": endpoint_category,
                "current_count": current_count
            }
            
        except Exception as e:
            print(f"Rate limit status error: {e}")
            return {
                "limit": 0,
                "remaining": 0,
                "reset": 0,
                "window": 0,
                "category": "error",
                "current_count": 0
            }


# Global rate limiter instance
rate_limiter: Optional[RateLimiter] = None


def get_rate_limiter() -> RateLimiter:
    """Get the global rate limiter instance."""
    global rate_limiter
    if rate_limiter is None:
        # This will be initialized when Redis connection is available
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail="Rate limiting service unavailable"
        )
    return rate_limiter


def initialize_rate_limiter(redis_client: redis.Redis):
    """Initialize the global rate limiter with Redis client."""
    global rate_limiter
    rate_limiter = RateLimiter(redis_client)


class RateLimitMiddleware(BaseHTTPMiddleware):
    """
    FastAPI middleware for rate limiting using BaseHTTPMiddleware.
    Checks rate limits before processing requests and adds rate limit headers to responses.
    """

    def __init__(self, app):
        super().__init__(app)

    async def dispatch(self, request: Request, call_next):
        """Process request with rate limiting."""
        try:
            # Skip rate limiting for health checks and static files
            if request.url.path in ["/health", "/docs", "/redoc", "/openapi.json"]:
                response = await call_next(request)
                return response

            # Get rate limiter
            limiter = get_rate_limiter()

            # Check rate limit
            is_limited, rate_info = await limiter.is_rate_limited(request)

            if is_limited:
                # Return rate limit exceeded response
                return JSONResponse(
                    status_code=status.HTTP_429_TOO_MANY_REQUESTS,
                    content={
                        "error": "Rate limit exceeded",
                        "message": f"Too many requests for {rate_info['category']} endpoints. "
                                  f"Limit: {rate_info['limit']} requests per {rate_info['window']} seconds.",
                        "retry_after": rate_info['reset'] - int(time.time()),
                        "limit": rate_info['limit'],
                        "window": rate_info['window']
                    },
                    headers={
                        "X-RateLimit-Limit": str(rate_info['limit']),
                        "X-RateLimit-Remaining": str(rate_info['remaining']),
                        "X-RateLimit-Reset": str(rate_info['reset']),
                        "X-RateLimit-Window": str(rate_info['window']),
                        "Retry-After": str(rate_info['reset'] - int(time.time()))
                    }
                )

            # Process request
            response = await call_next(request)

            # Add rate limit headers to successful responses
            response.headers["X-RateLimit-Limit"] = str(rate_info['limit'])
            response.headers["X-RateLimit-Remaining"] = str(rate_info['remaining'])
            response.headers["X-RateLimit-Reset"] = str(rate_info['reset'])
            response.headers["X-RateLimit-Window"] = str(rate_info['window'])

            return response

        except Exception as e:
            # If rate limiting fails, continue without blocking the request
            print(f"Rate limiting middleware error: {e}")
            response = await call_next(request)
            return response


# Legacy function-based middleware for backward compatibility
async def rate_limit_middleware(request: Request, call_next):
    """
    Legacy function-based middleware - deprecated.
    Use RateLimitMiddleware class instead.
    """
    middleware = RateLimitMiddleware(None)
    return await middleware.dispatch(request, call_next)


# Convenience functions for manual rate limit checking
async def check_auth_rate_limit(request: Request) -> None:
    """
    Check authentication endpoint rate limit and raise HTTPException if exceeded.
    Use this in authentication endpoints for additional protection.
    """
    try:
        limiter = get_rate_limiter()
        is_limited, rate_info = await limiter.is_rate_limited(request)
        
        if is_limited:
            raise HTTPException(
                status_code=status.HTTP_429_TOO_MANY_REQUESTS,
                detail={
                    "error": "Authentication rate limit exceeded",
                    "message": f"Too many authentication attempts. Please try again in {rate_info['reset'] - int(time.time())} seconds.",
                    "retry_after": rate_info['reset'] - int(time.time())
                }
            )
    except HTTPException:
        raise
    except Exception as e:
        # Don't block authentication if rate limiting fails
        print(f"Auth rate limit check error: {e}")
        pass


async def get_rate_limit_info(request: Request) -> Dict:
    """
    Get current rate limit information for debugging/monitoring.
    """
    try:
        limiter = get_rate_limiter()
        return await limiter.get_rate_limit_status(request)
    except Exception as e:
        print(f"Rate limit info error: {e}")
        return {"error": str(e)}

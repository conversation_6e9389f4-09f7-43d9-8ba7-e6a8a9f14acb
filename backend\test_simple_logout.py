#!/usr/bin/env python3
"""
Simple test for logout functionality using the same approach as critical functionality test.
"""

import asyncio
import httpx
import json

async def test_simple_logout():
    """Test logout functionality with a simple approach."""
    print("🔐 Simple Logout Test")
    print("=" * 40)
    
    base_url = "http://localhost:8000"
    
    async with httpx.AsyncClient(timeout=30.0) as client:
        # Step 1: Login to get a token
        print("1. Logging in...")
        login_data = {
            "email": "<EMAIL>",
            "password": "SuperYzn123!"
        }
        
        try:
            response = await client.post(f"{base_url}/api/v1/auth/login", json=login_data)
            print(f"Login response status: {response.status_code}")
            
            if response.status_code == 200:
                response_data = response.json()
                access_token = response_data.get("access_token")
                print(f"✅ Login successful, got token: {access_token[:20]}...")
            else:
                print(f"❌ Login failed: {response.text}")
                return False
                
        except Exception as e:
            print(f"❌ Login error: {e}")
            return False
        
        # Step 2: Test logout
        print("\n2. Testing logout...")
        headers = {"Authorization": f"Bearer {access_token}"}
        
        try:
            response = await client.post(f"{base_url}/api/v1/auth/logout", headers=headers)
            print(f"Logout response status: {response.status_code}")
            print(f"Logout response: {response.text}")
            
            if response.status_code == 200:
                print("✅ Logout successful")
                return True
            else:
                print(f"❌ Logout failed with status {response.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ Logout error: {e}")
            return False

async def main():
    """Run the simple logout test."""
    success = await test_simple_logout()
    if success:
        print("\n🎉 Simple logout test passed!")
    else:
        print("\n❌ Simple logout test failed!")
    return success

if __name__ == "__main__":
    success = asyncio.run(main())
    exit(0 if success else 1)

import { Link } from 'react-router-dom'
import { ExternalLink, ShoppingCart, Star } from 'lucide-react'
import { Product } from '../../types'
import { formatPrice, truncateText, highlightSearchTerm } from '../../lib/utils'
import { useSearchStore } from '../../stores/searchStore'
import { cn } from '../../lib/utils'

interface SearchResultsProps {
  results: Product[]
  isLoading: boolean
  query: string
}

const SearchResults = ({ results, isLoading, query }: SearchResultsProps) => {
  const { total, searchTime } = useSearchStore()

  if (isLoading) {
    return (
      <div className="space-y-4">
        {[...Array(5)].map((_, i) => (
          <div key={i} className="animate-pulse">
            <div className="bg-white rounded-lg border border-gray-200 p-6">
              <div className="flex justify-between items-start">
                <div className="flex-1">
                  <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                  <div className="h-3 bg-gray-200 rounded w-1/2 mb-4"></div>
                  <div className="flex space-x-4">
                    <div className="h-3 bg-gray-200 rounded w-20"></div>
                    <div className="h-3 bg-gray-200 rounded w-24"></div>
                    <div className="h-3 bg-gray-200 rounded w-16"></div>
                  </div>
                </div>
                <div className="h-6 bg-gray-200 rounded w-20"></div>
              </div>
            </div>
          </div>
        ))}
      </div>
    )
  }

  if (results.length === 0 && query) {
    return (
      <div className="text-center py-12">
        <div className="text-gray-400 mb-4">
          <svg className="mx-auto h-12 w-12" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
          </svg>
        </div>
        <h3 className="text-lg font-medium text-gray-900 mb-2">No results found</h3>
        <p className="text-gray-500 mb-4">
          We couldn't find any products matching "{query}". Try adjusting your search terms.
        </p>
        <div className="text-sm text-gray-400">
          <p>Suggestions:</p>
          <ul className="mt-2 space-y-1">
            <li>• Check your spelling</li>
            <li>• Try more general terms</li>
            <li>• Use different keywords</li>
          </ul>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-4">
      {/* Results header */}
      {total > 0 && (
        <div className="flex items-center justify-between text-sm text-gray-600 mb-6">
          <div>
            Showing {results.length} of {total.toLocaleString()} results
            {searchTime > 0 && (
              <span className="ml-2">
                ({searchTime < 1000 ? `${searchTime.toFixed(0)}ms` : `${(searchTime / 1000).toFixed(2)}s`})
              </span>
            )}
          </div>
        </div>
      )}

      {/* Results list */}
      <div className="space-y-4">
        {results.map((product) => (
          <SearchResultItem
            key={product.id}
            product={product}
            query={query}
          />
        ))}
      </div>
    </div>
  )
}

interface SearchResultItemProps {
  product: Product
  query: string
}

const SearchResultItem = ({ product, query }: SearchResultItemProps) => {
  const handleAddToCart = (e: React.MouseEvent) => {
    e.preventDefault()
    e.stopPropagation()
    // TODO: Implement add to shopping list functionality
    console.log('Add to cart:', product.id)
  }

  const handleVisitSite = (e: React.MouseEvent) => {
    e.preventDefault()
    e.stopPropagation()
    window.open(product.url, '_blank', 'noopener,noreferrer')
  }

  return (
    <div className="bg-white rounded-lg border border-gray-200 hover:border-gray-300 hover:shadow-md transition-all duration-200">
      <Link to={`/product/${product.id}`} className="block p-6">
        <div className="flex justify-between items-start">
          <div className="flex-1 min-w-0">
            {/* Product name */}
            <h3 
              className="text-lg font-medium text-gray-900 mb-2 line-clamp-2"
              dangerouslySetInnerHTML={{ 
                __html: highlightSearchTerm(product.name, query) 
              }}
            />
            
            {/* Product details */}
            <div className="flex flex-wrap items-center gap-4 text-sm text-gray-600 mb-3">
              {product.brand && (
                <span className="flex items-center">
                  <span className="font-medium">Brand:</span>
                  <span className="ml-1">{product.brand}</span>
                </span>
              )}
              
              {product.mfr && (
                <span className="flex items-center">
                  <span className="font-medium">MFR:</span>
                  <span className="ml-1 font-mono text-xs bg-gray-100 px-2 py-1 rounded">
                    {product.mfr}
                  </span>
                </span>
              )}
              
              {product.maincat && (
                <span className="flex items-center">
                  <span className="font-medium">Category:</span>
                  <span className="ml-1">{product.maincat}</span>
                </span>
              )}
            </div>
            
            {/* Seller and price */}
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                  {product.seller}
                </span>
                
                {product.rank && (
                  <div className="flex items-center text-xs text-gray-500">
                    <Star className="h-3 w-3 mr-1" />
                    {(product.rank * 100).toFixed(0)}% match
                  </div>
                )}
              </div>
              
              <div className="text-right">
                <div className="text-lg font-semibold text-gray-900">
                  {formatPrice(product.price)}
                </div>
              </div>
            </div>
          </div>
        </div>
        
        {/* Action buttons */}
        <div className="flex items-center justify-end space-x-2 mt-4 pt-4 border-t border-gray-100">
          <button
            onClick={handleAddToCart}
            className="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors"
          >
            <ShoppingCart className="h-4 w-4 mr-1" />
            Add to List
          </button>
          
          <button
            onClick={handleVisitSite}
            className="inline-flex items-center px-3 py-2 border border-gray-300 text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors"
          >
            <ExternalLink className="h-4 w-4 mr-1" />
            Visit Site
          </button>
        </div>
      </Link>
    </div>
  )
}

export default SearchResults

import React, { useEffect, useState } from "react";
import {
  Users,
  Shield,
  Database,
  Activity,
  TrendingUp,
  AlertCircle,
} from "lucide-react";
import { apiClient } from "../../lib/api";
import { PageHeader } from "../../components/navigation/Breadcrumbs";

interface UserStats {
  total_users: number;
  active_users: number;
  superuser_count: number;
  new_users_last_7_days: number;
  new_users_last_30_days: number;
}

interface RoleStats {
  total_roles: number;
  active_roles: number;
  system_roles: number;
  custom_roles: number;
}

interface PermissionStats {
  total_permissions: number;
  active_permissions: number;
  system_permissions: number;
  custom_permissions: number;
}

interface DatabaseHealth {
  status: string;
  total_users: number;
  total_roles: number;
  total_permissions: number;
  total_user_roles: number;
  total_role_permissions: number;
  response_time_ms: number;
}

const AdminDashboardPage: React.FC = () => {
  const [userStats, setUserStats] = useState<UserStats | null>(null);
  const [roleStats, setRoleStats] = useState<RoleStats | null>(null);
  const [permissionStats, setPermissionStats] =
    useState<PermissionStats | null>(null);
  const [dbHealth, setDbHealth] = useState<DatabaseHealth | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchDashboardData = async () => {
      try {
        setLoading(true);
        setError(null);

        // Fetch all dashboard data in parallel
        const [userStatsRes, roleStatsRes, permissionStatsRes, dbHealthRes] =
          await Promise.all([
            apiClient.get("/admin/stats/users"),
            apiClient.get("/admin/stats/roles"),
            apiClient.get("/admin/stats/permissions"),
            apiClient.get("/admin/health/database"),
          ]);

        setUserStats(userStatsRes);
        setRoleStats(roleStatsRes);
        setPermissionStats(permissionStatsRes);
        setDbHealth(dbHealthRes);
      } catch (err: any) {
        console.error("Failed to fetch dashboard data:", err);
        setError(err.response?.data?.detail || "Failed to load dashboard data");
      } finally {
        setLoading(false);
      }
    };

    fetchDashboardData();
  }, []);

  if (loading) {
    return (
      <div className="space-y-6">
        <PageHeader title="Admin Dashboard" />
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="space-y-6">
        <PageHeader title="Admin Dashboard" />
        <div className="bg-red-50 border border-red-200 rounded-lg p-6">
          <div className="flex items-center">
            <AlertCircle className="h-5 w-5 text-red-600 mr-2" />
            <span className="text-red-800">{error}</span>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <PageHeader title="Admin Dashboard" />

      {/* Overview Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {/* Users Stats */}
        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <Users className="h-8 w-8 text-blue-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">Total Users</p>
              <p className="text-2xl font-semibold text-gray-900">
                {userStats?.total_users || 0}
              </p>
            </div>
          </div>
          <div className="mt-4">
            <div className="flex items-center text-sm text-gray-600">
              <TrendingUp className="h-4 w-4 text-green-500 mr-1" />
              <span>{userStats?.new_users_last_7_days || 0} new this week</span>
            </div>
          </div>
        </div>

        {/* Active Users */}
        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <Activity className="h-8 w-8 text-green-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">Active Users</p>
              <p className="text-2xl font-semibold text-gray-900">
                {userStats?.active_users || 0}
              </p>
            </div>
          </div>
          <div className="mt-4">
            <div className="text-sm text-gray-600">
              <span>{userStats?.superuser_count || 0} superadmins</span>
            </div>
          </div>
        </div>

        {/* Roles */}
        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <Shield className="h-8 w-8 text-purple-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">Total Roles</p>
              <p className="text-2xl font-semibold text-gray-900">
                {roleStats?.total_roles || 0}
              </p>
            </div>
          </div>
          <div className="mt-4">
            <div className="text-sm text-gray-600">
              <span>
                {roleStats?.system_roles || 0} system,{" "}
                {roleStats?.custom_roles || 0} custom
              </span>
            </div>
          </div>
        </div>

        {/* Database Health */}
        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <Database
                className={`h-8 w-8 ${
                  dbHealth?.status === "healthy"
                    ? "text-green-600"
                    : "text-red-600"
                }`}
              />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">Database</p>
              <p className="text-2xl font-semibold text-gray-900 capitalize">
                {dbHealth?.status || "Unknown"}
              </p>
            </div>
          </div>
          <div className="mt-4">
            <div className="text-sm text-gray-600">
              <span>{dbHealth?.response_time_ms || 0}ms response</span>
            </div>
          </div>
        </div>
      </div>

      {/* Detailed Statistics */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* User Statistics */}
        <div className="bg-white rounded-lg shadow">
          <div className="px-6 py-4 border-b border-gray-200">
            <h3 className="text-lg font-medium text-gray-900">
              User Statistics
            </h3>
          </div>
          <div className="p-6">
            <dl className="grid grid-cols-1 gap-4 sm:grid-cols-2">
              <div>
                <dt className="text-sm font-medium text-gray-500">
                  Total Users
                </dt>
                <dd className="mt-1 text-2xl font-semibold text-gray-900">
                  {userStats?.total_users || 0}
                </dd>
              </div>
              <div>
                <dt className="text-sm font-medium text-gray-500">
                  Active Users
                </dt>
                <dd className="mt-1 text-2xl font-semibold text-green-600">
                  {userStats?.active_users || 0}
                </dd>
              </div>
              <div>
                <dt className="text-sm font-medium text-gray-500">
                  Superadmins
                </dt>
                <dd className="mt-1 text-2xl font-semibold text-purple-600">
                  {userStats?.superuser_count || 0}
                </dd>
              </div>
              <div>
                <dt className="text-sm font-medium text-gray-500">
                  New (30 days)
                </dt>
                <dd className="mt-1 text-2xl font-semibold text-blue-600">
                  {userStats?.new_users_last_30_days || 0}
                </dd>
              </div>
            </dl>
          </div>
        </div>

        {/* System Statistics */}
        <div className="bg-white rounded-lg shadow">
          <div className="px-6 py-4 border-b border-gray-200">
            <h3 className="text-lg font-medium text-gray-900">
              System Statistics
            </h3>
          </div>
          <div className="p-6">
            <dl className="grid grid-cols-1 gap-4 sm:grid-cols-2">
              <div>
                <dt className="text-sm font-medium text-gray-500">
                  Total Roles
                </dt>
                <dd className="mt-1 text-2xl font-semibold text-gray-900">
                  {roleStats?.total_roles || 0}
                </dd>
              </div>
              <div>
                <dt className="text-sm font-medium text-gray-500">
                  Permissions
                </dt>
                <dd className="mt-1 text-2xl font-semibold text-gray-900">
                  {permissionStats?.total_permissions || 0}
                </dd>
              </div>
              <div>
                <dt className="text-sm font-medium text-gray-500">
                  User-Role Assignments
                </dt>
                <dd className="mt-1 text-2xl font-semibold text-blue-600">
                  {dbHealth?.total_user_roles || 0}
                </dd>
              </div>
              <div>
                <dt className="text-sm font-medium text-gray-500">
                  Role-Permission Links
                </dt>
                <dd className="mt-1 text-2xl font-semibold text-purple-600">
                  {dbHealth?.total_role_permissions || 0}
                </dd>
              </div>
            </dl>
          </div>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="bg-white rounded-lg shadow">
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-medium text-gray-900">Quick Actions</h3>
        </div>
        <div className="p-6">
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
            <a
              href="/admin/users"
              className="flex items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
            >
              <Users className="h-6 w-6 text-blue-600 mr-3" />
              <div>
                <p className="font-medium text-gray-900">Manage Users</p>
                <p className="text-sm text-gray-500">
                  View and edit user accounts
                </p>
              </div>
            </a>
            <a
              href="/admin/roles"
              className="flex items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
            >
              <Shield className="h-6 w-6 text-purple-600 mr-3" />
              <div>
                <p className="font-medium text-gray-900">Manage Roles</p>
                <p className="text-sm text-gray-500">
                  Configure roles and permissions
                </p>
              </div>
            </a>
            <a
              href="/admin/health"
              className="flex items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
            >
              <Database className="h-6 w-6 text-green-600 mr-3" />
              <div>
                <p className="font-medium text-gray-900">System Health</p>
                <p className="text-sm text-gray-500">Monitor system status</p>
              </div>
            </a>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AdminDashboardPage;

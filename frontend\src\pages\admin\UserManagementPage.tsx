import React, { useEffect, useState } from "react";
import {
  Users,
  Search,
  Filter,
  Edit,
  Shield,
  Shield<PERSON>heck,
  User<PERSON>he<PERSON>,
  UserX,
  MoreVertical,
  AlertCircle,
} from "lucide-react";
import { apiClient } from "../../lib/api";
import { PageHeader } from "../../components/navigation/Breadcrumbs";
import RoleAssignmentModal from "../../components/admin/RoleAssignmentModal";

interface User {
  id: string;
  email: string;
  full_name: string;
  is_active: boolean;
  is_superuser: boolean;
  created_at: string;
  updated_at: string;
  roles?: string[];
  permissions?: string[];
}

const UserManagementPage: React.FC = () => {
  const [users, setUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState("");
  const [filterActive, setFilterActive] = useState<boolean | null>(null);
  const [filterSuperuser, setFilterSuperuser] = useState<boolean | null>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalUsers, setTotalUsers] = useState(0);
  const pageSize = 20;

  // Role assignment modal state
  const [roleModalOpen, setRoleModalOpen] = useState(false);
  const [selectedUser, setSelectedUser] = useState<User | null>(null);

  const fetchUsers = async () => {
    try {
      setLoading(true);
      setError(null);

      const params = new URLSearchParams({
        skip: ((currentPage - 1) * pageSize).toString(),
        limit: pageSize.toString(),
      });

      if (searchTerm) {
        params.append("search", searchTerm);
      }
      if (filterActive !== null) {
        params.append("is_active", filterActive.toString());
      }
      if (filterSuperuser !== null) {
        params.append("is_superuser", filterSuperuser.toString());
      }

      const response = await apiClient.get(`/admin/users?${params.toString()}`);
      // Handle both array response and object response
      const usersData = Array.isArray(response)
        ? response
        : response.users || response.data || [];
      setUsers(usersData);
      setTotalUsers(
        Array.isArray(response)
          ? response.length
          : response.total || usersData.length
      );
    } catch (err: any) {
      console.error("Failed to fetch users:", err);
      setError(err.response?.data?.detail || "Failed to load users");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchUsers();
  }, [currentPage, searchTerm, filterActive, filterSuperuser]);

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    setCurrentPage(1);
    fetchUsers();
  };

  const toggleUserStatus = async (userId: string, currentStatus: boolean) => {
    try {
      await apiClient.put(`/admin/users/${userId}`, {
        is_active: !currentStatus,
      });
      fetchUsers(); // Refresh the list
    } catch (err: any) {
      console.error("Failed to update user status:", err);
      alert(
        "Failed to update user status: " +
          (err.response?.data?.detail || err.message)
      );
    }
  };

  const toggleSuperuserStatus = async (
    userId: string,
    currentStatus: boolean
  ) => {
    try {
      if (currentStatus) {
        await apiClient.post(`/admin/users/${userId}/remove-superuser`);
      } else {
        await apiClient.post(`/admin/users/${userId}/make-superuser`);
      }
      fetchUsers(); // Refresh the list
    } catch (err: any) {
      console.error("Failed to update superuser status:", err);
      alert(
        "Failed to update superuser status: " +
          (err.response?.data?.detail || err.message)
      );
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  const openRoleAssignmentModal = (user: User) => {
    setSelectedUser(user);
    setRoleModalOpen(true);
  };

  const closeRoleAssignmentModal = () => {
    setRoleModalOpen(false);
    setSelectedUser(null);
  };

  const handleRoleAssigned = () => {
    fetchUsers(); // Refresh the user list to show updated roles
  };

  const totalPages = Math.ceil(totalUsers / pageSize);

  if (loading && users.length === 0) {
    return (
      <div className="space-y-6">
        <PageHeader title="User Management" />
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <PageHeader title="User Management" />

      {/* Search and Filters */}
      <div className="bg-white rounded-lg shadow p-6">
        <form onSubmit={handleSearch} className="space-y-4">
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <input
                  type="text"
                  placeholder="Search by email or name..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10 pr-4 py-2 w-full border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
            </div>
            <button
              type="submit"
              className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
            >
              Search
            </button>
          </div>

          <div className="flex flex-wrap gap-4">
            <div className="flex items-center gap-2">
              <Filter className="h-4 w-4 text-gray-500" />
              <span className="text-sm font-medium text-gray-700">
                Filters:
              </span>
            </div>

            <select
              value={filterActive === null ? "" : filterActive.toString()}
              onChange={(e) =>
                setFilterActive(
                  e.target.value === "" ? null : e.target.value === "true"
                )
              }
              className="px-3 py-1 border border-gray-300 rounded text-sm"
            >
              <option value="">All Status</option>
              <option value="true">Active Only</option>
              <option value="false">Inactive Only</option>
            </select>

            <select
              value={filterSuperuser === null ? "" : filterSuperuser.toString()}
              onChange={(e) =>
                setFilterSuperuser(
                  e.target.value === "" ? null : e.target.value === "true"
                )
              }
              className="px-3 py-1 border border-gray-300 rounded text-sm"
            >
              <option value="">All Users</option>
              <option value="true">Superusers Only</option>
              <option value="false">Regular Users Only</option>
            </select>

            {(searchTerm ||
              filterActive !== null ||
              filterSuperuser !== null) && (
              <button
                type="button"
                onClick={() => {
                  setSearchTerm("");
                  setFilterActive(null);
                  setFilterSuperuser(null);
                  setCurrentPage(1);
                }}
                className="px-3 py-1 text-sm text-gray-600 hover:text-gray-800"
              >
                Clear Filters
              </button>
            )}
          </div>
        </form>
      </div>

      {error && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <div className="flex items-center">
            <AlertCircle className="h-5 w-5 text-red-600 mr-2" />
            <span className="text-red-800">{error}</span>
          </div>
        </div>
      )}

      {/* Users Table */}
      <div className="bg-white rounded-lg shadow overflow-hidden">
        <div className="px-6 py-4 border-b border-gray-200">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-medium text-gray-900">
              Users ({totalUsers})
            </h3>
            <div className="text-sm text-gray-500">
              Page {currentPage} of {totalPages}
            </div>
          </div>
        </div>

        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  User
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Status
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Roles
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Created
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {users.map((user) => (
                <tr key={user.id} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <div className="flex-shrink-0 h-10 w-10">
                        <div className="h-10 w-10 rounded-full bg-gray-300 flex items-center justify-center">
                          <Users className="h-5 w-5 text-gray-600" />
                        </div>
                      </div>
                      <div className="ml-4">
                        <div className="text-sm font-medium text-gray-900">
                          {user.full_name}
                        </div>
                        <div className="text-sm text-gray-500">
                          {user.email}
                        </div>
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex flex-col gap-1">
                      <span
                        className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                          user.is_active
                            ? "bg-green-100 text-green-800"
                            : "bg-red-100 text-red-800"
                        }`}
                      >
                        {user.is_active ? "Active" : "Inactive"}
                      </span>
                      {user.is_superuser && (
                        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
                          <ShieldCheck className="h-3 w-3 mr-1" />
                          Superuser
                        </span>
                      )}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex flex-wrap gap-1">
                      {user.roles && user.roles.length > 0 ? (
                        user.roles.map((role) => (
                          <span
                            key={role}
                            className="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-blue-100 text-blue-800"
                          >
                            {role}
                          </span>
                        ))
                      ) : (
                        <span className="text-sm text-gray-500">
                          No roles assigned
                        </span>
                      )}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {formatDate(user.created_at)}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                    <div className="flex items-center justify-end gap-2">
                      <button
                        onClick={() =>
                          toggleUserStatus(user.id, user.is_active)
                        }
                        className={`p-1 rounded hover:bg-gray-100 ${
                          user.is_active ? "text-red-600" : "text-green-600"
                        }`}
                        title={
                          user.is_active ? "Deactivate user" : "Activate user"
                        }
                      >
                        {user.is_active ? (
                          <UserX className="h-4 w-4" />
                        ) : (
                          <UserCheck className="h-4 w-4" />
                        )}
                      </button>
                      <button
                        onClick={() =>
                          toggleSuperuserStatus(user.id, user.is_superuser)
                        }
                        className={`p-1 rounded hover:bg-gray-100 ${
                          user.is_superuser
                            ? "text-purple-600"
                            : "text-gray-600"
                        }`}
                        title={
                          user.is_superuser
                            ? "Remove superuser"
                            : "Make superuser"
                        }
                      >
                        <Shield className="h-4 w-4" />
                      </button>
                      <button
                        onClick={() => openRoleAssignmentModal(user)}
                        className="p-1 rounded hover:bg-gray-100 text-gray-600"
                        title="Manage roles"
                      >
                        <MoreVertical className="h-4 w-4" />
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        {/* Pagination */}
        {totalPages > 1 && (
          <div className="px-6 py-4 border-t border-gray-200">
            <div className="flex items-center justify-between">
              <div className="text-sm text-gray-700">
                Showing {(currentPage - 1) * pageSize + 1} to{" "}
                {Math.min(currentPage * pageSize, totalUsers)} of {totalUsers}{" "}
                users
              </div>
              <div className="flex gap-2">
                <button
                  onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                  disabled={currentPage === 1}
                  className="px-3 py-1 border border-gray-300 rounded text-sm disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
                >
                  Previous
                </button>
                <button
                  onClick={() =>
                    setCurrentPage(Math.min(totalPages, currentPage + 1))
                  }
                  disabled={currentPage === totalPages}
                  className="px-3 py-1 border border-gray-300 rounded text-sm disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
                >
                  Next
                </button>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Role Assignment Modal */}
      {selectedUser && (
        <RoleAssignmentModal
          user={selectedUser}
          isOpen={roleModalOpen}
          onClose={closeRoleAssignmentModal}
          onRoleAssigned={handleRoleAssigned}
        />
      )}
    </div>
  );
};

export default UserManagementPage;

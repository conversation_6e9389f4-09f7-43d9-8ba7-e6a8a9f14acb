#!/usr/bin/env python3
"""Quick API test for backend functionality."""

import asyncio
import asyncpg
import os
import sys

# Add the app directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), 'app'))

async def test_apis():
    try:
        from services.search_service import SearchService
        from services.shopping_list_service import ShoppingListService
        import redis.asyncio as redis
        
        conn = await asyncpg.connect('postgresql://profident_user:profident_dev_password@localhost:5433/profident_dev')
        redis_client = redis.Redis(host='localhost', port=6380, db=0, decode_responses=True)
        
        print("🧪 Testing Backend APIs")
        print("=" * 40)
        
        # Test suggestions API
        print("\n1. Testing Suggestions API...")
        result = await SearchService.get_search_suggestions(conn, redis_client, 'dental', limit=3)
        print(f'✅ Suggestions API: {len(result.suggestions)} suggestions returned')
        
        for i, suggestion in enumerate(result.suggestions):
            print(f'  {i+1}. ID: {suggestion.id}, Name: {suggestion.name[:40]}...')
            assert suggestion.id is not None
            assert suggestion.name is not None
        
        print('✅ All suggestions have required fields')
        
        # Test quick-add functionality
        print("\n2. Testing Quick-Add API...")
        if result.suggestions:
            test_user = "12345678-1234-1234-1234-123456789012"  # Valid UUID format
            
            # Clean up first
            await conn.execute("DELETE FROM shopping_list_items WHERE shopping_list_id IN (SELECT id FROM shopping_lists WHERE user_id = $1)", test_user)
            await conn.execute("DELETE FROM shopping_lists WHERE user_id = $1", test_user)
            
            # Test quick-add
            product = result.suggestions[0]
            added_item = await ShoppingListService.quick_add_product_to_default_list(
                conn, redis_client, test_user, product.id, 1
            )
            
            if added_item:
                print(f'✅ Quick-add successful: Added {product.name[:30]}... to shopping list')
                print(f'   Item ID: {added_item.id}, List ID: {added_item.shopping_list_id}')
            else:
                print('❌ Quick-add failed')
            
            # Clean up
            await conn.execute("DELETE FROM shopping_list_items WHERE shopping_list_id IN (SELECT id FROM shopping_lists WHERE user_id = $1)", test_user)
            await conn.execute("DELETE FROM shopping_lists WHERE user_id = $1", test_user)
        
        print("\n🎉 All API tests passed!")
        
        await conn.close()
        await redis_client.close()
        
    except Exception as e:
        print(f'❌ Error: {e}')
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_apis())

import React, { useEffect, useState } from "react";
import {
  Shield,
  Search,
  Plus,
  Edit,
  Trash2,
  Users,
  Key,
  AlertCircle,
  CheckCircle,
} from "lucide-react";
import { apiClient } from "../../lib/api";
import { PageHeader } from "../../components/navigation/Breadcrumbs";

interface Role {
  id: string;
  name: string;
  display_name: string;
  description: string;
  is_active: boolean;
  is_system: boolean;
  created_at: string;
  updated_at: string;
  permissions: string[];
  user_count?: number;
}

interface Permission {
  id: string;
  name: string;
  display_name: string;
  description: string;
  resource: string;
  action: string;
}

const RoleManagementPage: React.FC = () => {
  const [roles, setRoles] = useState<Role[]>([]);
  const [permissions, setPermissions] = useState<Permission[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState("");
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [editingRole, setEditingRole] = useState<Role | null>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalRoles, setTotalRoles] = useState(0);
  const pageSize = 20;

  const fetchRoles = async () => {
    try {
      setLoading(true);
      setError(null);

      // For now, we'll create mock data since the backend doesn't have role management endpoints yet
      // In a real implementation, this would call the actual role management API
      const mockRoles: Role[] = [
        {
          id: "1",
          name: "superadmin",
          display_name: "Super Administrator",
          description: "Full system access with all permissions",
          is_active: true,
          is_system: true,
          created_at: "2025-01-01T00:00:00Z",
          updated_at: "2025-01-01T00:00:00Z",
          permissions: [
            "user:read",
            "user:write",
            "user:delete",
            "admin:access",
          ],
          user_count: 1,
        },
        {
          id: "2",
          name: "user",
          display_name: "Regular User",
          description: "Standard user access with basic permissions",
          is_active: true,
          is_system: true,
          created_at: "2025-01-01T00:00:00Z",
          updated_at: "2025-01-01T00:00:00Z",
          permissions: [
            "search:read",
            "shopping_list:read",
            "shopping_list:write",
          ],
          user_count: 19,
        },
      ];

      // Filter based on search term if provided
      const filteredRoles = searchTerm
        ? mockRoles.filter(
            (role) =>
              role.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
              role.display_name
                .toLowerCase()
                .includes(searchTerm.toLowerCase()) ||
              role.description.toLowerCase().includes(searchTerm.toLowerCase())
          )
        : mockRoles;

      setRoles(filteredRoles);
      setTotalRoles(filteredRoles.length);
    } catch (err: any) {
      console.error("Failed to fetch roles:", err);
      setError(err.response?.data?.detail || "Failed to load roles");
    } finally {
      setLoading(false);
    }
  };

  const fetchPermissions = async () => {
    try {
      // Mock permissions data
      const mockPermissions: Permission[] = [
        {
          id: "1",
          name: "user:read",
          display_name: "Read Users",
          description: "View user information",
          resource: "user",
          action: "read",
        },
        {
          id: "2",
          name: "user:write",
          display_name: "Write Users",
          description: "Create and update users",
          resource: "user",
          action: "write",
        },
        {
          id: "3",
          name: "user:delete",
          display_name: "Delete Users",
          description: "Delete user accounts",
          resource: "user",
          action: "delete",
        },
        {
          id: "4",
          name: "admin:access",
          display_name: "Admin Access",
          description: "Access admin dashboard",
          resource: "admin",
          action: "access",
        },
        {
          id: "5",
          name: "search:read",
          display_name: "Search Products",
          description: "Search and view products",
          resource: "search",
          action: "read",
        },
        {
          id: "6",
          name: "shopping_list:read",
          display_name: "View Shopping Lists",
          description: "View shopping lists",
          resource: "shopping_list",
          action: "read",
        },
        {
          id: "7",
          name: "shopping_list:write",
          display_name: "Manage Shopping Lists",
          description: "Create and update shopping lists",
          resource: "shopping_list",
          action: "write",
        },
      ];
      setPermissions(mockPermissions);
    } catch (err: any) {
      console.error("Failed to fetch permissions:", err);
    }
  };

  useEffect(() => {
    fetchRoles();
    fetchPermissions();
  }, [currentPage, searchTerm]);

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    setCurrentPage(1);
    fetchRoles();
  };

  const deleteRole = async (roleId: string) => {
    if (
      !confirm(
        "Are you sure you want to delete this role? This action cannot be undone."
      )
    ) {
      return;
    }

    try {
      await apiClient.delete(`/roles/${roleId}`);
      fetchRoles(); // Refresh the list
    } catch (err: any) {
      console.error("Failed to delete role:", err);
      alert(
        "Failed to delete role: " + (err.response?.data?.detail || err.message)
      );
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  const totalPages = Math.ceil(totalRoles / pageSize);

  if (loading && roles.length === 0) {
    return (
      <div className="space-y-6">
        <PageHeader title="Role Management" />
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <PageHeader title="Role Management" />

      {/* Development Notice */}
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <div className="flex items-center">
          <AlertCircle className="h-5 w-5 text-blue-600 mr-2" />
          <div>
            <h3 className="text-sm font-medium text-blue-800">
              Development Preview
            </h3>
            <p className="text-sm text-blue-700 mt-1">
              This page shows mock role data. Full role management functionality
              will be implemented in a future phase.
            </p>
          </div>
        </div>
      </div>

      {/* Search and Actions */}
      <div className="bg-white rounded-lg shadow p-6">
        <div className="flex flex-col sm:flex-row gap-4 items-center justify-between">
          <form onSubmit={handleSearch} className="flex-1 max-w-md">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <input
                type="text"
                placeholder="Search roles..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10 pr-4 py-2 w-full border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>
          </form>

          <button
            onClick={() => setShowCreateModal(true)}
            className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            <Plus className="h-4 w-4" />
            Create Role
          </button>
        </div>
      </div>

      {error && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <div className="flex items-center">
            <AlertCircle className="h-5 w-5 text-red-600 mr-2" />
            <span className="text-red-800">{error}</span>
          </div>
        </div>
      )}

      {/* Roles Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {roles.map((role) => (
          <div
            key={role.id}
            className="bg-white rounded-lg shadow border border-gray-200"
          >
            <div className="p-6">
              <div className="flex items-start justify-between">
                <div className="flex items-center">
                  <div
                    className={`flex-shrink-0 h-10 w-10 rounded-lg flex items-center justify-center ${
                      role.is_system ? "bg-purple-100" : "bg-blue-100"
                    }`}
                  >
                    <Shield
                      className={`h-5 w-5 ${
                        role.is_system ? "text-purple-600" : "text-blue-600"
                      }`}
                    />
                  </div>
                  <div className="ml-3">
                    <h3 className="text-lg font-medium text-gray-900">
                      {role.display_name}
                    </h3>
                    <p className="text-sm text-gray-500">{role.name}</p>
                  </div>
                </div>

                <div className="flex items-center gap-1">
                  {role.is_active ? (
                    <CheckCircle
                      className="h-5 w-5 text-green-500"
                      title="Active"
                    />
                  ) : (
                    <AlertCircle
                      className="h-5 w-5 text-red-500"
                      title="Inactive"
                    />
                  )}
                  {role.is_system && (
                    <Shield
                      className="h-4 w-4 text-purple-500"
                      title="System Role"
                    />
                  )}
                </div>
              </div>

              <p className="mt-3 text-sm text-gray-600 line-clamp-2">
                {role.description || "No description provided"}
              </p>

              <div className="mt-4 flex items-center justify-between text-sm text-gray-500">
                <div className="flex items-center gap-4">
                  <div className="flex items-center gap-1">
                    <Users className="h-4 w-4" />
                    <span>{role.user_count || 0} users</span>
                  </div>
                  <div className="flex items-center gap-1">
                    <Key className="h-4 w-4" />
                    <span>{role.permissions?.length || 0} permissions</span>
                  </div>
                </div>
              </div>

              <div className="mt-4 text-xs text-gray-400">
                Created: {formatDate(role.created_at)}
              </div>

              {/* Permissions Preview */}
              {role.permissions && role.permissions.length > 0 && (
                <div className="mt-4">
                  <div className="text-xs font-medium text-gray-700 mb-2">
                    Permissions:
                  </div>
                  <div className="flex flex-wrap gap-1">
                    {role.permissions.slice(0, 3).map((permission) => (
                      <span
                        key={permission}
                        className="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-gray-100 text-gray-800"
                      >
                        {permission}
                      </span>
                    ))}
                    {role.permissions.length > 3 && (
                      <span className="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-gray-100 text-gray-800">
                        +{role.permissions.length - 3} more
                      </span>
                    )}
                  </div>
                </div>
              )}

              {/* Actions */}
              <div className="mt-6 flex items-center justify-end gap-2">
                <button
                  onClick={() => setEditingRole(role)}
                  className="p-2 text-gray-600 hover:text-blue-600 hover:bg-blue-50 rounded transition-colors"
                  title="Edit role"
                >
                  <Edit className="h-4 w-4" />
                </button>
                {!role.is_system && (
                  <button
                    onClick={() => deleteRole(role.id)}
                    className="p-2 text-gray-600 hover:text-red-600 hover:bg-red-50 rounded transition-colors"
                    title="Delete role"
                  >
                    <Trash2 className="h-4 w-4" />
                  </button>
                )}
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Empty State */}
      {roles.length === 0 && !loading && (
        <div className="text-center py-12">
          <Shield className="mx-auto h-12 w-12 text-gray-400" />
          <h3 className="mt-2 text-sm font-medium text-gray-900">
            No roles found
          </h3>
          <p className="mt-1 text-sm text-gray-500">
            {searchTerm
              ? "Try adjusting your search terms."
              : "Get started by creating a new role."}
          </p>
          {!searchTerm && (
            <div className="mt-6">
              <button
                onClick={() => setShowCreateModal(true)}
                className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
              >
                <Plus className="h-4 w-4 mr-2" />
                Create Role
              </button>
            </div>
          )}
        </div>
      )}

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="bg-white rounded-lg shadow p-4">
          <div className="flex items-center justify-between">
            <div className="text-sm text-gray-700">
              Showing {(currentPage - 1) * pageSize + 1} to{" "}
              {Math.min(currentPage * pageSize, totalRoles)} of {totalRoles}{" "}
              roles
            </div>
            <div className="flex gap-2">
              <button
                onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                disabled={currentPage === 1}
                className="px-3 py-1 border border-gray-300 rounded text-sm disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
              >
                Previous
              </button>
              <button
                onClick={() =>
                  setCurrentPage(Math.min(totalPages, currentPage + 1))
                }
                disabled={currentPage === totalPages}
                className="px-3 py-1 border border-gray-300 rounded text-sm disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
              >
                Next
              </button>
            </div>
          </div>
        </div>
      )}

      {/* TODO: Add Create/Edit Role Modal */}
      {showCreateModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-lg p-6 max-w-md w-full">
            <h3 className="text-lg font-medium text-gray-900 mb-4">
              Create New Role
            </h3>
            <p className="text-sm text-gray-600 mb-4">
              Role creation modal will be implemented in the next phase.
            </p>
            <div className="flex justify-end gap-2">
              <button
                onClick={() => setShowCreateModal(false)}
                className="px-4 py-2 text-gray-600 hover:text-gray-800"
              >
                Cancel
              </button>
              <button
                onClick={() => setShowCreateModal(false)}
                className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
              >
                OK
              </button>
            </div>
          </div>
        </div>
      )}

      {editingRole && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-lg p-6 max-w-md w-full">
            <h3 className="text-lg font-medium text-gray-900 mb-4">
              Edit Role: {editingRole.display_name}
            </h3>
            <p className="text-sm text-gray-600 mb-4">
              Role editing modal will be implemented in the next phase.
            </p>
            <div className="flex justify-end gap-2">
              <button
                onClick={() => setEditingRole(null)}
                className="px-4 py-2 text-gray-600 hover:text-gray-800"
              >
                Cancel
              </button>
              <button
                onClick={() => setEditingRole(null)}
                className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
              >
                OK
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default RoleManagementPage;

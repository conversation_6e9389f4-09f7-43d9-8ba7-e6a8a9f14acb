#!/usr/bin/env python3
"""
Authorization Middleware Test Script

Tests the role-based authorization middleware functionality including:
- Role checking
- Permission checking
- Resource ownership validation
- Superuser bypass functionality
"""

import asyncio
import asyncpg
import sys
from pathlib import Path
from unittest.mock import Mock

# Add the app directory to Python path
sys.path.append(str(Path(__file__).parent))

from app.config import settings
from app.middleware.authorization import (
    <PERSON><PERSON><PERSON><PERSON>,
    AuthorizationError,
    require_role,
    require_permission,
    require_superadmin,
    require_user_management,
    check_resource_ownership,
)
from app.models.user import User


class AuthorizationMiddlewareTester:
    """Tests the authorization middleware functionality."""
    
    def __init__(self):
        if settings:
            self.db_url = settings.DATABASE_URL.replace("postgresql+asyncpg://", "postgresql://")
        else:
            self.db_url = "postgresql://profident_user:profident_dev_password@localhost:5433/profident_dev"
        
        print(f"🔗 Using database URL: {self.db_url[:50]}...")
    
    async def get_connection(self):
        """Get database connection."""
        return await asyncpg.connect(self.db_url)
    
    def create_mock_user(
        self,
        user_id: str = "test-user-123",
        email: str = "<EMAIL>",
        roles: list = None,
        permissions: list = None,
        is_superuser: bool = False
    ) -> User:
        """Create a mock user for testing."""
        user = Mock(spec=User)
        user.id = user_id
        user.email = email
        user.roles = roles or []
        user.permissions = permissions or []
        user.is_superuser = is_superuser
        user.is_active = True
        return user
    
    async def test_role_checker_basic_functionality(self):
        """Test basic RoleChecker functionality."""
        print("\n🔍 Test 1: Basic RoleChecker functionality...")
        
        # Test 1.1: User with required role
        print("  1.1: User with required role...")
        user_with_role = self.create_mock_user(roles=["user"])
        role_checker = RoleChecker(required_roles=["user"])
        
        try:
            result = role_checker(user_with_role)
            print("  ✅ User with required role passed authorization")
        except AuthorizationError:
            print("  ❌ User with required role failed authorization")
            return False
        
        # Test 1.2: User without required role
        print("  1.2: User without required role...")
        user_without_role = self.create_mock_user(roles=["other"])
        
        try:
            result = role_checker(user_without_role)
            print("  ❌ User without required role should have failed authorization")
            return False
        except AuthorizationError:
            print("  ✅ User without required role correctly failed authorization")
        
        # Test 1.3: Superuser bypass
        print("  1.3: Superuser bypass...")
        superuser = self.create_mock_user(is_superuser=True)
        
        try:
            result = role_checker(superuser)
            print("  ✅ Superuser correctly bypassed role check")
        except AuthorizationError:
            print("  ❌ Superuser should have bypassed role check")
            return False
        
        return True
    
    async def test_permission_checker_functionality(self):
        """Test permission-based authorization."""
        print("\n🔐 Test 2: Permission-based authorization...")
        
        # Test 2.1: User with required permission
        print("  2.1: User with required permission...")
        user_with_permission = self.create_mock_user(permissions=["users.manage"])
        permission_checker = RoleChecker(required_permissions=["users.manage"])
        
        try:
            result = permission_checker(user_with_permission)
            print("  ✅ User with required permission passed authorization")
        except AuthorizationError:
            print("  ❌ User with required permission failed authorization")
            return False
        
        # Test 2.2: User without required permission
        print("  2.2: User without required permission...")
        user_without_permission = self.create_mock_user(permissions=["other.permission"])
        
        try:
            result = permission_checker(user_without_permission)
            print("  ❌ User without required permission should have failed authorization")
            return False
        except AuthorizationError:
            print("  ✅ User without required permission correctly failed authorization")
        
        return True
    
    async def test_multiple_roles_and_permissions(self):
        """Test authorization with multiple roles and permissions."""
        print("\n🎭 Test 3: Multiple roles and permissions...")
        
        # Test 3.1: Require ANY of multiple roles
        print("  3.1: Require ANY of multiple roles...")
        user_with_one_role = self.create_mock_user(roles=["user"])
        any_role_checker = RoleChecker(
            required_roles=["admin", "user"],
            require_all_roles=False
        )
        
        try:
            result = any_role_checker(user_with_one_role)
            print("  ✅ User with one of multiple required roles passed")
        except AuthorizationError:
            print("  ❌ User with one of multiple required roles failed")
            return False
        
        # Test 3.2: Require ALL of multiple roles
        print("  3.2: Require ALL of multiple roles...")
        user_with_partial_roles = self.create_mock_user(roles=["user"])
        all_roles_checker = RoleChecker(
            required_roles=["admin", "user"],
            require_all_roles=True
        )
        
        try:
            result = all_roles_checker(user_with_partial_roles)
            print("  ❌ User with partial roles should have failed")
            return False
        except AuthorizationError:
            print("  ✅ User with partial roles correctly failed")
        
        # Test 3.3: User with all required roles
        print("  3.3: User with all required roles...")
        user_with_all_roles = self.create_mock_user(roles=["admin", "user"])
        
        try:
            result = all_roles_checker(user_with_all_roles)
            print("  ✅ User with all required roles passed")
        except AuthorizationError:
            print("  ❌ User with all required roles failed")
            return False
        
        return True
    
    async def test_convenience_functions(self):
        """Test convenience authorization functions."""
        print("\n🛠️  Test 4: Convenience authorization functions...")
        
        # Test 4.1: require_role function
        print("  4.1: require_role function...")
        user_with_admin = self.create_mock_user(roles=["admin"])
        admin_checker = require_role("admin")
        
        try:
            result = admin_checker(user_with_admin)
            print("  ✅ require_role function works correctly")
        except AuthorizationError:
            print("  ❌ require_role function failed")
            return False
        
        # Test 4.2: require_permission function
        print("  4.2: require_permission function...")
        user_with_manage_permission = self.create_mock_user(permissions=["users.manage"])
        manage_checker = require_permission("users.manage")
        
        try:
            result = manage_checker(user_with_manage_permission)
            print("  ✅ require_permission function works correctly")
        except AuthorizationError:
            print("  ❌ require_permission function failed")
            return False
        
        return True
    
    async def test_resource_ownership(self):
        """Test resource ownership checking."""
        print("\n🏠 Test 5: Resource ownership checking...")
        
        # Test 5.1: User owns resource
        print("  5.1: User owns resource...")
        resource_owner = self.create_mock_user(user_id="owner-123")
        owns_resource = check_resource_ownership("owner-123", resource_owner)
        
        if owns_resource:
            print("  ✅ Resource ownership correctly identified")
        else:
            print("  ❌ Resource ownership check failed")
            return False
        
        # Test 5.2: User doesn't own resource
        print("  5.2: User doesn't own resource...")
        non_owner = self.create_mock_user(user_id="other-456")
        doesnt_own = check_resource_ownership("owner-123", non_owner, allow_admin_override=False)
        
        if not doesnt_own:
            print("  ✅ Non-ownership correctly identified")
        else:
            print("  ❌ Non-ownership check failed")
            return False
        
        # Test 5.3: Admin override
        print("  5.3: Admin override...")
        admin_user = self.create_mock_user(user_id="admin-789", roles=["admin"])
        admin_access = check_resource_ownership("owner-123", admin_user, allow_admin_override=True)
        
        if admin_access:
            print("  ✅ Admin override works correctly")
        else:
            print("  ❌ Admin override failed")
            return False
        
        return True
    
    async def test_real_database_integration(self):
        """Test authorization with real database users."""
        print("\n🗄️  Test 6: Real database integration...")
        
        try:
            conn = await self.get_connection()
            
            # Get a real user from database
            user_record = await conn.fetchrow("""
                SELECT u.*,
                       array_agg(DISTINCT r.name) FILTER (WHERE r.name IS NOT NULL) as roles,
                       array_agg(DISTINCT p.name) FILTER (WHERE p.name IS NOT NULL) as permissions
                FROM users u
                LEFT JOIN user_roles ur ON u.id = ur.user_id AND ur.is_active = TRUE
                LEFT JOIN roles r ON ur.role_id = r.id AND r.is_active = TRUE
                LEFT JOIN role_permissions rp ON r.id = rp.role_id AND rp.is_active = TRUE
                LEFT JOIN permissions p ON rp.permission_id = p.id
                GROUP BY u.id, u.email, u.hashed_password, u.full_name, u.is_active, u.is_superuser, u.created_at, u.updated_at
                LIMIT 1
            """)
            
            if not user_record:
                print("  ⚠️  No users found in database")
                return True
            
            # Create User object from database record
            user = User.from_record(user_record)
            print(f"  📋 Testing with user: {user.email}")
            print(f"  📋 User roles: {user.roles}")
            print(f"  📋 User permissions: {user.permissions[:5] if user.permissions else []}")
            
            # Test role checking with real user
            if user.roles and len(user.roles) > 0:
                role_checker = RoleChecker(required_roles=[user.roles[0]])
                try:
                    result = role_checker(user)
                    print("  ✅ Real user role checking works")
                except AuthorizationError:
                    print("  ❌ Real user role checking failed")
                    return False
            
            # Test permission checking with real user
            if user.permissions and len(user.permissions) > 0:
                permission_checker = RoleChecker(required_permissions=[user.permissions[0]])
                try:
                    result = permission_checker(user)
                    print("  ✅ Real user permission checking works")
                except AuthorizationError:
                    print("  ❌ Real user permission checking failed")
                    return False
            
            await conn.close()
            return True
            
        except Exception as e:
            print(f"  ❌ Database integration test failed: {e}")
            return False
    
    async def run_all_tests(self):
        """Run all authorization middleware tests."""
        print("🔐 Testing Authorization Middleware")
        print("=" * 60)
        
        tests = [
            ("Basic RoleChecker functionality", self.test_role_checker_basic_functionality),
            ("Permission-based authorization", self.test_permission_checker_functionality),
            ("Multiple roles and permissions", self.test_multiple_roles_and_permissions),
            ("Convenience authorization functions", self.test_convenience_functions),
            ("Resource ownership checking", self.test_resource_ownership),
            ("Real database integration", self.test_real_database_integration),
        ]
        
        results = []
        for test_name, test_func in tests:
            try:
                result = await test_func()
                results.append((test_name, result))
            except Exception as e:
                print(f"❌ {test_name} failed with exception: {e}")
                results.append((test_name, False))
        
        # Summary
        print("\n🎉 Authorization Middleware Test Summary")
        print("=" * 60)
        
        passed = 0
        total = len(results)
        
        for test_name, result in results:
            status = "✅ PASSED" if result else "❌ FAILED"
            print(f"{status}: {test_name}")
            if result:
                passed += 1
        
        print(f"\n📊 Results: {passed}/{total} tests passed")
        
        if passed == total:
            print("🎉 All authorization middleware tests passed!")
            return True
        else:
            print("❌ Some authorization middleware tests failed")
            return False


async def main():
    """Run authorization middleware tests."""
    tester = AuthorizationMiddlewareTester()
    success = await tester.run_all_tests()
    return success


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)

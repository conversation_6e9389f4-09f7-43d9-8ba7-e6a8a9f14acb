#!/usr/bin/env python3
"""
Test script for authentication middleware and JWT functionality
Verifies all authentication components are working correctly
"""

import asyncio
import sys
import json
from pathlib import Path

# Add the app directory to Python path
sys.path.insert(0, str(Path(__file__).parent / "app"))

from app.database import DatabaseManager
from app.services.auth_service import AuthService
from app.schemas.auth import UserCreate, UserLogin
from app.utils.security import create_access_token, verify_token


async def test_auth_service_imports():
    """Test authentication service imports."""
    print("🔍 Testing authentication service imports...")
    
    try:
        from app.services.auth_service import AuthService
        from app.models.user import User, UserRepository
        from app.schemas.auth import UserCreate, UserLogin, UserResponse
        from app.utils.security import create_access_token, verify_token
        
        print("✅ Authentication service imports successful")
        return True
        
    except Exception as e:
        print(f"❌ Authentication service import failed: {e}")
        return False


async def test_jwt_token_operations():
    """Test JWT token creation and verification."""
    print("\n🔍 Testing JWT token operations...")
    
    try:
        # Test token creation
        test_user_id = "test-user-123"
        test_email = "<EMAIL>"
        
        token = create_access_token(data={"sub": test_user_id, "email": test_email})
        print(f"✅ JWT token created successfully")
        
        # Test token verification
        payload = verify_token(token)
        print(f"✅ JWT token verified successfully")
        print(f"✅ Token payload: user_id={payload.get('sub')}, email={payload.get('email')}")
        
        if payload.get("sub") == test_user_id and payload.get("email") == test_email:
            print("✅ Token data integrity verified")
        else:
            print("❌ Token data integrity failed")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ JWT token operations test failed: {e}")
        return False


async def test_user_registration_and_authentication():
    """Test user registration and authentication flow."""
    print("\n🔍 Testing user registration and authentication...")
    
    try:
        db = DatabaseManager()
        await db.initialize()
        
        async with db.get_connection() as conn:
            import uuid
            import time
            
            # Create unique test user
            unique_email = f"auth_test_{int(time.time())}@example.com"
            test_user_data = UserCreate(
                email=unique_email,
                password="TestPassword123!",
                full_name="Auth Test User"
            )
            
            # Test user registration
            user, tokens = await AuthService.register_user(conn, test_user_data)
            print(f"✅ User registration successful: {user.email}")
            print(f"✅ Registration tokens generated: access_token length={len(tokens['access_token'])}")
            
            # Test user authentication
            login_data = UserLogin(
                email=unique_email,
                password="TestPassword123!"
            )
            
            auth_user, auth_tokens = await AuthService.authenticate_user(conn, login_data)
            print(f"✅ User authentication successful: {auth_user.email}")
            print(f"✅ Authentication tokens generated: access_token length={len(auth_tokens['access_token'])}")
            
            # Test get current user
            current_user = await AuthService.get_current_user(conn, auth_tokens['access_token'])
            print(f"✅ Get current user successful: {current_user.email}")
            
            # Test token refresh
            refresh_tokens = await AuthService.refresh_access_token(conn, auth_tokens['refresh_token'])
            print(f"✅ Token refresh successful: new access_token length={len(refresh_tokens['access_token'])}")
            
            # Clean up test user
            await conn.execute("DELETE FROM users WHERE email = $1", unique_email)
            print("✅ Test user cleaned up")
        
        await db.close()
        return True
        
    except Exception as e:
        print(f"❌ User registration and authentication test failed: {e}")
        return False


async def test_authentication_dependencies():
    """Test authentication dependencies and middleware."""
    print("\n🔍 Testing authentication dependencies...")
    
    try:
        from app.dependencies import get_current_user, get_current_active_user, get_current_superuser
        from app.dependencies import auth_rate_limiter, api_rate_limiter
        
        print("✅ Authentication dependencies imported successfully")
        
        # Test rate limiter configuration
        print(f"✅ Auth rate limiter: {auth_rate_limiter.max_requests} requests per {auth_rate_limiter.window_seconds}s")
        print(f"✅ API rate limiter: {api_rate_limiter.max_requests} requests per {api_rate_limiter.window_seconds}s")
        
        return True
        
    except Exception as e:
        print(f"❌ Authentication dependencies test failed: {e}")
        return False


async def test_auth_api_endpoints():
    """Test authentication API endpoints."""
    print("\n🔍 Testing authentication API endpoints...")
    
    try:
        from fastapi.testclient import TestClient
        from app.main import app
        
        with TestClient(app) as client:
            # Test auth health endpoint
            response = client.get("/api/v1/auth/health")
            if response.status_code == 200:
                data = response.json()
                print(f"✅ Auth health endpoint: {data['message']}")
            else:
                print(f"❌ Auth health endpoint failed: {response.status_code}")
                return False
            
            # Test register endpoint structure (should return validation error without data)
            response = client.post("/api/v1/auth/register", json={})
            if response.status_code == 422:  # Validation error expected
                print("✅ Register endpoint structure working (validation error expected)")
            else:
                print(f"⚠️ Register endpoint: {response.status_code} (expected 422)")
            
            # Test login endpoint structure (should return validation error without data)
            response = client.post("/api/v1/auth/login", json={})
            if response.status_code == 422:  # Validation error expected
                print("✅ Login endpoint structure working (validation error expected)")
            else:
                print(f"⚠️ Login endpoint: {response.status_code} (expected 422)")
            
            # Test protected endpoint without auth (should return 401)
            response = client.get("/api/v1/auth/me")
            if response.status_code == 401:
                print("✅ Protected endpoint correctly requires authentication")
            else:
                print(f"⚠️ Protected endpoint: {response.status_code} (expected 401)")
            
            # Test refresh endpoint structure
            response = client.post("/api/v1/auth/refresh", json={})
            if response.status_code == 422:  # Validation error expected
                print("✅ Refresh endpoint structure working (validation error expected)")
            else:
                print(f"⚠️ Refresh endpoint: {response.status_code} (expected 422)")
        
        return True
        
    except Exception as e:
        print(f"❌ Authentication API endpoints test failed: {e}")
        return False


async def test_password_security():
    """Test password hashing and validation."""
    print("\n🔍 Testing password security...")
    
    try:
        from app.utils.security import get_password_hash, verify_password, validate_password_strength
        
        # Test password hashing
        test_password = "TestPassword123!"
        hashed = get_password_hash(test_password)
        print(f"✅ Password hashing successful: hash length={len(hashed)}")
        
        # Test password verification
        if verify_password(test_password, hashed):
            print("✅ Password verification successful")
        else:
            print("❌ Password verification failed")
            return False
        
        # Test wrong password
        if not verify_password("WrongPassword", hashed):
            print("✅ Wrong password correctly rejected")
        else:
            print("❌ Wrong password incorrectly accepted")
            return False
        
        # Test password strength validation
        is_valid, errors = validate_password_strength("TestPassword123!")
        if is_valid:
            print("✅ Strong password validation successful")
        else:
            print(f"❌ Strong password validation failed: {errors}")
            return False
        
        # Test weak password
        is_valid, errors = validate_password_strength("weak")
        if not is_valid:
            print(f"✅ Weak password correctly rejected: {errors}")
        else:
            print("❌ Weak password incorrectly accepted")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Password security test failed: {e}")
        return False


async def cleanup():
    """Clean up resources."""
    print("\n🧹 Cleaning up...")
    
    try:
        # Clean up any remaining test data
        db = DatabaseManager()
        await db.initialize()
        
        async with db.get_connection() as conn:
            # Clean up test users
            await conn.execute("""
                DELETE FROM users 
                WHERE email LIKE '<EMAIL>'
                OR created_at > NOW() - INTERVAL '1 hour'
                AND email LIKE '%test%'
            """)
            print("✅ Test data cleaned up")
        
        await db.close()
    except Exception as e:
        print(f"⚠️ Cleanup warning: {e}")


async def main():
    """Run all authentication middleware tests."""
    print("🚀 Starting ProfiDent Authentication Middleware Tests\n")
    print("Testing JWT authentication, user management, and security features")
    print("=" * 70)
    
    tests = [
        ("Authentication Service Import", test_auth_service_imports),
        ("JWT Token Operations", test_jwt_token_operations),
        ("User Registration & Authentication", test_user_registration_and_authentication),
        ("Authentication Dependencies", test_authentication_dependencies),
        ("Authentication API Endpoints", test_auth_api_endpoints),
        ("Password Security", test_password_security),
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = await test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} failed with exception: {e}")
            results.append((test_name, False))
    
    # Cleanup
    await cleanup()
    
    # Summary
    print("\n" + "=" * 70)
    print("📊 TEST SUMMARY")
    print("=" * 70)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{status} - {test_name}")
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All authentication middleware tests passed!")
        print("✅ JWT authentication system is working correctly")
        print("🔐 User management and security features are functional")
        print("🛡️ Authentication middleware is ready for production")
        return True
    else:
        print("❌ Some tests failed. Check authentication middleware configuration.")
        return False


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)

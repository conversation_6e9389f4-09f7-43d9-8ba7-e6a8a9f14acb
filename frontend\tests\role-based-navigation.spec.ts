import { test, expect } from '@playwright/test';

test.describe('Role-Based Navigation Tests', () => {
  test.beforeEach(async ({ page }) => {
    // Navigate to the application
    await page.goto('http://localhost:3000');
  });

  test('should show basic navigation for unauthenticated users', async ({ page }) => {
    // Check that login/register options are visible
    await expect(page.locator('text=Sign In')).toBeVisible();
    
    // Take screenshot for documentation
    await page.screenshot({ path: 'test-results/unauthenticated-navigation.png' });
  });

  test('should show role-based navigation for superadmin user', async ({ page }) => {
    // Login as superadmin
    await page.click('text=Sign In');
    await page.fill('input[type="email"]', '<EMAIL>');
    await page.fill('input[type="password"]', 'admin123!@#');
    await page.click('button[type="submit"]');
    
    // Wait for login to complete
    await page.waitForSelector('text=ProfiDent', { timeout: 10000 });
    
    // Check base navigation items are visible
    await expect(page.locator('text=Home')).toBeVisible();
    await expect(page.locator('text=Search')).toBeVisible();
    await expect(page.locator('text=Shopping Lists')).toBeVisible();
    await expect(page.locator('text=Profile')).toBeVisible();
    
    // Check admin navigation section is visible
    await expect(page.locator('text=Administration')).toBeVisible();
    await expect(page.locator('text=Admin Dashboard')).toBeVisible();
    await expect(page.locator('text=User Management')).toBeVisible();
    await expect(page.locator('text=Role Management')).toBeVisible();
    
    // Check user dropdown shows role indicator
    await page.click('[data-testid="user-menu-button"], .user-menu-button, button:has-text("<EMAIL>")');
    await expect(page.locator('text=Super Administrator')).toBeVisible();
    
    // Check admin menu items in dropdown
    await expect(page.locator('text=Admin Dashboard')).toBeVisible();
    await expect(page.locator('text=User Management')).toBeVisible();
    await expect(page.locator('text=Role Management')).toBeVisible();
    
    // Take screenshot for documentation
    await page.screenshot({ path: 'test-results/superadmin-navigation.png' });
  });

  test('should preserve search functionality with role-based navigation', async ({ page }) => {
    // Login as superadmin
    await page.click('text=Sign In');
    await page.fill('input[type="email"]', '<EMAIL>');
    await page.fill('input[type="password"]', 'admin123!@#');
    await page.click('button[type="submit"]');
    
    // Wait for login to complete
    await page.waitForSelector('text=ProfiDent', { timeout: 10000 });
    
    // Navigate to search page
    await page.click('text=Search');
    await page.waitForSelector('input[placeholder*="Search"]', { timeout: 5000 });
    
    // Test search functionality
    const searchInput = page.locator('input[placeholder*="Search"]').first();
    await searchInput.fill('dental');
    
    // Wait for search results or suggestions
    await page.waitForTimeout(1000);
    
    // Check that search interface is working
    const hasResults = await page.locator('.search-results, .suggestions, [data-testid="search-results"]').count() > 0;
    const hasNoResultsMessage = await page.locator('text=No results found, text=No products found').count() > 0;
    
    // Either results should be shown or a "no results" message
    expect(hasResults || hasNoResultsMessage).toBeTruthy();
    
    // Take screenshot for documentation
    await page.screenshot({ path: 'test-results/search-with-role-navigation.png' });
  });

  test('should preserve shopping list functionality with role-based navigation', async ({ page }) => {
    // Login as superadmin
    await page.click('text=Sign In');
    await page.fill('input[type="email"]', '<EMAIL>');
    await page.fill('input[type="password"]', 'admin123!@#');
    await page.click('button[type="submit"]');
    
    // Wait for login to complete
    await page.waitForSelector('text=ProfiDent', { timeout: 10000 });
    
    // Navigate to shopping lists page
    await page.click('text=Shopping Lists');
    await page.waitForTimeout(2000);
    
    // Check that shopping lists page loads
    const hasShoppingListContent = await page.locator('text=Shopping Lists, text=My Lists, text=Default List').count() > 0;
    expect(hasShoppingListContent).toBeTruthy();
    
    // Check shopping list icon in header
    await expect(page.locator('.shopping-cart-icon, [data-testid="shopping-list-icon"]')).toBeVisible();
    
    // Take screenshot for documentation
    await page.screenshot({ path: 'test-results/shopping-lists-with-role-navigation.png' });
  });

  test('should handle navigation clicks correctly', async ({ page }) => {
    // Login as superadmin
    await page.click('text=Sign In');
    await page.fill('input[type="email"]', '<EMAIL>');
    await page.fill('input[type="password"]', 'admin123!@#');
    await page.click('button[type="submit"]');
    
    // Wait for login to complete
    await page.waitForSelector('text=ProfiDent', { timeout: 10000 });
    
    // Test navigation to different pages
    await page.click('text=Home');
    await expect(page).toHaveURL(/.*\//);
    
    await page.click('text=Search');
    await expect(page).toHaveURL(/.*\/search/);
    
    await page.click('text=Shopping Lists');
    await expect(page).toHaveURL(/.*\/shopping-lists/);
    
    await page.click('text=Profile');
    await expect(page).toHaveURL(/.*\/profile/);
    
    // Test admin navigation
    await page.click('text=Admin Dashboard');
    await expect(page).toHaveURL(/.*\/admin/);
    
    // Take screenshot for documentation
    await page.screenshot({ path: 'test-results/navigation-routing.png' });
  });

  test('should show correct active navigation states', async ({ page }) => {
    // Login as superadmin
    await page.click('text=Sign In');
    await page.fill('input[type="email"]', '<EMAIL>');
    await page.fill('input[type="password"]', 'admin123!@#');
    await page.click('button[type="submit"]');
    
    // Wait for login to complete
    await page.waitForSelector('text=ProfiDent', { timeout: 10000 });
    
    // Navigate to search and check active state
    await page.click('text=Search');
    
    // Check that search navigation item has active styling
    const searchNavItem = page.locator('a:has-text("Search")').first();
    const searchClasses = await searchNavItem.getAttribute('class');
    expect(searchClasses).toContain('bg-primary-100');
    
    // Navigate to admin dashboard and check active state
    await page.click('text=Admin Dashboard');
    
    // Check that admin dashboard navigation item has active styling
    const adminNavItem = page.locator('a:has-text("Admin Dashboard")').first();
    const adminClasses = await adminNavItem.getAttribute('class');
    expect(adminClasses).toContain('bg-primary-100');
    
    // Take screenshot for documentation
    await page.screenshot({ path: 'test-results/active-navigation-states.png' });
  });

  test('should handle mobile navigation correctly', async ({ page }) => {
    // Set mobile viewport
    await page.setViewportSize({ width: 375, height: 667 });
    
    // Login as superadmin
    await page.click('text=Sign In');
    await page.fill('input[type="email"]', '<EMAIL>');
    await page.fill('input[type="password"]', 'admin123!@#');
    await page.click('button[type="submit"]');
    
    // Wait for login to complete
    await page.waitForSelector('text=ProfiDent', { timeout: 10000 });
    
    // Check that mobile menu button is visible
    const menuButton = page.locator('button:has([data-testid="menu-icon"]), button:has(.lucide-menu)');
    await expect(menuButton).toBeVisible();
    
    // Click menu button to open sidebar
    await menuButton.click();
    
    // Check that navigation items are visible in mobile sidebar
    await expect(page.locator('text=Home')).toBeVisible();
    await expect(page.locator('text=Search')).toBeVisible();
    await expect(page.locator('text=Administration')).toBeVisible();
    
    // Take screenshot for documentation
    await page.screenshot({ path: 'test-results/mobile-navigation.png' });
  });
});

#!/usr/bin/env python3
"""
Check current permissions in the database
"""

import asyncio
import asyncpg
import sys
from pathlib import Path

# Add the app directory to Python path
sys.path.append(str(Path(__file__).parent))

from app.config import settings

async def check_permissions():
    """Check current permissions in database."""
    try:
        db_url = settings.DATABASE_URL.replace('postgresql+asyncpg://', 'postgresql://')
        conn = await asyncpg.connect(db_url)

        print('📋 Current Permissions in Database:')
        permissions = await conn.fetch('SELECT name, resource, action, description FROM permissions ORDER BY resource, action')

        current_resource = None
        for perm in permissions:
            if perm['resource'] != current_resource:
                current_resource = perm['resource']
                print(f'\n🔹 {current_resource.upper()}:')
            print(f'  - {perm["name"]}: {perm["description"]}')

        print('\n\n👥 User Roles and Permissions:')

        # Check what roles exist and their permissions
        roles_data = await conn.fetch('''
            SELECT r.name as role_name,
                   array_agg(p.name) as permissions
            FROM roles r
            LEFT JOIN role_permissions rp ON r.id = rp.role_id AND rp.is_active = TRUE
            LEFT JOIN permissions p ON rp.permission_id = p.id
            WHERE r.is_active = TRUE
            GROUP BY r.id, r.name
            ORDER BY r.name
        ''')

        for role in roles_data:
            print(f'\n🔹 Role: {role["role_name"]}')
            perms = role['permissions'] if role['permissions'] and role['permissions'][0] else []
            if perms:
                for perm in sorted(perms):
                    print(f'  - {perm}')
            else:
                print('  - No permissions assigned')

        await conn.close()

    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    asyncio.run(check_permissions())

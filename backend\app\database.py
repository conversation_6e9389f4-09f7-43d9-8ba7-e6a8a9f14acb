"""
ProfiDent Database Connection Manager
Handles PostgreSQL connection pooling with asyncpg
"""

import asyncio
import logging
from typing import Optional, AsyncGenerator
from contextlib import asynccontextmanager

import asyncpg
import redis.asyncio as redis
from asyncpg import Pool, Connection

try:
    from .config import settings, get_pool_config, get_redis_config
except ImportError:
    from config import settings, get_pool_config, get_redis_config

# Configure logging
logger = logging.getLogger(__name__)


class DatabaseManager:
    """Database connection manager with connection pooling."""
    
    def __init__(self):
        self.pool: Optional[Pool] = None
        self.redis_client: Optional[redis.Redis] = None
        self._is_initialized = False
    
    async def initialize(self) -> None:
        """Initialize database connections and pools."""
        if self._is_initialized:
            logger.warning("Database manager already initialized")
            return
        
        try:
            # Initialize PostgreSQL connection pool
            await self._init_postgres_pool()
            
            # Initialize Redis connection
            await self._init_redis_client()
            
            self._is_initialized = True
            logger.info("Database manager initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize database manager: {e}")
            await self.close()
            raise
    
    async def _init_postgres_pool(self) -> None:
        """Initialize PostgreSQL connection pool."""
        pool_config = get_pool_config()
        
        # Remove asyncpg prefix for direct connection
        db_url = settings.DATABASE_URL.replace("postgresql+asyncpg://", "postgresql://")
        
        logger.info(f"Creating PostgreSQL connection pool with config: {pool_config}")
        
        self.pool = await asyncpg.create_pool(
            db_url,
            **pool_config,
            init=self._init_connection
        )
        
        logger.info(f"PostgreSQL connection pool created successfully")
    
    async def _init_redis_client(self) -> None:
        """Initialize Redis client."""
        redis_config = get_redis_config()
        
        logger.info(f"Creating Redis client connection")
        
        self.redis_client = redis.Redis(**redis_config)
        
        # Test Redis connection
        await self.redis_client.ping()
        logger.info("Redis client connected successfully")
    
    async def _init_connection(self, connection: Connection) -> None:
        """Initialize individual database connections."""
        # Set connection-specific settings
        await connection.execute("SET timezone = 'UTC'")
        await connection.execute("SET statement_timeout = '30s'")
        
        # Enable extensions if needed
        try:
            await connection.execute("CREATE EXTENSION IF NOT EXISTS vector")
            await connection.execute("CREATE EXTENSION IF NOT EXISTS pg_trgm")
            await connection.execute("CREATE EXTENSION IF NOT EXISTS btree_gin")
        except Exception as e:
            logger.warning(f"Could not enable extensions: {e}")
    
    async def close(self) -> None:
        """Close all database connections."""
        if self.pool:
            await self.pool.close()
            self.pool = None
            logger.info("PostgreSQL connection pool closed")
        
        if self.redis_client:
            await self.redis_client.close()
            self.redis_client = None
            logger.info("Redis client closed")
        
        self._is_initialized = False
    
    @asynccontextmanager
    async def get_connection(self) -> AsyncGenerator[Connection, None]:
        """Get a database connection from the pool."""
        if not self.pool:
            raise RuntimeError("Database pool not initialized")
        
        async with self.pool.acquire() as connection:
            try:
                yield connection
            except Exception as e:
                logger.error(f"Database connection error: {e}")
                raise
    
    async def execute_query(self, query: str, *args) -> list:
        """Execute a query and return results."""
        async with self.get_connection() as conn:
            return await conn.fetch(query, *args)
    
    async def execute_one(self, query: str, *args):
        """Execute a query and return single result."""
        async with self.get_connection() as conn:
            return await conn.fetchrow(query, *args)
    
    async def execute_scalar(self, query: str, *args):
        """Execute a query and return scalar value."""
        async with self.get_connection() as conn:
            return await conn.fetchval(query, *args)
    
    async def execute_command(self, query: str, *args) -> str:
        """Execute a command (INSERT, UPDATE, DELETE)."""
        async with self.get_connection() as conn:
            return await conn.execute(query, *args)
    
    async def get_pool_status(self) -> dict:
        """Get connection pool status information."""
        if not self.pool:
            return {"status": "not_initialized"}
        
        return {
            "status": "active",
            "size": self.pool.get_size(),
            "min_size": self.pool.get_min_size(),
            "max_size": self.pool.get_max_size(),
            "idle_connections": self.pool.get_idle_size(),
        }
    
    async def health_check(self) -> dict:
        """Perform health check on database connections."""
        health_status = {
            "postgres": {"status": "unknown", "error": None},
            "redis": {"status": "unknown", "error": None}
        }
        
        # Check PostgreSQL
        try:
            result = await self.execute_scalar("SELECT 1")
            if result == 1:
                health_status["postgres"]["status"] = "healthy"
            else:
                health_status["postgres"]["status"] = "unhealthy"
                health_status["postgres"]["error"] = "Unexpected result"
        except Exception as e:
            health_status["postgres"]["status"] = "unhealthy"
            health_status["postgres"]["error"] = str(e)
        
        # Check Redis
        try:
            if self.redis_client:
                await self.redis_client.ping()
                health_status["redis"]["status"] = "healthy"
            else:
                health_status["redis"]["status"] = "not_initialized"
        except Exception as e:
            health_status["redis"]["status"] = "unhealthy"
            health_status["redis"]["error"] = str(e)
        
        return health_status


# Global database manager instance
db_manager = DatabaseManager()


async def get_database() -> DatabaseManager:
    """Get database manager instance."""
    if not db_manager._is_initialized:
        await db_manager.initialize()
    return db_manager


async def get_db_connection() -> AsyncGenerator[Connection, None]:
    """Dependency for getting database connection."""
    db = await get_database()
    async with db.get_connection() as connection:
        yield connection


async def get_redis_client() -> redis.Redis:
    """Get Redis client."""
    db = await get_database()
    if not db.redis_client:
        raise RuntimeError("Redis client not initialized")
    return db.redis_client


# Startup and shutdown handlers
async def startup_database():
    """Initialize database connections on startup."""
    logger.info("Starting database connections...")
    await db_manager.initialize()
    
    # Verify connections
    health = await db_manager.health_check()
    logger.info(f"Database health check: {health}")


async def shutdown_database():
    """Close database connections on shutdown."""
    logger.info("Closing database connections...")
    await db_manager.close()


# Connection pool testing utilities
async def test_connection_pool():
    """Test connection pool functionality."""
    logger.info("Testing connection pool...")
    
    # Test multiple concurrent connections
    async def test_connection():
        async with db_manager.get_connection() as conn:
            result = await conn.fetchval("SELECT version()")
            return result[:50]
    
    # Create multiple concurrent tasks
    tasks = [test_connection() for _ in range(10)]
    results = await asyncio.gather(*tasks)
    
    logger.info(f"Connection pool test completed. Results: {len(results)} connections")
    return results

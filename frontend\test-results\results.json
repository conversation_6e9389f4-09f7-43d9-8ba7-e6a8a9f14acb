{"config": {"configFile": "D:\\online work\\profident\\frontend\\playwright.config.ts", "rootDir": "D:/online work/profident/frontend/tests", "forbidOnly": false, "fullyParallel": true, "globalSetup": null, "globalTeardown": null, "globalTimeout": 0, "grep": {}, "grepInvert": null, "maxFailures": 0, "metadata": {"actualWorkers": 1}, "preserveOutput": "always", "reporter": [["html", null], ["json", {"outputFile": "test-results/results.json"}], ["list", null]], "reportSlowTests": {"max": 5, "threshold": 300000}, "quiet": false, "projects": [{"outputDir": "D:/online work/profident/frontend/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 1}, "id": "chromium", "name": "chromium", "testDir": "D:/online work/profident/frontend/tests", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 60000}, {"outputDir": "D:/online work/profident/frontend/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 1}, "id": "firefox", "name": "firefox", "testDir": "D:/online work/profident/frontend/tests", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 60000}, {"outputDir": "D:/online work/profident/frontend/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 1}, "id": "webkit", "name": "webkit", "testDir": "D:/online work/profident/frontend/tests", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 60000}, {"outputDir": "D:/online work/profident/frontend/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 1}, "id": "Mobile Chrome", "name": "Mobile Chrome", "testDir": "D:/online work/profident/frontend/tests", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 60000}, {"outputDir": "D:/online work/profident/frontend/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 1}, "id": "Mobile Safari", "name": "Mobile Safari", "testDir": "D:/online work/profident/frontend/tests", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 60000}], "shard": null, "updateSnapshots": "missing", "updateSourceMethod": "patch", "version": "1.54.1", "workers": 1, "webServer": {"command": "npm run dev", "url": "http://localhost:3000", "reuseExistingServer": true, "timeout": 120000}}, "suites": [{"title": "rbac-edge-cases.spec.ts", "file": "rbac-edge-cases.spec.ts", "column": 0, "line": 0, "specs": [], "suites": [{"title": "RBAC Edge Cases and Security", "file": "rbac-edge-cases.spec.ts", "line": 23, "column": 6, "specs": [], "suites": [{"title": "Token and Authentication Edge Cases", "file": "rbac-edge-cases.spec.ts", "line": 33, "column": 8, "specs": [{"title": "should handle invalid token gracefully", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 0, "parallelIndex": 0, "status": "passed", "duration": 5182, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-08-01T10:30:51.222Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "b8c18c310c1ec7038b82-dfdb57f4704d7bed8873", "file": "rbac-edge-cases.spec.ts", "line": 34, "column": 5}, {"title": "should handle malformed token gracefully", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 0, "parallelIndex": 0, "status": "passed", "duration": 1581, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-08-01T10:30:56.570Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "b8c18c310c1ec7038b82-6d79e135ac2e0fbcc5b1", "file": "rbac-edge-cases.spec.ts", "line": 47, "column": 5}, {"title": "should handle missing user data gracefully", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 0, "parallelIndex": 0, "status": "passed", "duration": 3089, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-08-01T10:30:58.158Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "b8c18c310c1ec7038b82-4e9d04b4c2748ad18f65", "file": "rbac-edge-cases.spec.ts", "line": 59, "column": 5}]}, {"title": "Permission Boundary Testing", "file": "rbac-edge-cases.spec.ts", "line": 97, "column": 8, "specs": [{"title": "should properly handle route guards", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 0, "parallelIndex": 0, "status": "passed", "duration": 3085, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-08-01T10:31:01.252Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "b8c18c310c1ec7038b82-d4383cb60a609f4a30f6", "file": "rbac-edge-cases.spec.ts", "line": 118, "column": 5}, {"title": "should handle permission checks consistently", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 0, "parallelIndex": 0, "status": "passed", "duration": 3784, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-08-01T10:31:04.346Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "b8c18c310c1ec7038b82-786622481b4b5d4e5366", "file": "rbac-edge-cases.spec.ts", "line": 136, "column": 5}, {"title": "should maintain UI state consistency across navigation", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 0, "parallelIndex": 0, "status": "passed", "duration": 2561, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-08-01T10:31:08.136Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "b8c18c310c1ec7038b82-2ca6d0843e6de457c666", "file": "rbac-edge-cases.spec.ts", "line": 154, "column": 5}]}, {"title": "Network and Error Handling", "file": "rbac-edge-cases.spec.ts", "line": 177, "column": 8, "specs": [{"title": "should handle network errors during authentication", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 0, "parallelIndex": 0, "status": "passed", "duration": 3573, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-08-01T10:31:10.703Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "b8c18c310c1ec7038b82-320a7664d67e7d0b22f2", "file": "rbac-edge-cases.spec.ts", "line": 178, "column": 5}, {"title": "should handle API errors gracefully", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 0, "parallelIndex": 0, "status": "passed", "duration": 2887, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-08-01T10:31:14.281Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "b8c18c310c1ec7038b82-19004eecc2067dec6cef", "file": "rbac-edge-cases.spec.ts", "line": 201, "column": 5}]}, {"title": "UI State and Consistency", "file": "rbac-edge-cases.spec.ts", "line": 237, "column": 8, "specs": [{"title": "should maintain role-based UI after page refresh", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 0, "parallelIndex": 0, "status": "passed", "duration": 3100, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-08-01T10:31:17.172Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "b8c18c310c1ec7038b82-8061f7858c2a70ec1a2f", "file": "rbac-edge-cases.spec.ts", "line": 257, "column": 5}, {"title": "should handle rapid navigation between protected routes", "ok": false, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 0, "parallelIndex": 0, "status": "failed", "duration": 3883, "error": {"message": "Error: expect.toBeVisible: Error: strict mode violation: getByR<PERSON>('link', { name: 'Admin Dashboard' }) resolved to 2 elements:\n    1) <a href=\"/admin\" class=\"flex items-center px-4 py-2 text-sm font-medium rounded-md transition-colors text-gray-600 hover:bg-gray-100 hover:text-gray-900\">…</a> aka getByRole('list').getByRole('link', { name: 'Admin Dashboard' })\n    2) <a href=\"/admin\" class=\"hover:text-gray-700 transition-colors\">Admin Dashboard</a> aka getByRole('main').getByRole('link', { name: 'Admin Dashboard' })\n\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for getBy<PERSON>ole('link', { name: 'Admin Dashboard' })\u001b[22m\n", "stack": "Error: expect.toBeVisible: Error: strict mode violation: getByR<PERSON>('link', { name: 'Admin Dashboard' }) resolved to 2 elements:\n    1) <a href=\"/admin\" class=\"flex items-center px-4 py-2 text-sm font-medium rounded-md transition-colors text-gray-600 hover:bg-gray-100 hover:text-gray-900\">…</a> aka getByRole('list').getByRole('link', { name: 'Admin Dashboard' })\n    2) <a href=\"/admin\" class=\"hover:text-gray-700 transition-colors\">Admin Dashboard</a> aka getByRole('main').getByRole('link', { name: 'Admin Dashboard' })\n\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for getBy<PERSON>ole('link', { name: 'Admin Dashboard' })\u001b[22m\n\n    at D:\\online work\\profident\\frontend\\tests\\rbac-edge-cases.spec.ts:292:9", "location": {"file": "D:\\online work\\profident\\frontend\\tests\\rbac-edge-cases.spec.ts", "column": 9, "line": 292}, "snippet": "\u001b[0m \u001b[90m 290 |\u001b[39m       \u001b[36mawait\u001b[39m expect(\n \u001b[90m 291 |\u001b[39m         page\u001b[33m.\u001b[39mgetByRole(\u001b[32m\"link\"\u001b[39m\u001b[33m,\u001b[39m { name\u001b[33m:\u001b[39m \u001b[32m\"Admin Dashboard\"\u001b[39m })\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 292 |\u001b[39m       )\u001b[33m.\u001b[39mtoBeVisible()\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m         \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 293 |\u001b[39m     })\u001b[33m;\u001b[39m\n \u001b[90m 294 |\u001b[39m\n \u001b[90m 295 |\u001b[39m     test(\u001b[32m\"should handle browser back/forward with role-based routes\"\u001b[39m\u001b[33m,\u001b[39m \u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\u001b[0m"}, "errors": [{"location": {"file": "D:\\online work\\profident\\frontend\\tests\\rbac-edge-cases.spec.ts", "column": 9, "line": 292}, "message": "Error: expect.toBeVisible: Error: strict mode violation: getBy<PERSON><PERSON>('link', { name: 'Admin Dashboard' }) resolved to 2 elements:\n    1) <a href=\"/admin\" class=\"flex items-center px-4 py-2 text-sm font-medium rounded-md transition-colors text-gray-600 hover:bg-gray-100 hover:text-gray-900\">…</a> aka getByRole('list').getByRole('link', { name: 'Admin Dashboard' })\n    2) <a href=\"/admin\" class=\"hover:text-gray-700 transition-colors\">Admin Dashboard</a> aka getByRole('main').getByRole('link', { name: 'Admin Dashboard' })\n\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for getBy<PERSON>ole('link', { name: 'Admin Dashboard' })\u001b[22m\n\n\n\u001b[0m \u001b[90m 290 |\u001b[39m       \u001b[36mawait\u001b[39m expect(\n \u001b[90m 291 |\u001b[39m         page\u001b[33m.\u001b[39mgetByRole(\u001b[32m\"link\"\u001b[39m\u001b[33m,\u001b[39m { name\u001b[33m:\u001b[39m \u001b[32m\"Admin Dashboard\"\u001b[39m })\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 292 |\u001b[39m       )\u001b[33m.\u001b[39mtoBeVisible()\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m         \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 293 |\u001b[39m     })\u001b[33m;\u001b[39m\n \u001b[90m 294 |\u001b[39m\n \u001b[90m 295 |\u001b[39m     test(\u001b[32m\"should handle browser back/forward with role-based routes\"\u001b[39m\u001b[33m,\u001b[39m \u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\u001b[0m\n\u001b[2m    at D:\\online work\\profident\\frontend\\tests\\rbac-edge-cases.spec.ts:292:9\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-08-01T10:31:20.279Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "D:\\online work\\profident\\frontend\\test-results\\rbac-edge-cases-RBAC-Edge--2b33f-on-between-protected-routes-chromium\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "D:\\online work\\profident\\frontend\\test-results\\rbac-edge-cases-RBAC-Edge--2b33f-on-between-protected-routes-chromium\\video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "D:\\online work\\profident\\frontend\\test-results\\rbac-edge-cases-RBAC-Edge--2b33f-on-between-protected-routes-chromium\\error-context.md"}], "errorLocation": {"file": "D:\\online work\\profident\\frontend\\tests\\rbac-edge-cases.spec.ts", "column": 9, "line": 292}}], "status": "unexpected"}], "id": "b8c18c310c1ec7038b82-83e003131fe2b1f615c1", "file": "rbac-edge-cases.spec.ts", "line": 277, "column": 5}, {"title": "should handle browser back/forward with role-based routes", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": -1, "parallelIndex": -1, "status": "skipped", "duration": 0, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-08-01T10:31:25.269Z", "annotations": [], "attachments": []}], "status": "skipped"}], "id": "b8c18c310c1ec7038b82-0a9c6bd96ec418ae4df6", "file": "rbac-edge-cases.spec.ts", "line": 295, "column": 5}]}]}]}, {"title": "role-based-access-control.spec.ts", "file": "role-based-access-control.spec.ts", "column": 0, "line": 0, "specs": [], "suites": [{"title": "Role-Based Access Control (RBAC)", "file": "role-based-access-control.spec.ts", "line": 24, "column": 6, "specs": [], "suites": [{"title": "Unauthenticated User Access", "file": "role-based-access-control.spec.ts", "line": 37, "column": 8, "specs": [{"title": "should redirect to login when accessing protected routes", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 1, "parallelIndex": 0, "status": "passed", "duration": 3003, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-08-01T10:31:26.845Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "809f53d152cfd84cbdca-338fa64b53f281ff6716", "file": "role-based-access-control.spec.ts", "line": 38, "column": 5}, {"title": "should allow access to public routes", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 1, "parallelIndex": 0, "status": "passed", "duration": 2033, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-08-01T10:31:30.026Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "809f53d152cfd84cbdca-52052538225d23c525c9", "file": "role-based-access-control.spec.ts", "line": 66, "column": 5}]}, {"title": "Superadmin User Access", "file": "role-based-access-control.spec.ts", "line": 81, "column": 8, "specs": [{"title": "should have access to all protected routes", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 1, "parallelIndex": 0, "status": "passed", "duration": 3354, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-08-01T10:31:32.068Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "809f53d152cfd84cbdca-40165bc5c7ab73542e82", "file": "role-based-access-control.spec.ts", "line": 105, "column": 5}, {"title": "should display admin navigation items", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 1, "parallelIndex": 0, "status": "passed", "duration": 2268, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-08-01T10:31:35.430Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "809f53d152cfd84cbdca-2833c3b0d628ad8c4c4e", "file": "role-based-access-control.spec.ts", "line": 143, "column": 5}, {"title": "should display role information in header dropdown", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 1, "parallelIndex": 0, "status": "passed", "duration": 2310, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-08-01T10:31:37.704Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "809f53d152cfd84cbdca-7af2c14271796a0ddd5b", "file": "role-based-access-control.spec.ts", "line": 159, "column": 5}, {"title": "should display comprehensive role information in profile", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 1, "parallelIndex": 0, "status": "passed", "duration": 3928, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-08-01T10:31:40.021Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "809f53d152cfd84cbdca-6bc3cdd989f1a1426d3a", "file": "role-based-access-control.spec.ts", "line": 190, "column": 5}, {"title": "should display authorization badges on protected pages", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 1, "parallelIndex": 0, "status": "passed", "duration": 2415, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-08-01T10:31:43.955Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "809f53d152cfd84cbdca-2ae1959f680afb0ac0cc", "file": "role-based-access-control.spec.ts", "line": 245, "column": 5}, {"title": "should allow logout and redirect to login", "ok": false, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 1, "parallelIndex": 0, "status": "failed", "duration": 18295, "error": {"message": "TimeoutError: page.waitForURL: Timeout 15000ms exceeded.\n=========================== logs ===========================\nwaiting for navigation to \"http://localhost:3000/\" until \"load\"\n============================================================", "stack": "TimeoutError: page.waitForURL: Timeout 15000ms exceeded.\n=========================== logs ===========================\nwaiting for navigation to \"http://localhost:3000/\" until \"load\"\n============================================================\n    at D:\\online work\\profident\\frontend\\tests\\role-based-access-control.spec.ts:97:14", "location": {"file": "D:\\online work\\profident\\frontend\\tests\\role-based-access-control.spec.ts", "column": 14, "line": 97}, "snippet": "\u001b[0m \u001b[90m  95 |\u001b[39m       \u001b[90m// Submit login form and wait for navigation\u001b[39m\n \u001b[90m  96 |\u001b[39m       \u001b[36mawait\u001b[39m \u001b[33mPromise\u001b[39m\u001b[33m.\u001b[39mall([\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m  97 |\u001b[39m         page\u001b[33m.\u001b[39mwaitForURL(\u001b[32m`${BASE_URL}/`\u001b[39m\u001b[33m,\u001b[39m { timeout\u001b[33m:\u001b[39m \u001b[35m15000\u001b[39m })\u001b[33m,\u001b[39m\n \u001b[90m     |\u001b[39m              \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m  98 |\u001b[39m         page\u001b[33m.\u001b[39mgetByRole(\u001b[32m\"button\"\u001b[39m\u001b[33m,\u001b[39m { name\u001b[33m:\u001b[39m \u001b[32m\"Sign in\"\u001b[39m })\u001b[33m.\u001b[39mclick()\u001b[33m,\u001b[39m\n \u001b[90m  99 |\u001b[39m       ])\u001b[33m;\u001b[39m\n \u001b[90m 100 |\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "D:\\online work\\profident\\frontend\\tests\\role-based-access-control.spec.ts", "column": 14, "line": 97}, "message": "TimeoutError: page.waitForURL: Timeout 15000ms exceeded.\n=========================== logs ===========================\nwaiting for navigation to \"http://localhost:3000/\" until \"load\"\n============================================================\n\n\u001b[0m \u001b[90m  95 |\u001b[39m       \u001b[90m// Submit login form and wait for navigation\u001b[39m\n \u001b[90m  96 |\u001b[39m       \u001b[36mawait\u001b[39m \u001b[33mPromise\u001b[39m\u001b[33m.\u001b[39mall([\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m  97 |\u001b[39m         page\u001b[33m.\u001b[39mwaitForURL(\u001b[32m`${BASE_URL}/`\u001b[39m\u001b[33m,\u001b[39m { timeout\u001b[33m:\u001b[39m \u001b[35m15000\u001b[39m })\u001b[33m,\u001b[39m\n \u001b[90m     |\u001b[39m              \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m  98 |\u001b[39m         page\u001b[33m.\u001b[39mgetByRole(\u001b[32m\"button\"\u001b[39m\u001b[33m,\u001b[39m { name\u001b[33m:\u001b[39m \u001b[32m\"Sign in\"\u001b[39m })\u001b[33m.\u001b[39mclick()\u001b[33m,\u001b[39m\n \u001b[90m  99 |\u001b[39m       ])\u001b[33m;\u001b[39m\n \u001b[90m 100 |\u001b[39m\u001b[0m\n\u001b[2m    at D:\\online work\\profident\\frontend\\tests\\role-based-access-control.spec.ts:97:14\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-08-01T10:31:46.376Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "D:\\online work\\profident\\frontend\\test-results\\role-based-access-control--80123-ogout-and-redirect-to-login-chromium\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "D:\\online work\\profident\\frontend\\test-results\\role-based-access-control--80123-ogout-and-redirect-to-login-chromium\\video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "D:\\online work\\profident\\frontend\\test-results\\role-based-access-control--80123-ogout-and-redirect-to-login-chromium\\error-context.md"}], "errorLocation": {"file": "D:\\online work\\profident\\frontend\\tests\\role-based-access-control.spec.ts", "column": 14, "line": 97}}], "status": "unexpected"}], "id": "809f53d152cfd84cbdca-0a2dbe20389516c50375", "file": "role-based-access-control.spec.ts", "line": 262, "column": 5}]}, {"title": "Search Functionality Preservation", "file": "role-based-access-control.spec.ts", "line": 283, "column": 8, "specs": [{"title": "should preserve search functionality after RBAC implementation", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": -1, "parallelIndex": -1, "status": "skipped", "duration": 0, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-08-01T10:32:05.756Z", "annotations": [], "attachments": []}], "status": "skipped"}], "id": "809f53d152cfd84cbdca-1260aaab005ad917d4eb", "file": "role-based-access-control.spec.ts", "line": 307, "column": 5}]}, {"title": "Performance and Error Handling", "file": "role-based-access-control.spec.ts", "line": 347, "column": 8, "specs": [{"title": "should handle authentication errors gracefully", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": -1, "parallelIndex": -1, "status": "skipped", "duration": 0, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-08-01T10:32:05.765Z", "annotations": [], "attachments": []}], "status": "skipped"}], "id": "809f53d152cfd84cbdca-b0b7b9ca35c16a82fea4", "file": "role-based-access-control.spec.ts", "line": 348, "column": 5}, {"title": "should load role-based UI elements quickly", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": -1, "parallelIndex": -1, "status": "skipped", "duration": 0, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-08-01T10:32:05.775Z", "annotations": [], "attachments": []}], "status": "skipped"}], "id": "809f53d152cfd84cbdca-0315b7b9a633d62cbe3d", "file": "role-based-access-control.spec.ts", "line": 367, "column": 5}]}]}]}], "errors": [], "stats": {"startTime": "2025-08-01T10:30:49.604Z", "duration": 76219.00899999999, "expected": 16, "skipped": 4, "unexpected": 2, "flaky": 0}}
# ProfiDent - Dental Product Search Application

## 📋 Project Overview

ProfiDent is a comprehensive dental product search and price comparison platform designed for dental professionals. The application features:

- **FastAPI Backend**: High-performance REST API with PostgreSQL database containing 339K+ dental products
- **React Frontend**: Modern TypeScript-based UI with real-time search and filtering
- **Advanced Search**: Full-text search with instant suggestions and comprehensive filtering
- **Shopping Lists**: Product management and list creation functionality
- **Role-Based Access Control**: Secure authentication with user roles and permissions
- **Performance Optimized**: Redis caching, connection pooling, and optimized database queries

## 🔧 Prerequisites

Before setting up ProfiDent, ensure you have the following software installed:

### Required Software
- **Python 3.11+** - Backend runtime
- **Node.js 16.0+** - Frontend development and build tools
- **PostgreSQL 13+** - Primary database
- **Redis 6.0+** - Caching and session management
- **Git** - Version control

### Recommended Tools
- **pgAdmin** or **DBeaver** - Database management
- **Redis Desktop Manager** - Redis monitoring
- **VS Code** - Development environment

## 🚀 Installation Steps

### 1. Clone the Repository

```bash
git clone <repository-url>
cd profident
```

### 2. Backend Setup

#### Create Python Virtual Environment

```bash
# Navigate to backend directory
cd backend

# Create virtual environment
python -m venv venv

# Activate virtual environment
# On Windows:
venv\Scripts\activate
# On macOS/Linux:
source venv/bin/activate
```

#### Install Python Dependencies

```bash
# Install all backend dependencies
pip install -r requirements.txt
```

#### Database Setup

1. **Install and Start PostgreSQL**
   ```bash
   # Default PostgreSQL port: 5432
   # ProfiDent uses custom port: 5433
   ```

2. **Create Database and User**
   ```sql
   -- Connect to PostgreSQL as superuser
   CREATE USER profident_user WITH PASSWORD 'profident_dev_password';
   CREATE DATABASE profident_dev OWNER profident_user;
   GRANT ALL PRIVILEGES ON DATABASE profident_dev TO profident_user;
   ```

3. **Configure Database Connection**
   
   Create `.env` file in `backend/` directory:
   ```env
   # Database Configuration
   DATABASE_URL=postgresql+asyncpg://profident_user:profident_dev_password@localhost:5433/profident_dev
   POSTGRES_USER=profident_user
   POSTGRES_PASSWORD=profident_dev_password
   POSTGRES_DB=profident_dev
   POSTGRES_HOST=localhost
   POSTGRES_PORT=5433
   
   # Redis Configuration
   REDIS_URL=redis://localhost:6380
   REDIS_HOST=localhost
   REDIS_PORT=6380
   
   # Security
   SECRET_KEY=your-secret-key-change-in-production-environment
   ```

#### Redis Setup

1. **Install and Start Redis**
   ```bash
   # Default Redis port: 6379
   # ProfiDent uses custom port: 6380
   redis-server --port 6380
   ```

#### Initialize Database

```bash
# Run database migrations and setup
python run_migration.py

# Import product data (339K+ products)
python import_products.py

# Create initial superadmin user
python check_and_create_superadmin.py
```

### 3. Frontend Setup

#### Navigate to Frontend Directory

```bash
cd ../frontend
```

#### Install Node.js Dependencies

```bash
# Install all frontend dependencies
npm install

# Install Playwright for testing (optional)
npm run test:install
```

#### Configure Frontend Environment

Create `.env` file in `frontend/` directory:
```env
VITE_API_BASE_URL=http://localhost:8000
VITE_APP_TITLE=ProfiDent
```

## 🏃‍♂️ Running the Application

### Start Backend Server

```bash
# Navigate to backend directory
cd backend

# Activate virtual environment (if not already active)
# Windows:
venv\Scripts\activate
# macOS/Linux:
source venv/bin/activate

# Start the FastAPI server
python start_real_server.py
```

**Backend will be available at:**
- API Server: http://localhost:8000
- API Documentation: http://localhost:8000/docs
- Health Check: http://localhost:8000/health

### Start Frontend Development Server

```bash
# Navigate to frontend directory (in a new terminal)
cd frontend

# Start the React development server
npm run dev
```

**Frontend will be available at:**
- Application: http://localhost:5173
- Development Server: http://localhost:5173

## ✅ Verification Steps

### 1. Backend Health Check

```bash
# Test API health
curl http://localhost:8000/health

# Expected response:
# {"status":"healthy","message":"API server is running"}
```

### 2. Database Connection Test

```bash
cd backend
python -c "
import asyncio
from app.database import get_database

async def test():
    db = await get_database()
    print('✅ Database connected successfully')

asyncio.run(test())
"
```

### 3. Frontend Connection Test

1. Open http://localhost:5173 in your browser
2. You should see the ProfiDent homepage
3. Try searching for "dental" - you should see instant suggestions
4. Register a new account to test authentication

### 4. Full System Test

```bash
# Run comprehensive backend tests
cd backend
python test_critical_functionality.py

# Run frontend E2E tests (optional)
cd ../frontend
npm run test:e2e
```

## 🔧 Troubleshooting

### Common Issues and Solutions

#### Backend Issues

**Database Connection Failed**
```bash
# Check PostgreSQL is running on correct port
netstat -an | grep 5433

# Verify database exists
psql -h localhost -p 5433 -U profident_user -d profident_dev
```

**Redis Connection Failed**
```bash
# Check Redis is running on correct port
redis-cli -p 6380 ping

# Expected response: PONG
```

**Import Errors**
```bash
# Ensure virtual environment is activated
which python  # Should point to venv/bin/python

# Reinstall dependencies
pip install -r requirements.txt --force-reinstall
```

#### Frontend Issues

**Node Modules Issues**
```bash
# Clear npm cache and reinstall
rm -rf node_modules package-lock.json
npm install
```

**Port Already in Use**
```bash
# Kill process using port 5173
# Windows:
netstat -ano | findstr :5173
taskkill /PID <PID> /F

# macOS/Linux:
lsof -ti:5173 | xargs kill -9
```

**API Connection Issues**
- Verify backend is running on http://localhost:8000
- Check CORS settings in backend configuration
- Ensure `.env` file has correct API URL

## 🛠️ Development Workflow

### Backend Development

```bash
# Start development server with auto-reload
cd backend
uvicorn app.main:app --reload --host 0.0.0.0 --port 8000

# Run specific tests
python test_search_api.py
python test_auth_system.py

# Database operations
python run_migration.py  # Run migrations
python optimize_database.py  # Optimize performance
```

### Frontend Development

```bash
# Start development server
npm run dev

# Type checking
npm run type-check

# Linting
npm run lint

# Build for production
npm run build
```

### Testing

```bash
# Backend testing
cd backend
python test_comprehensive_search.py
python test_security_comprehensive.py

# Frontend testing
cd frontend
npm run test:e2e
npm run test:e2e:ui  # Interactive mode
```

### Database Management

```bash
# Backup database
pg_dump -h localhost -p 5433 -U profident_user profident_dev > backup.sql

# Restore database
psql -h localhost -p 5433 -U profident_user profident_dev < backup.sql

# Check database stats
python backend/test_imported_data.py
```

## 🎯 Default Credentials

### Superadmin Account
- **Email**: <EMAIL>
- **Password**: admin123!@#
- **Role**: superadmin (full system access)

### Test User Account
- **Email**: <EMAIL>  
- **Password**: user123!@#
- **Role**: user (standard access)

## 📊 Performance Monitoring

### Key Metrics to Monitor

- **Search Response Time**: < 100ms for suggestions, < 500ms for full search
- **Database Connection Pool**: Monitor active/idle connections
- **Redis Cache Hit Rate**: Should be > 80% for search operations
- **API Response Times**: Monitor via http://localhost:8000/docs

### Performance Testing

```bash
# Test search performance
cd backend
python test_search_security_performance.py

# Test concurrent performance
python test_communication_patterns.py
```

---

## 🎉 Success!

If you've followed all steps successfully, you should now have:

✅ **Backend API** running on http://localhost:8000  
✅ **Frontend App** running on http://localhost:5173  
✅ **Database** with 339K+ dental products  
✅ **Redis Cache** for optimal performance  
✅ **Authentication** system with RBAC  
✅ **Search Functionality** with instant suggestions  

**Next Steps**: Explore the application, test search functionality, create shopping lists, and review the API documentation at http://localhost:8000/docs

For additional help or issues, check the troubleshooting section above or review the comprehensive test files in the `backend/` directory.

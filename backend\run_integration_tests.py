#!/usr/bin/env python3
"""
Simple Integration Test Runner
Runs integration tests without pytest
"""

import sys
import time
from pathlib import Path
from fastapi.testclient import TestClient

# Add the app directory to Python path
sys.path.insert(0, str(Path(__file__).parent / "app"))

from app.main import app


def test_complete_workflow():
    """Test complete user workflow."""
    print("🧪 Testing complete user workflow...")
    
    client = TestClient(app)
    unique_email = f"integration_test_{int(time.time())}@example.com"
    
    # 1. Register user
    register_data = {
        "email": unique_email,
        "password": "TestPassword123!",
        "full_name": "Integration Test User"
    }
    
    response = client.post("/api/v1/auth/register", json=register_data)
    if response.status_code != 201:
        print(f"❌ Registration failed: {response.status_code} - {response.text}")
        return False
    
    register_result = response.json()
    if "user" not in register_result or "token" not in register_result:
        print("❌ Invalid registration response format")
        return False
    
    access_token = register_result["token"]["access_token"]
    headers = {"Authorization": f"Bearer {access_token}"}
    
    print("  ✅ User registration successful")
    
    # 2. Test authentication
    response = client.get("/api/v1/auth/me", headers=headers)
    if response.status_code != 200:
        print(f"❌ Authentication failed: {response.status_code}")
        return False
    
    user_data = response.json()
    if user_data["email"] != unique_email:
        print("❌ Wrong user data returned")
        return False
    
    print("  ✅ User authentication working")
    
    # 3. Test search
    response = client.get("/api/v1/search/?q=dental", headers=headers)
    if response.status_code != 200:
        print(f"❌ Search failed: {response.status_code}")
        return False
    
    search_result = response.json()
    if "results" not in search_result or search_result["total"] == 0:
        print("❌ No search results found")
        return False
    
    print(f"  ✅ Search working ({search_result['total']} results found)")
    
    # 4. Test product categories
    response = client.get("/api/v1/products/categories/", headers=headers)
    if response.status_code != 200:
        print(f"❌ Get categories failed: {response.status_code}")
        return False
    
    categories_result = response.json()
    if "categories" not in categories_result:
        print("❌ No categories in response")
        return False
    
    print(f"  ✅ Product categories working ({len(categories_result['categories'])} categories)")
    
    # 5. Test shopping list creation
    list_data = {
        "name": "Integration Test List",
        "description": "Created during integration testing"
    }
    response = client.post("/api/v1/shopping-lists/", json=list_data, headers=headers)
    if response.status_code != 201:
        print(f"❌ Create shopping list failed: {response.status_code}")
        return False
    
    list_result = response.json()
    if list_result["name"] != list_data["name"]:
        print("❌ Wrong list name")
        return False
    
    shopping_list_id = list_result["id"]
    print("  ✅ Shopping list creation working")
    
    # 6. Test adding item to shopping list
    response = client.get("/api/v1/search/?q=dental&limit=1", headers=headers)
    search_result = response.json()
    if search_result["results"]:
        product_id = search_result["results"][0]["id"]
        
        item_data = {
            "product_id": product_id,
            "quantity": 2,
            "notes": "Integration test item"
        }
        response = client.post(f"/api/v1/shopping-lists/{shopping_list_id}/items", 
                             json=item_data, headers=headers)
        if response.status_code != 201:
            print(f"❌ Add item failed: {response.status_code}")
            return False
        
        item_result = response.json()
        if item_result["product_id"] != product_id:
            print("❌ Wrong product added")
            return False
        
        print("  ✅ Shopping list item management working")
    
    # 7. Test shopping list analysis
    response = client.get(f"/api/v1/shopping-lists/{shopping_list_id}/analysis", headers=headers)
    if response.status_code != 200:
        print(f"❌ Analysis failed: {response.status_code}")
        return False
    
    analysis_result = response.json()
    if "analysis" not in analysis_result:
        print("❌ No analysis in response")
        return False
    
    print("  ✅ Shopping list analysis working")
    
    print("🎉 Complete user workflow test PASSED")
    return True


def test_search_performance():
    """Test search performance."""
    print("🧪 Testing search performance...")
    
    client = TestClient(app)
    
    # Create test user
    unique_email = f"perf_test_{int(time.time())}@example.com"
    register_data = {
        "email": unique_email,
        "password": "TestPassword123!",
        "full_name": "Performance Test User"
    }
    
    response = client.post("/api/v1/auth/register", json=register_data)
    if response.status_code != 201:
        print(f"❌ Registration failed: {response.status_code}")
        return False
    
    access_token = response.json()["token"]["access_token"]
    headers = {"Authorization": f"Bearer {access_token}"}
    
    # Test search performance
    search_tests = [
        {"query": "dental", "min_results": 1000},
        {"query": "implant", "min_results": 100},
        {"query": "crown", "min_results": 100},
    ]
    
    for test in search_tests:
        start_time = time.time()
        response = client.get(f"/api/v1/search/?q={test['query']}", headers=headers)
        response_time = (time.time() - start_time) * 1000
        
        if response.status_code != 200:
            print(f"❌ Search '{test['query']}' failed: {response.status_code}")
            return False
        
        result = response.json()
        if result["total"] < test["min_results"]:
            print(f"❌ Query '{test['query']}' returned {result['total']} results, expected at least {test['min_results']}")
            return False
        
        if result["search_time_ms"] > 1000:
            print(f"❌ Search took {result['search_time_ms']}ms, should be under 1000ms")
            return False
        
        print(f"  ✅ Search '{test['query']}': {result['total']} results in {result['search_time_ms']}ms (API: {response_time:.1f}ms)")
    
    print("🎉 Search performance test PASSED")
    return True


def test_error_handling():
    """Test error handling."""
    print("🧪 Testing error handling...")
    
    client = TestClient(app)
    
    # Test unauthenticated requests
    response = client.get("/api/v1/auth/me")
    if response.status_code != 401:
        print(f"❌ Should require authentication, got {response.status_code}")
        return False
    
    response = client.get("/api/v1/search/?q=dental")
    if response.status_code != 401:
        print(f"❌ Search should require authentication, got {response.status_code}")
        return False
    
    # Test invalid authentication
    headers = {"Authorization": "Bearer invalid_token"}
    response = client.get("/api/v1/auth/me", headers=headers)
    if response.status_code != 401:
        print(f"❌ Should reject invalid token, got {response.status_code}")
        return False
    
    print("  ✅ Authentication errors handled correctly")
    
    # Create valid user for further tests
    unique_email = f"error_test_{int(time.time())}@example.com"
    register_data = {
        "email": unique_email,
        "password": "TestPassword123!",
        "full_name": "Error Test User"
    }
    
    response = client.post("/api/v1/auth/register", json=register_data)
    if response.status_code != 201:
        print(f"❌ Registration failed: {response.status_code}")
        return False
    
    access_token = response.json()["token"]["access_token"]
    headers = {"Authorization": f"Bearer {access_token}"}
    
    # Test invalid product ID
    response = client.get("/api/v1/products/999999999", headers=headers)
    if response.status_code != 404:
        print(f"❌ Should return 404 for invalid product ID, got {response.status_code}")
        return False
    
    # Test invalid shopping list ID
    response = client.get("/api/v1/shopping-lists/999999999", headers=headers)
    if response.status_code != 404:
        print(f"❌ Should return 404 for invalid shopping list ID, got {response.status_code}")
        return False
    
    # Test duplicate user registration
    response = client.post("/api/v1/auth/register", json=register_data)
    if response.status_code != 400:
        print(f"❌ Should reject duplicate email, got {response.status_code}")
        return False
    
    print("  ✅ Resource errors handled correctly")
    print("🎉 Error handling test PASSED")
    return True


def main():
    """Run all integration tests."""
    print("🚀 ProfiDent Integration Tests")
    print("=" * 60)
    
    tests = [
        test_complete_workflow,
        test_search_performance,
        test_error_handling,
    ]
    
    passed_tests = 0
    total_tests = len(tests)
    
    for test in tests:
        try:
            if test():
                passed_tests += 1
            else:
                print(f"❌ Test {test.__name__} FAILED")
        except Exception as e:
            print(f"❌ Test {test.__name__} FAILED with exception: {e}")
    
    print("\n" + "=" * 60)
    print(f"📊 INTEGRATION TEST RESULTS: {passed_tests}/{total_tests} PASSED")
    
    if passed_tests == total_tests:
        print("🎉 ALL INTEGRATION TESTS PASSED!")
        print("✅ API is ready for production")
    else:
        print(f"⚠️ {total_tests - passed_tests} tests failed")
    
    print("=" * 60)
    
    return passed_tests == total_tests


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

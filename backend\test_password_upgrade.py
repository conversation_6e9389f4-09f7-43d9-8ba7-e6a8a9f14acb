#!/usr/bin/env python3
"""
Test script for password upgrade functionality during authentication.
"""

import asyncio
import aiohttp
import json

async def test_password_upgrade():
    """Test password upgrade during authentication."""
    print("🔄 Testing Password Upgrade During Authentication")
    print("=" * 60)
    
    base_url = "http://localhost:8000"
    
    async with aiohttp.ClientSession() as session:
        # Test 1: Register a new user (will use current algorithm)
        print("\n1. Registering new user with current algorithm...")
        register_data = {
            "email": "<EMAIL>",
            "password": "UpgradeTest123!",
            "full_name": "Upgrade Test User"
        }
        
        try:
            async with session.post(f"{base_url}/api/v1/auth/register", json=register_data) as response:
                if response.status in [201, 400]:  # 400 if user already exists
                    print("✅ User registration handled correctly")
                    if response.status == 201:
                        response_data = await response.json()
                        print(f"   User created with algorithm: {response_data.get('user', {}).get('id', 'N/A')}")
                else:
                    print(f"❌ Unexpected registration status: {response.status}")
                    print(await response.text())
        except Exception as e:
            print(f"❌ Registration error: {e}")
        
        # Test 2: Login with the user (should work normally)
        print("\n2. Testing login with current password hash...")
        login_data = {
            "email": "<EMAIL>",
            "password": "UpgradeTest123!"
        }
        
        try:
            async with session.post(f"{base_url}/api/v1/auth/login", json=login_data) as response:
                if response.status == 200:
                    print("✅ Login successful")
                    response_data = await response.json()
                    print(f"   User authenticated: {response_data.get('user', {}).get('email', 'N/A')}")
                elif response.status == 401:
                    print("❌ Login failed - incorrect credentials")
                else:
                    print(f"❌ Unexpected login status: {response.status}")
                    print(await response.text())
        except Exception as e:
            print(f"❌ Login error: {e}")
        
        # Test 3: Test with wrong password
        print("\n3. Testing login with wrong password...")
        wrong_login_data = {
            "email": "<EMAIL>",
            "password": "WrongPassword123!"
        }
        
        try:
            async with session.post(f"{base_url}/api/v1/auth/login", json=wrong_login_data) as response:
                if response.status == 401:
                    print("✅ Wrong password correctly rejected")
                else:
                    print(f"❌ Expected 401, got {response.status}")
                    print(await response.text())
        except Exception as e:
            print(f"❌ Wrong password test error: {e}")
        
        # Test 4: Test multiple logins (to verify upgrade doesn't break anything)
        print("\n4. Testing multiple consecutive logins...")
        for i in range(3):
            try:
                async with session.post(f"{base_url}/api/v1/auth/login", json=login_data) as response:
                    if response.status == 200:
                        print(f"✅ Login {i+1} successful")
                    else:
                        print(f"❌ Login {i+1} failed with status {response.status}")
            except Exception as e:
                print(f"❌ Login {i+1} error: {e}")
    
    print("\n✅ Password upgrade testing completed!")

if __name__ == "__main__":
    asyncio.run(test_password_upgrade())

import { describe, it, expect, vi } from 'vitest'
import { screen } from '@testing-library/react'
import { render } from '../../../test-utils'
import SearchResults from '../SearchResults'
import { Product } from '../../../types'

// Mock the search store
vi.mock('../../../stores/searchStore', () => ({
  useSearchStore: () => ({
    total: 100,
    searchTime: 45.2,
  }),
}))

const mockProducts: Product[] = [
  {
    id: 1,
    name: 'Dental Implant System',
    brand: 'Nobel Biocare',
    seller: 'Benco',
    maincat: 'Implants',
    price: '299.99',
    url: 'https://example.com/product/1',
    search_text: 'dental implant system',
    created_at: '2024-01-01T00:00:00Z',
    updated_at: '2024-01-01T00:00:00Z',
    mfr: 'NB123',
    rank: 0.95,
  },
  {
    id: 2,
    name: 'Composite Filling Material',
    brand: '3M',
    seller: '<PERSON>',
    maincat: 'Restorative',
    price: 'Price not available',
    url: 'https://example.com/product/2',
    search_text: 'composite filling material',
    created_at: '2024-01-01T00:00:00Z',
    updated_at: '2024-01-01T00:00:00Z',
    rank: 0.87,
  },
]

describe('SearchResults', () => {
  it('renders loading state', () => {
    render(<SearchResults results={[]} isLoading={true} query="dental" />)
    
    // Should show loading skeletons
    expect(document.querySelectorAll('.animate-pulse')).toHaveLength(5)
  })

  it('renders empty state when no results', () => {
    render(<SearchResults results={[]} isLoading={false} query="nonexistent" />)
    
    expect(screen.getByText('No results found')).toBeInTheDocument()
    expect(screen.getByText(/We couldn't find any products matching "nonexistent"/)).toBeInTheDocument()
  })

  it('renders search results with product information', () => {
    render(<SearchResults results={mockProducts} isLoading={false} query="dental" />)
    
    // Check if products are rendered
    expect(screen.getByText('Dental Implant System')).toBeInTheDocument()
    expect(screen.getByText('Composite Filling Material')).toBeInTheDocument()
    
    // Check product details
    expect(screen.getByText('Nobel Biocare')).toBeInTheDocument()
    expect(screen.getByText('3M')).toBeInTheDocument()
    expect(screen.getByText('Benco')).toBeInTheDocument()
    expect(screen.getByText('Henry Schein')).toBeInTheDocument()
    
    // Check prices
    expect(screen.getByText('$299.99')).toBeInTheDocument()
    expect(screen.getByText('Price not available')).toBeInTheDocument()
  })

  it('displays search statistics', () => {
    render(<SearchResults results={mockProducts} isLoading={false} query="dental" />)
    
    expect(screen.getByText(/Showing 2 of 100 results/)).toBeInTheDocument()
    expect(screen.getByText(/45ms/)).toBeInTheDocument()
  })

  it('shows MFR codes when available', () => {
    render(<SearchResults results={mockProducts} isLoading={false} query="dental" />)
    
    expect(screen.getByText('NB123')).toBeInTheDocument()
  })

  it('shows match percentage when rank is available', () => {
    render(<SearchResults results={mockProducts} isLoading={false} query="dental" />)
    
    expect(screen.getByText('95% match')).toBeInTheDocument()
    expect(screen.getByText('87% match')).toBeInTheDocument()
  })

  it('renders action buttons for each product', () => {
    render(<SearchResults results={mockProducts} isLoading={false} query="dental" />)
    
    const addToListButtons = screen.getAllByText('Add to List')
    const visitSiteButtons = screen.getAllByText('Visit Site')
    
    expect(addToListButtons).toHaveLength(2)
    expect(visitSiteButtons).toHaveLength(2)
  })

  it('highlights search terms in product names', () => {
    render(<SearchResults results={mockProducts} isLoading={false} query="dental" />)
    
    // The highlightSearchTerm function should wrap matching terms in <mark> tags
    const productName = screen.getByText('Dental Implant System')
    expect(productName.innerHTML).toContain('mark')
  })
})

version: '3.8'

services:
  # PostgreSQL Database with pgvector extension for development
  postgres:
    image: pgvector/pgvector:pg16
    container_name: profident_postgres_dev
    environment:
      POSTGRES_DB: profident_dev
      POSTGRES_USER: profident_user
      POSTGRES_PASSWORD: profident_dev_password
      POSTGRES_HOST_AUTH_METHOD: trust
    ports:
      - "5433:5432"  # Different port to avoid conflicts
    volumes:
      - postgres_dev_data:/var/lib/postgresql/data
      - ./scripts/init-db.sql:/docker-entrypoint-initdb.d/init-db.sql
      - ./scripts:/scripts  # Mount scripts directory for easy access
    networks:
      - profident_dev_network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U profident_user -d profident_dev"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Redis for development caching
  redis:
    image: redis:7-alpine
    container_name: profident_redis_dev
    ports:
      - "6380:6379"  # Different port to avoid conflicts
    volumes:
      - redis_dev_data:/data
    networks:
      - profident_dev_network
    restart: unless-stopped
    command: redis-server --appendonly yes
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5

  # pgAdmin for database management (development only)
  pgadmin:
    image: dpage/pgadmin4:latest
    container_name: profident_pgadmin_dev
    environment:
      PGADMIN_DEFAULT_EMAIL: <EMAIL>
      PGADMIN_DEFAULT_PASSWORD: admin123
      PGADMIN_CONFIG_SERVER_MODE: 'False'
    ports:
      - "5050:80"
    volumes:
      - pgadmin_data:/var/lib/pgadmin
    networks:
      - profident_dev_network
    depends_on:
      postgres:
        condition: service_healthy
    restart: unless-stopped

volumes:
  postgres_dev_data:
    driver: local
  redis_dev_data:
    driver: local
  pgadmin_data:
    driver: local

networks:
  profident_dev_network:
    driver: bridge

"""
Secure error handling for authentication endpoints.
Provides standardized error responses that don't leak sensitive information.
"""

from typing import Dict, List, Any, Optional
from fastapi import HTTPException, status
import logging

logger = logging.getLogger(__name__)


class AuthErrorHandler:
    """Handles authentication errors securely."""
    
    # Standard error messages that don't leak information
    GENERIC_LOGIN_ERROR = "Invalid email or password"
    GENERIC_REGISTRATION_ERROR = "Registration failed. Please check your input and try again"
    GENERIC_VALIDATION_ERROR = "Invalid input provided"
    RATE_LIMIT_ERROR = "Too many attempts. Please try again later"
    SERVER_ERROR = "An internal error occurred. Please try again later"
    
    @classmethod
    def handle_validation_error(cls, field_errors: Dict[str, List[str]], 
                              endpoint: str = "authentication") -> HTTPException:
        """
        Handle validation errors securely.
        
        Args:
            field_errors: Dictionary of field validation errors
            endpoint: Endpoint name for logging
            
        Returns:
            HTTPException with secure error message
        """
        # Log detailed errors for debugging (server-side only)
        logger.warning(f"Validation error in {endpoint}: {field_errors}")
        
        # For registration, we can be more specific about validation errors
        # For login, we should be more generic for security
        if endpoint == "registration":
            # Provide specific validation feedback for registration
            formatted_errors = {}
            for field, errors in field_errors.items():
                # Sanitize error messages to prevent information leakage
                sanitized_errors = []
                for error in errors:
                    # Remove any potentially sensitive information
                    sanitized_error = cls._sanitize_error_message(error)
                    sanitized_errors.append(sanitized_error)
                formatted_errors[field] = sanitized_errors
            
            return HTTPException(
                status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
                detail={
                    "message": "Validation failed",
                    "errors": formatted_errors
                }
            )
        else:
            # For login and other sensitive endpoints, use generic message
            return HTTPException(
                status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
                detail={
                    "message": cls.GENERIC_VALIDATION_ERROR,
                    "errors": {"general": ["Please check your input and try again"]}
                }
            )
    
    @classmethod
    def handle_login_error(cls, error_type: str = "invalid_credentials") -> HTTPException:
        """
        Handle login errors securely.
        
        Args:
            error_type: Type of login error
            
        Returns:
            HTTPException with generic error message
        """
        # Log the actual error type for debugging
        logger.warning(f"Login error: {error_type}")
        
        # Always return the same generic message for security
        return HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail={
                "message": cls.GENERIC_LOGIN_ERROR,
                "error_code": "INVALID_CREDENTIALS"
            }
        )
    
    @classmethod
    def handle_registration_error(cls, error_type: str, details: str = None) -> HTTPException:
        """
        Handle registration errors.
        
        Args:
            error_type: Type of registration error
            details: Additional error details
            
        Returns:
            HTTPException with appropriate error message
        """
        # Log detailed error for debugging
        logger.warning(f"Registration error: {error_type} - {details}")
        
        if error_type == "email_exists":
            return HTTPException(
                status_code=status.HTTP_409_CONFLICT,
                detail={
                    "message": "An account with this email already exists",
                    "error_code": "EMAIL_EXISTS"
                }
            )
        elif error_type == "validation_failed":
            return HTTPException(
                status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
                detail={
                    "message": cls.GENERIC_REGISTRATION_ERROR,
                    "error_code": "VALIDATION_FAILED"
                }
            )
        else:
            return HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail={
                    "message": cls.SERVER_ERROR,
                    "error_code": "INTERNAL_ERROR"
                }
            )
    
    @classmethod
    def handle_rate_limit_error(cls, retry_after: int = None) -> HTTPException:
        """
        Handle rate limiting errors.
        
        Args:
            retry_after: Seconds until retry is allowed
            
        Returns:
            HTTPException with rate limit error
        """
        detail = {
            "message": cls.RATE_LIMIT_ERROR,
            "error_code": "RATE_LIMITED"
        }
        
        if retry_after:
            detail["retry_after"] = retry_after
        
        return HTTPException(
            status_code=status.HTTP_429_TOO_MANY_REQUESTS,
            detail=detail
        )
    
    @classmethod
    def handle_server_error(cls, error_details: str = None) -> HTTPException:
        """
        Handle internal server errors.
        
        Args:
            error_details: Internal error details (for logging only)
            
        Returns:
            HTTPException with generic server error
        """
        # Log detailed error for debugging
        if error_details:
            logger.error(f"Server error: {error_details}")
        
        return HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail={
                "message": cls.SERVER_ERROR,
                "error_code": "INTERNAL_ERROR"
            }
        )
    
    @classmethod
    def _sanitize_error_message(cls, error_message: str) -> str:
        """
        Sanitize error messages to prevent information leakage.
        
        Args:
            error_message: Original error message
            
        Returns:
            Sanitized error message
        """
        # Remove potentially sensitive information
        sensitive_patterns = [
            "database", "sql", "connection", "server", "internal",
            "exception", "traceback", "stack", "file", "path"
        ]
        
        message_lower = error_message.lower()
        for pattern in sensitive_patterns:
            if pattern in message_lower:
                return "Invalid input provided"
        
        # Limit message length
        if len(error_message) > 200:
            return "Invalid input provided"
        
        return error_message
    
    @classmethod
    def create_success_response(cls, message: str, data: Any = None) -> Dict[str, Any]:
        """
        Create standardized success response.
        
        Args:
            message: Success message
            data: Response data
            
        Returns:
            Standardized success response
        """
        response = {
            "success": True,
            "message": message
        }
        
        if data is not None:
            response["data"] = data
        
        return response


class InputSanitizer:
    """Sanitizes input data for security."""
    
    @classmethod
    def sanitize_auth_input(cls, data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Sanitize authentication input data.
        
        Args:
            data: Input data dictionary
            
        Returns:
            Sanitized data dictionary
        """
        sanitized = {}
        
        for key, value in data.items():
            if isinstance(value, str):
                # Basic string sanitization
                sanitized_value = cls._sanitize_string(value)
                sanitized[key] = sanitized_value
            else:
                sanitized[key] = value
        
        return sanitized
    
    @classmethod
    def _sanitize_string(cls, text: str) -> str:
        """
        Sanitize string input.
        
        Args:
            text: Input string
            
        Returns:
            Sanitized string
        """
        if not text:
            return ""
        
        # Remove null bytes and control characters
        sanitized = ''.join(char for char in text if ord(char) >= 32 or char in '\t\n\r')
        
        # Limit length
        if len(sanitized) > 1000:
            sanitized = sanitized[:1000]
        
        # Trim whitespace
        sanitized = sanitized.strip()
        
        return sanitized


class SecurityLogger:
    """Logs security-related events."""
    
    @classmethod
    def log_failed_login(cls, email: str, ip_address: str, user_agent: str = None):
        """Log failed login attempt."""
        logger.warning(
            f"Failed login attempt - Email: {email}, IP: {ip_address}, "
            f"User-Agent: {user_agent[:100] if user_agent else 'Unknown'}"
        )
    
    @classmethod
    def log_successful_login(cls, email: str, ip_address: str):
        """Log successful login."""
        logger.info(f"Successful login - Email: {email}, IP: {ip_address}")
    
    @classmethod
    def log_registration_attempt(cls, email: str, ip_address: str, success: bool):
        """Log registration attempt."""
        status_text = "successful" if success else "failed"
        logger.info(f"Registration {status_text} - Email: {email}, IP: {ip_address}")
    
    @classmethod
    def log_rate_limit_exceeded(cls, endpoint: str, ip_address: str):
        """Log rate limit exceeded."""
        logger.warning(f"Rate limit exceeded - Endpoint: {endpoint}, IP: {ip_address}")
    
    @classmethod
    def log_validation_error(cls, endpoint: str, errors: Dict[str, List[str]], ip_address: str):
        """Log validation errors."""
        logger.warning(
            f"Validation error - Endpoint: {endpoint}, IP: {ip_address}, "
            f"Errors: {len(errors)} fields"
        )
    
    @classmethod
    def log_security_event(cls, event_type: str, details: str, ip_address: str = None):
        """Log general security events."""
        logger.warning(f"Security event: {event_type} - {details} - IP: {ip_address}")


# Convenience functions for common error scenarios
def validation_error(field_errors: Dict[str, List[str]], endpoint: str = "authentication") -> HTTPException:
    """Create validation error response."""
    return AuthErrorHandler.handle_validation_error(field_errors, endpoint)


def login_error(error_type: str = "invalid_credentials") -> HTTPException:
    """Create login error response."""
    return AuthErrorHandler.handle_login_error(error_type)


def registration_error(error_type: str, details: str = None) -> HTTPException:
    """Create registration error response."""
    return AuthErrorHandler.handle_registration_error(error_type, details)


def rate_limit_error(retry_after: int = None) -> HTTPException:
    """Create rate limit error response."""
    return AuthErrorHandler.handle_rate_limit_error(retry_after)


def server_error(error_details: str = None) -> HTTPException:
    """Create server error response."""
    return AuthErrorHandler.handle_server_error(error_details)

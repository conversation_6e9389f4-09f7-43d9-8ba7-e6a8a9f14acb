/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    line-height: 1.6;
    color: #333;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.hidden {
    display: none !important;
}

/* Loading Screen */
.loading-screen {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
}

.loading-content {
    text-align: center;
    color: white;
}

.loading-spinner {
    width: 60px;
    height: 60px;
    border: 4px solid rgba(255, 255, 255, 0.3);
    border-top: 4px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 20px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.loading-progress {
    width: 300px;
    height: 4px;
    background: rgba(255, 255, 255, 0.3);
    border-radius: 2px;
    margin: 20px auto 0;
    overflow: hidden;
}

.progress-bar {
    height: 100%;
    background: white;
    width: 0%;
    transition: width 0.3s ease;
    border-radius: 2px;
}

/* Main App */
.app {
    background: #f8fafc;
    min-height: 100vh;
}

/* Header */
.header {
    background: white;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    position: sticky;
    top: 0;
    z-index: 100;
}

.header-content {
    padding: 20px 0;
    text-align: center;
}

.logo {
    font-size: 2.5rem;
    font-weight: 700;
    color: #667eea;
    margin-bottom: 5px;
}

.logo i {
    margin-right: 10px;
}

.tagline {
    color: #64748b;
    font-size: 1.1rem;
}

/* Search Section */
.search-section {
    padding: 40px 0;
    background: white;
}

.search-container {
    max-width: 600px;
    margin: 0 auto;
}

.search-input-wrapper {
    position: relative;
    margin-bottom: 20px;
}

.search-input {
    width: 100%;
    padding: 16px 50px 16px 50px;
    font-size: 1.1rem;
    border: 2px solid #e2e8f0;
    border-radius: 12px;
    outline: none;
    transition: all 0.3s ease;
    background: #f8fafc;
}

.search-input:focus {
    border-color: #667eea;
    background: white;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.search-icon {
    position: absolute;
    left: 16px;
    top: 50%;
    transform: translateY(-50%);
    color: #94a3b8;
    font-size: 1.2rem;
}

.clear-search {
    position: absolute;
    right: 16px;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: #94a3b8;
    cursor: pointer;
    padding: 5px;
    border-radius: 50%;
    transition: all 0.2s ease;
}

.clear-search:hover {
    background: #f1f5f9;
    color: #64748b;
}

.search-stats {
    text-align: center;
    color: #64748b;
    font-size: 0.9rem;
}

/* Search Suggestions */
.search-suggestions {
    margin-bottom: 40px;
}

.suggestions-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 2px solid #e2e8f0;
}

.suggestions-header h3 {
    color: #1e293b;
    font-size: 1.5rem;
}

#results-count {
    color: #64748b;
    font-size: 0.9rem;
}

.suggestions-list {
    display: grid;
    gap: 15px;
}

.suggestion-item {
    background: white;
    border: 1px solid #e2e8f0;
    border-radius: 12px;
    padding: 20px;
    cursor: pointer;
    transition: all 0.2s ease;
    position: relative;
}

.suggestion-item:hover {
    border-color: #667eea;
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.15);
    transform: translateY(-2px);
}

.suggestion-seller {
    display: inline-block;
    background: #667eea;
    color: white;
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 500;
    margin-bottom: 10px;
    text-transform: uppercase;
}

.suggestion-name {
    font-size: 1.1rem;
    font-weight: 600;
    color: #1e293b;
    margin-bottom: 8px;
    line-height: 1.4;
}

.suggestion-details {
    display: flex;
    gap: 20px;
    color: #64748b;
    font-size: 0.9rem;
}

.suggestion-mfr {
    font-weight: 500;
}

/* Product Detail */
.product-detail {
    margin-bottom: 40px;
}

.detail-header {
    display: flex;
    align-items: center;
    gap: 20px;
    margin-bottom: 30px;
    padding-bottom: 15px;
    border-bottom: 2px solid #e2e8f0;
}

.back-button {
    background: #f1f5f9;
    border: 1px solid #e2e8f0;
    padding: 10px 16px;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.2s ease;
    color: #64748b;
    font-size: 0.9rem;
}

.back-button:hover {
    background: #e2e8f0;
    color: #475569;
}

.detail-header h2 {
    color: #1e293b;
    font-size: 1.8rem;
}

.selected-product {
    background: white;
    border: 2px solid #667eea;
    border-radius: 12px;
    padding: 25px;
    margin-bottom: 30px;
}

.product-card {
    background: white;
    border: 1px solid #e2e8f0;
    border-radius: 12px;
    padding: 20px;
    transition: all 0.2s ease;
}

.product-card:hover {
    border-color: #667eea;
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.15);
}

.product-seller {
    display: inline-block;
    background: #10b981;
    color: white;
    padding: 6px 14px;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
    margin-bottom: 15px;
    text-transform: uppercase;
}

.product-name {
    font-size: 1.3rem;
    font-weight: 600;
    color: #1e293b;
    margin-bottom: 15px;
    line-height: 1.4;
}

.product-info {
    display: grid;
    gap: 10px;
    margin-bottom: 20px;
}

.info-row {
    display: flex;
    gap: 10px;
}

.info-label {
    font-weight: 600;
    color: #475569;
    min-width: 120px;
}

.info-value {
    color: #64748b;
    flex: 1;
}

.product-link {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    background: #667eea;
    color: white;
    padding: 10px 16px;
    border-radius: 8px;
    text-decoration: none;
    font-weight: 500;
    transition: all 0.2s ease;
}

.product-link:hover {
    background: #5a67d8;
    transform: translateY(-1px);
}

/* Comparison Section */
.comparison-section {
    margin-top: 30px;
}

.comparison-title {
    display: flex;
    align-items: center;
    gap: 10px;
    color: #1e293b;
    font-size: 1.4rem;
    margin-bottom: 20px;
}

.comparison-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 20px;
}

/* No Results */
.no-results {
    text-align: center;
    padding: 60px 20px;
    color: #64748b;
}

.no-results-content i {
    font-size: 4rem;
    margin-bottom: 20px;
    opacity: 0.5;
}

.no-results-content h3 {
    font-size: 1.5rem;
    margin-bottom: 10px;
    color: #475569;
}

/* Stats Section */
.stats-section {
    background: white;
    padding: 40px 0;
    border-top: 1px solid #e2e8f0;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
}

.stat-card {
    text-align: center;
    padding: 20px;
    background: #f8fafc;
    border-radius: 12px;
    border: 1px solid #e2e8f0;
}

.stat-number {
    font-size: 2.5rem;
    font-weight: 700;
    color: #667eea;
    margin-bottom: 5px;
}

.stat-label {
    color: #64748b;
    font-weight: 500;
}

/* Footer */
.footer {
    background: #1e293b;
    color: #94a3b8;
    text-align: center;
    padding: 20px 0;
}

/* Responsive Design */
@media (max-width: 768px) {
    .container {
        padding: 0 15px;
    }
    
    .logo {
        font-size: 2rem;
    }
    
    .tagline {
        font-size: 1rem;
    }
    
    .search-input {
        font-size: 1rem;
        padding: 14px 45px 14px 45px;
    }
    
    .comparison-grid {
        grid-template-columns: 1fr;
    }
    
    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .detail-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 15px;
    }
    
    .suggestions-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }
}

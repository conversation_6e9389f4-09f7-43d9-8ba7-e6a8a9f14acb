import React from 'react';
import { Loader2, Search, Database } from 'lucide-react';

interface LoadingOverlayProps {
  isVisible: boolean;
  message?: string;
  progress?: number;
}

const LoadingOverlay: React.FC<LoadingOverlayProps> = ({
  isVisible,
  message = "Loading search data...",
  progress = 0
}) => {
  if (!isVisible) return null;

  return (
    <div className="fixed inset-0 bg-white bg-opacity-95 backdrop-blur-sm z-[9999] flex items-center justify-center">
      <div className="text-center max-w-md mx-auto px-6">
        {/* Logo/Brand */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-primary-600 mb-2">ProfiDent</h1>
          <p className="text-gray-600">Dental Product Search</p>
        </div>

        {/* Loading Animation */}
        <div className="mb-6">
          <div className="relative">
            {/* Main spinner */}
            <Loader2 className="h-12 w-12 text-primary-600 animate-spin mx-auto mb-4" />
            
            {/* Data icons */}
            <div className="flex justify-center space-x-4 mb-4">
              <div className="flex flex-col items-center">
                <Database className="h-6 w-6 text-primary-400 mb-1" />
                <span className="text-xs text-gray-500">Categories</span>
              </div>
              <div className="flex flex-col items-center">
                <Search className="h-6 w-6 text-primary-400 mb-1" />
                <span className="text-xs text-gray-500">Manufacturers</span>
              </div>
              <div className="flex flex-col items-center">
                <Database className="h-6 w-6 text-primary-400 mb-1" />
                <span className="text-xs text-gray-500">Sellers</span>
              </div>
            </div>
          </div>
        </div>

        {/* Loading Message */}
        <div className="mb-6">
          <p className="text-lg font-medium text-gray-900 mb-2">{message}</p>
          <p className="text-sm text-gray-600">
            Preparing instant search for 339K+ dental products
          </p>
        </div>

        {/* Progress Bar */}
        {progress > 0 && (
          <div className="mb-4">
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div 
                className="bg-primary-600 h-2 rounded-full transition-all duration-300 ease-out"
                style={{ width: `${Math.min(progress, 100)}%` }}
              />
            </div>
            <p className="text-xs text-gray-500 mt-2">{Math.round(progress)}% complete</p>
          </div>
        )}

        {/* Loading Steps */}
        <div className="text-left bg-gray-50 rounded-lg p-4">
          <h4 className="text-sm font-medium text-gray-900 mb-2">Loading:</h4>
          <div className="space-y-1 text-xs text-gray-600">
            <div className="flex items-center">
              <div className="w-2 h-2 bg-primary-600 rounded-full mr-2 animate-pulse"></div>
              Product categories and filters
            </div>
            <div className="flex items-center">
              <div className="w-2 h-2 bg-primary-600 rounded-full mr-2 animate-pulse"></div>
              Manufacturer data and search index
            </div>
            <div className="flex items-center">
              <div className="w-2 h-2 bg-primary-600 rounded-full mr-2 animate-pulse"></div>
              Seller information and pricing
            </div>
          </div>
        </div>

        {/* Tip */}
        <div className="mt-6 p-3 bg-blue-50 border border-blue-200 rounded-lg">
          <p className="text-xs text-blue-800">
            💡 <strong>Tip:</strong> Once loaded, search results will be instant!
          </p>
        </div>
      </div>
    </div>
  );
};

export default LoadingOverlay;

class ProfiDentApp {
    constructor() {
        this.sellers = [
            'benco', 'darby', 'dds', 'frontier', 'henry',
            'midwest', 'net32', 'optimus', 'safco', 'tdsc'
        ];
        this.productData = {};
        this.searchIndex = new Map();
        this.mfrIndex = new Map();
        this.isLoading = true;
        this.searchTimeout = null;
        this.currentQuery = '';
        
        this.initializeApp();
    }

    async initializeApp() {
        try {
            await this.loadAllData();
            this.buildSearchIndex();
            this.setupEventListeners();
            this.updateStats();
            this.hideLoadingScreen();
        } catch (error) {
            console.error('Failed to initialize app:', error);
            this.showError('Failed to load product data. Please refresh the page.');
        }
    }

    async loadAllData() {
        const loadingStatus = document.getElementById('loading-status');
        const progressBar = document.getElementById('progress-bar');
        
        let loadedCount = 0;
        const totalSellers = this.sellers.length;

        for (const seller of this.sellers) {
            try {
                loadingStatus.textContent = `Loading ${seller} products...`;
                
                const response = await fetch(`./dbapi/${seller}_up.json`);
                if (!response.ok) {
                    throw new Error(`Failed to load ${seller} data`);
                }
                
                const data = await response.json();
                this.productData[seller] = data;
                
                loadedCount++;
                const progress = (loadedCount / totalSellers) * 100;
                progressBar.style.width = `${progress}%`;
                
                console.log(`Loaded ${data.length} products from ${seller}`);
            } catch (error) {
                console.error(`Error loading ${seller}:`, error);
                loadingStatus.textContent = `Error loading ${seller} - continuing...`;
            }
        }
        
        loadingStatus.textContent = 'Building search index...';
    }

    buildSearchIndex() {
        console.log('Building search index...');
        
        for (const [seller, products] of Object.entries(this.productData)) {
            products.forEach((product, index) => {
                const mfr = product.mfr?.trim();
                const name = product.name?.toLowerCase() || '';
                
                // Build MFR index for comparison functionality
                if (mfr) {
                    if (!this.mfrIndex.has(mfr)) {
                        this.mfrIndex.set(mfr, []);
                    }
                    this.mfrIndex.get(mfr).push({
                        ...product,
                        seller,
                        originalIndex: index
                    });
                }
                
                // Build search index with name words
                const words = name.split(/\s+/).filter(word => word.length > 2);
                words.forEach(word => {
                    if (!this.searchIndex.has(word)) {
                        this.searchIndex.set(word, []);
                    }
                    this.searchIndex.get(word).push({
                        ...product,
                        seller,
                        originalIndex: index
                    });
                });
            });
        }
        
        console.log(`Search index built with ${this.searchIndex.size} terms`);
        console.log(`MFR index built with ${this.mfrIndex.size} unique MFRs`);
    }

    setupEventListeners() {
        const searchInput = document.getElementById('search-input');
        const clearSearch = document.getElementById('clear-search');
        const backButton = document.getElementById('back-to-search');

        searchInput.addEventListener('input', (e) => {
            this.handleSearchInput(e.target.value);
        });

        clearSearch.addEventListener('click', () => {
            this.clearSearch();
        });

        backButton.addEventListener('click', () => {
            this.showSearchResults();
        });

        // Handle escape key
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                if (!document.getElementById('product-detail').classList.contains('hidden')) {
                    this.showSearchResults();
                } else {
                    this.clearSearch();
                }
            }
        });
    }

    handleSearchInput(query) {
        clearTimeout(this.searchTimeout);
        this.currentQuery = query.trim();
        
        const clearButton = document.getElementById('clear-search');
        if (this.currentQuery) {
            clearButton.classList.remove('hidden');
        } else {
            clearButton.classList.add('hidden');
        }

        if (this.currentQuery.length < 2) {
            this.hideAllSections();
            return;
        }

        // Debounce search
        this.searchTimeout = setTimeout(() => {
            this.performSearch(this.currentQuery);
        }, 300);
    }

    performSearch(query) {
        const startTime = performance.now();
        const results = this.searchProducts(query);
        const endTime = performance.now();
        
        console.log(`Search completed in ${(endTime - startTime).toFixed(2)}ms`);
        console.log(`Found ${results.length} results for "${query}"`);
        
        this.displaySearchResults(results, query);
    }

    searchProducts(query) {
        const queryWords = query.toLowerCase().split(/\s+/).filter(word => word.length > 0);
        const resultMap = new Map();
        const maxResultsPerSeller = 10;

        // Search through index
        queryWords.forEach(queryWord => {
            for (const [indexWord, products] of this.searchIndex.entries()) {
                if (indexWord.includes(queryWord) || queryWord.includes(indexWord)) {
                    products.forEach(product => {
                        const key = `${product.seller}-${product.mfr}`;
                        if (!resultMap.has(key)) {
                            // Calculate relevance score
                            const nameMatch = product.name.toLowerCase().includes(query.toLowerCase());
                            const exactMatch = product.name.toLowerCase() === query.toLowerCase();
                            const startsWithMatch = product.name.toLowerCase().startsWith(query.toLowerCase());
                            
                            let score = 1;
                            if (exactMatch) score += 10;
                            else if (startsWithMatch) score += 5;
                            else if (nameMatch) score += 3;
                            
                            resultMap.set(key, { ...product, relevanceScore: score });
                        }
                    });
                }
            }
        });

        // Convert to array and sort by relevance
        let results = Array.from(resultMap.values());
        results.sort((a, b) => b.relevanceScore - a.relevanceScore);

        // Limit results per seller to prevent overwhelming UI
        const sellerCounts = {};
        results = results.filter(product => {
            if (!sellerCounts[product.seller]) {
                sellerCounts[product.seller] = 0;
            }
            if (sellerCounts[product.seller] < maxResultsPerSeller) {
                sellerCounts[product.seller]++;
                return true;
            }
            return false;
        });

        return results.slice(0, 100); // Limit total results
    }

    displaySearchResults(results, query) {
        const suggestionsContainer = document.getElementById('search-suggestions');
        const suggestionsList = document.getElementById('suggestions-list');
        const resultsCount = document.getElementById('results-count');
        const noResults = document.getElementById('no-results');
        const searchStats = document.getElementById('search-stats');
        const statsText = document.getElementById('stats-text');

        this.hideAllSections();

        if (results.length === 0) {
            noResults.classList.remove('hidden');
            return;
        }

        // Show search stats
        const sellerCount = new Set(results.map(r => r.seller)).size;
        statsText.textContent = `Found ${results.length} products from ${sellerCount} sellers`;
        searchStats.classList.remove('hidden');

        // Display results
        resultsCount.textContent = `${results.length} results`;
        suggestionsList.innerHTML = '';

        results.forEach(product => {
            const item = this.createSuggestionItem(product);
            suggestionsList.appendChild(item);
        });

        suggestionsContainer.classList.remove('hidden');
    }

    createSuggestionItem(product) {
        const item = document.createElement('div');
        item.className = 'suggestion-item';
        item.innerHTML = `
            <div class="suggestion-seller">${product.seller}</div>
            <div class="suggestion-name">${this.highlightQuery(product.name, this.currentQuery)}</div>
            <div class="suggestion-details">
                <span class="suggestion-mfr">MFR: ${product.mfr}</span>
                <span class="suggestion-manufacturer">${product.manufacturer || 'N/A'}</span>
            </div>
        `;

        item.addEventListener('click', () => {
            this.showProductDetail(product);
        });

        return item;
    }

    highlightQuery(text, query) {
        if (!query) return text;
        const regex = new RegExp(`(${query.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')})`, 'gi');
        return text.replace(regex, '<mark>$1</mark>');
    }

    showProductDetail(selectedProduct) {
        const productDetail = document.getElementById('product-detail');
        const selectedProductContainer = document.getElementById('selected-product');
        const comparisonGrid = document.getElementById('comparison-grid');

        this.hideAllSections();

        // Show selected product
        selectedProductContainer.innerHTML = this.createProductCard(selectedProduct, true);

        // Find comparison products with same MFR
        const comparisonProducts = this.mfrIndex.get(selectedProduct.mfr) || [];
        const otherSellers = comparisonProducts.filter(p => p.seller !== selectedProduct.seller);

        if (otherSellers.length > 0) {
            comparisonGrid.innerHTML = '';
            otherSellers.forEach(product => {
                const card = this.createProductCard(product, false);
                comparisonGrid.appendChild(card);
            });
            document.getElementById('comparison-section').style.display = 'block';
        } else {
            document.getElementById('comparison-section').style.display = 'none';
        }

        productDetail.classList.remove('hidden');
    }

    createProductCard(product, isSelected = false) {
        const cardElement = document.createElement('div');
        cardElement.className = 'product-card';
        
        const sellerClass = isSelected ? 'product-seller' : 'product-seller';
        const sellerStyle = isSelected ? 'background: #667eea;' : 'background: #10b981;';
        
        cardElement.innerHTML = `
            <div class="${sellerClass}" style="${sellerStyle}">${product.seller}</div>
            <div class="product-name">${product.name}</div>
            <div class="product-info">
                <div class="info-row">
                    <span class="info-label">MFR:</span>
                    <span class="info-value">${product.mfr}</span>
                </div>
                <div class="info-row">
                    <span class="info-label">Manufacturer:</span>
                    <span class="info-value">${product.manufacturer || 'N/A'}</span>
                </div>
                <div class="info-row">
                    <span class="info-label">Category:</span>
                    <span class="info-value">${product.maincat || 'N/A'}</span>
                </div>
            </div>
            ${product.url ? `<a href="${product.url}" target="_blank" class="product-link">
                <i class="fas fa-external-link-alt"></i>
                View on ${product.seller}
            </a>` : ''}
        `;

        return isSelected ? cardElement.innerHTML : cardElement;
    }

    showSearchResults() {
        this.hideAllSections();
        if (this.currentQuery && this.currentQuery.length >= 2) {
            this.performSearch(this.currentQuery);
        }
    }

    clearSearch() {
        document.getElementById('search-input').value = '';
        document.getElementById('clear-search').classList.add('hidden');
        this.currentQuery = '';
        this.hideAllSections();
    }

    hideAllSections() {
        document.getElementById('search-suggestions').classList.add('hidden');
        document.getElementById('product-detail').classList.add('hidden');
        document.getElementById('no-results').classList.add('hidden');
        document.getElementById('search-stats').classList.add('hidden');
    }

    updateStats() {
        let totalProducts = 0;
        const uniqueMfrs = new Set();
        const mfrCounts = new Map();

        for (const [seller, products] of Object.entries(this.productData)) {
            totalProducts += products.length;
            products.forEach(product => {
                if (product.mfr) {
                    uniqueMfrs.add(product.mfr);
                    mfrCounts.set(product.mfr, (mfrCounts.get(product.mfr) || 0) + 1);
                }
            });
        }

        const multiSellerProducts = Array.from(mfrCounts.values()).filter(count => count > 1).length;
        const overlapPercentage = ((multiSellerProducts / uniqueMfrs.size) * 100).toFixed(1);

        document.getElementById('total-products').textContent = totalProducts.toLocaleString();
        document.getElementById('unique-products').textContent = uniqueMfrs.size.toLocaleString();
        document.getElementById('overlap-percentage').textContent = `${overlapPercentage}%`;
    }

    hideLoadingScreen() {
        document.getElementById('loading-screen').classList.add('hidden');
        document.getElementById('app').classList.remove('hidden');
        document.getElementById('search-input').focus();
    }

    showError(message) {
        document.getElementById('loading-status').textContent = message;
        document.querySelector('.loading-spinner').style.display = 'none';
    }
}

// Initialize the app when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new ProfiDentApp();
});

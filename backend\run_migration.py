#!/usr/bin/env python3
"""Run database migration for is_default column."""

import asyncio
import asyncpg

async def run_migration():
    try:
        conn = await asyncpg.connect('postgresql://profident_user:profident_dev_password@localhost:5433/profident_dev')
        
        print("🔄 Running migration: Add is_default column to shopping_lists")
        
        # Check if column already exists
        exists = await conn.fetchval("""
            SELECT EXISTS (
                SELECT 1 FROM information_schema.columns 
                WHERE table_name = 'shopping_lists' AND column_name = 'is_default'
            )
        """)
        
        if exists:
            print("✅ Column is_default already exists")
        else:
            # Add is_default column
            await conn.execute("ALTER TABLE shopping_lists ADD COLUMN is_default BOOLEAN DEFAULT FALSE")
            print("✅ Added is_default column")
            
            # Add index for efficient default list queries
            await conn.execute("""
                CREATE INDEX IF NOT EXISTS idx_shopping_lists_user_default 
                ON shopping_lists (user_id, is_default) 
                WHERE is_default = TRUE
            """)
            print("✅ Added index for default lists")
            
            # Add unique constraint to ensure only one default list per user
            await conn.execute("""
                CREATE UNIQUE INDEX IF NOT EXISTS idx_shopping_lists_one_default_per_user 
                ON shopping_lists (user_id) 
                WHERE is_default = TRUE
            """)
            print("✅ Added unique constraint for one default per user")
            
            # Update existing first shopping list for each user to be default
            updated = await conn.execute("""
                UPDATE shopping_lists 
                SET is_default = TRUE 
                WHERE id IN (
                    SELECT DISTINCT ON (user_id) id 
                    FROM shopping_lists 
                    ORDER BY user_id, created_at ASC
                )
            """)
            print(f"✅ Updated existing lists to set defaults: {updated}")
        
        print("🎉 Migration completed successfully!")
        
        await conn.close()
        
    except Exception as e:
        print(f"❌ Migration failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(run_migration())

import { Routes, Route } from "react-router-dom";
import Layout from "./components/Layout";
import HomePage from "./pages/HomePage";
import SearchPage from "./pages/SearchPage";
import ProductPage from "./pages/ProductPage";
import ShoppingListsPage from "./pages/ShoppingListsPage";
import ShoppingListDetailPage from "./pages/ShoppingListDetailPage";
import LoginPage from "./pages/LoginPage";
import RegisterPage from "./pages/RegisterPage";
import ProfilePage from "./pages/ProfilePage";
import AdminDashboardPage from "./pages/admin/AdminDashboardPage";
import UserManagementPage from "./pages/admin/UserManagementPage";
import RoleManagementPage from "./pages/admin/RoleManagementPage";
import { useAuthStore } from "./stores/authStore";
import { useEffect } from "react";
import {
  AdminRoute,
  UserManagementRoute,
  RoleManagementRoute,
  ProtectedRoute,
} from "./components/auth/ProtectedRoute";

function App() {
  const initializeAuth = useAuthStore((state) => state.initializeAuth);

  useEffect(() => {
    initializeAuth();
  }, []); // Empty dependency array - only run once on mount

  return (
    <div className="min-h-screen bg-gray-50">
      <Routes>
        <Route path="/login" element={<LoginPage />} />
        <Route path="/register" element={<RegisterPage />} />
        <Route path="/" element={<Layout />}>
          <Route index element={<HomePage />} />
          <Route
            path="search"
            element={
              <ProtectedRoute
                permissions={["products.search"]}
                requireAuth={true}
                allowSuperAdmin={true}
              >
                <SearchPage />
              </ProtectedRoute>
            }
          />
          <Route
            path="product/:id"
            element={
              <ProtectedRoute
                permissions={["products.read"]}
                requireAuth={true}
                allowSuperAdmin={true}
              >
                <ProductPage />
              </ProtectedRoute>
            }
          />
          <Route
            path="shopping-lists"
            element={
              <ProtectedRoute
                permissions={["shopping_lists.read"]}
                requireAuth={true}
                allowSuperAdmin={true}
              >
                <ShoppingListsPage />
              </ProtectedRoute>
            }
          />
          <Route
            path="shopping-lists/:id"
            element={
              <ProtectedRoute
                permissions={["shopping_lists.read"]}
                requireAuth={true}
                allowSuperAdmin={true}
              >
                <ShoppingListDetailPage />
              </ProtectedRoute>
            }
          />
          <Route
            path="profile"
            element={
              <ProtectedRoute requireAuth={true}>
                <ProfilePage />
              </ProtectedRoute>
            }
          />

          {/* Admin Routes - Protected by role-based route guards */}
          <Route
            path="admin"
            element={
              <AdminRoute>
                <AdminDashboardPage />
              </AdminRoute>
            }
          />
          <Route
            path="admin/users"
            element={
              <UserManagementRoute>
                <UserManagementPage />
              </UserManagementRoute>
            }
          />
          <Route
            path="admin/roles"
            element={
              <RoleManagementRoute>
                <RoleManagementPage />
              </RoleManagementRoute>
            }
          />
        </Route>
      </Routes>
    </div>
  );
}

export default App;

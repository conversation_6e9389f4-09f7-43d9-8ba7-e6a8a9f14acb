"""
Shopping list schemas for request/response validation
"""

from datetime import datetime
from typing import Optional, List, Dict, Any
from pydantic import BaseModel, Field, validator


class ShoppingListBase(BaseModel):
    """Base shopping list schema."""
    name: str = Field(..., min_length=1, max_length=200, description="Shopping list name")
    description: Optional[str] = Field(None, max_length=1000, description="Shopping list description")
    is_default: bool = Field(False, description="Whether this is the user's default shopping list")


class ShoppingListCreate(ShoppingListBase):
    """Schema for shopping list creation."""
    pass


class ShoppingListUpdate(BaseModel):
    """Schema for shopping list updates."""
    name: Optional[str] = Field(None, min_length=1, max_length=200)
    description: Optional[str] = Field(None, max_length=1000)
    is_default: Optional[bool] = Field(None, description="Whether this should be the user's default shopping list")


class ShoppingListResponse(ShoppingListBase):
    """Schema for shopping list response."""
    id: int
    user_id: str
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None

    class Config:
        from_attributes = True


class ShoppingListItemBase(BaseModel):
    """Base shopping list item schema."""
    product_id: int = Field(..., description="Product ID")
    quantity: int = Field(1, ge=1, le=1000, description="Quantity")
    notes: Optional[str] = Field(None, max_length=500, description="Item notes")


class ShoppingListItemCreate(ShoppingListItemBase):
    """Schema for shopping list item creation."""
    pass


class ShoppingListItemUpdate(BaseModel):
    """Schema for shopping list item updates."""
    quantity: Optional[int] = Field(None, ge=1, le=1000)
    notes: Optional[str] = Field(None, max_length=500)


class ProductSummary(BaseModel):
    """Schema for product summary in shopping list items."""
    id: int
    mfr: str
    name: str
    brand: Optional[str] = None
    seller: str
    price: Optional[str] = None
    url: str


class ShoppingListItemResponse(ShoppingListItemBase):
    """Schema for shopping list item response."""
    id: int
    shopping_list_id: int
    added_at: Optional[datetime] = None
    product: Optional[ProductSummary] = None

    class Config:
        from_attributes = True


class ShoppingListSummary(BaseModel):
    """Schema for shopping list summary statistics."""
    total_items: int
    purchased_items: int
    remaining_items: int
    total_quantity: int
    purchased_quantity: int
    completion_percentage: float


class ShoppingListDetailResponse(ShoppingListResponse):
    """Schema for detailed shopping list response with items."""
    items: List[ShoppingListItemResponse] = []
    summary: Optional[ShoppingListSummary] = None


class ShoppingListsResponse(BaseModel):
    """Schema for shopping lists response."""
    success: bool = True
    message: str = "Shopping lists retrieved successfully"
    shopping_lists: List[ShoppingListResponse]
    pagination: Dict[str, Any]


class ShoppingListItemsResponse(BaseModel):
    """Schema for shopping list items response."""
    success: bool = True
    message: str = "Shopping list items retrieved successfully"
    shopping_list_id: int
    items: List[ShoppingListItemResponse]
    summary: ShoppingListSummary


class ShoppingListBulkAddRequest(BaseModel):
    """Schema for bulk adding items to shopping list."""
    items: List[ShoppingListItemCreate] = Field(..., min_items=1, max_items=100)


class ShoppingListBulkAddResponse(BaseModel):
    """Schema for bulk add response."""
    success: bool = True
    message: str = "Items added successfully"
    added_items: int
    failed_items: int
    errors: Optional[List[str]] = None


class ShoppingListBulkUpdateRequest(BaseModel):
    """Schema for bulk updating items."""
    item_updates: List[Dict[str, Any]] = Field(..., min_items=1, max_items=100)


class ShoppingListBulkUpdateResponse(BaseModel):
    """Schema for bulk update response."""
    success: bool = True
    message: str = "Items updated successfully"
    updated_items: int
    failed_items: int
    errors: Optional[List[str]] = None


class ShoppingListShareRequest(BaseModel):
    """Schema for sharing shopping list."""
    email: str = Field(..., description="Email to share with")
    permission: str = Field("view", pattern="^(view|edit)$", description="Permission level")


class ShoppingListShareResponse(BaseModel):
    """Schema for share response."""
    success: bool = True
    message: str = "Shopping list shared successfully"
    shared_with: str
    permission: str


class ShoppingListPriceAnalysis(BaseModel):
    """Schema for shopping list price analysis."""
    total_estimated_cost: float
    items_with_prices: int
    items_without_prices: int
    price_breakdown_by_seller: Dict[str, Dict[str, Any]]
    potential_savings: Optional[Dict[str, Any]] = None


class ShoppingListAnalysisResponse(BaseModel):
    """Schema for shopping list analysis response."""
    success: bool = True
    message: str = "Shopping list analysis completed"
    shopping_list_id: int
    analysis: ShoppingListPriceAnalysis


class APIResponse(BaseModel):
    """Generic API response schema."""
    success: bool = True
    message: str
    data: Optional[Dict[str, Any]] = None


class ErrorResponse(BaseModel):
    """Error response schema."""
    success: bool = False
    message: str
    errors: Optional[List[str]] = None
    error_code: Optional[str] = None

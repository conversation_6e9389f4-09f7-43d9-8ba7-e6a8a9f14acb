#!/usr/bin/env python3
"""
Debug Search Results UI
Focus specifically on why search results don't appear after search
"""

import asyncio
from playwright.async_api import async_playwright


async def debug_search_results():
    """Debug search results display."""
    print("🔍 Debugging Search Results UI")
    
    async with async_playwright() as p:
        browser = await p.chromium.launch(headless=False, slow_mo=500)
        page = await browser.new_page()
        
        # Track console messages and network
        console_messages = []
        network_requests = []
        page.on("console", lambda msg: console_messages.append(f"{msg.type}: {msg.text}"))
        page.on("response", lambda response: print(f"🌐 {response.status} {response.url}"))
        
        try:
            # Navigate to frontend
            print("📍 Loading frontend...")
            await page.goto("http://localhost:3000", wait_until="networkidle")
            await page.wait_for_timeout(3000)
            
            # Find search input
            search_input = page.locator('input[placeholder*="search" i]').first
            if not await search_input.is_visible():
                print("❌ Search input not found")
                return False
            
            print("✅ Search input found")
            
            # Perform search
            print("🔍 Performing search for 'composite'...")
            await search_input.click()
            await search_input.fill("composite")
            await page.wait_for_timeout(1000)
            
            # Submit search (press Enter)
            await search_input.press("Enter")
            print("⌨️ Pressed Enter to submit search")
            
            # Wait for navigation to search page
            await page.wait_for_load_state("networkidle")
            await page.wait_for_timeout(5000)  # Wait longer for results
            
            # Check current URL
            current_url = page.url
            print(f"📍 Current URL: {current_url}")
            
            # Take screenshot after search
            await page.screenshot(path="debug_screenshots/search_results_page.png")
            
            # Check for search results
            print("🔍 Looking for search results...")
            
            # Try multiple selectors for search results
            result_selectors = [
                '.search-results',
                '.product-list',
                '.results',
                '.product-item',
                '.product-card',
                '[data-testid="search-results"]',
                '[data-testid="product-list"]',
                'div:has-text("results")',
                'div:has-text("products")',
                'div:has-text("composite")',  # Should contain search term
            ]
            
            results_found = False
            for selector in result_selectors:
                try:
                    elements = page.locator(selector)
                    count = await elements.count()
                    if count > 0:
                        print(f"✅ Found {count} elements with selector: {selector}")
                        
                        # Get text content of first few elements
                        for i in range(min(3, count)):
                            try:
                                text = await elements.nth(i).text_content()
                                if text and len(text.strip()) > 0:
                                    print(f"  📝 Element {i+1}: {text[:100]}...")
                                    results_found = True
                            except:
                                pass
                        
                        if results_found:
                            break
                except:
                    continue
            
            if not results_found:
                print("❌ No search results found in UI")
                
                # Debug: Check page content
                print("🔍 Debugging page content...")
                
                # Get page title
                title = await page.title()
                print(f"📄 Page title: {title}")
                
                # Check for loading indicators
                loading_elements = await page.locator('[class*="loading"], [class*="spinner"], .loading').count()
                print(f"⏳ Loading elements: {loading_elements}")
                
                # Check for error messages
                error_elements = await page.locator('.error, [class*="error"], div:has-text("error")').count()
                print(f"❌ Error elements: {error_elements}")
                
                # Check if we're on the search page
                search_page_indicators = await page.locator('h1, h2, h3').all()
                print("📋 Page headings:")
                for heading in search_page_indicators[:5]:
                    try:
                        text = await heading.text_content()
                        if text:
                            print(f"  📝 {text}")
                    except:
                        pass
                
                # Check for any text containing "composite"
                composite_text = await page.locator('text=composite').count()
                print(f"🔍 Elements containing 'composite': {composite_text}")
                
                # Get all visible text on page
                body_text = await page.locator('body').text_content()
                if body_text:
                    print(f"📝 Page contains {len(body_text)} characters")
                    if "composite" in body_text.lower():
                        print("✅ Page contains search term 'composite'")
                    else:
                        print("❌ Page does not contain search term 'composite'")
            
            # Check React state for search results
            print("🔍 Checking React state...")
            
            search_state = await page.evaluate("""
                () => {
                    // Try to access search results from various sources
                    const results = {
                        url: window.location.href,
                        searchParams: window.location.search,
                        reactElements: document.querySelectorAll('[data-react-component]').length,
                        totalElements: document.querySelectorAll('*').length
                    };
                    return results;
                }
            """)
            print(f"⚛️ Page state: {search_state}")
            
            # Final screenshot
            await page.screenshot(path="debug_screenshots/final_search_results.png")
            
            return results_found
            
        except Exception as e:
            print(f"❌ Debug failed: {e}")
            await page.screenshot(path="debug_screenshots/error_search_results.png")
            import traceback
            traceback.print_exc()
            return False
        finally:
            await browser.close()


if __name__ == "__main__":
    success = asyncio.run(debug_search_results())
    if success:
        print("\n✅ Search results working!")
    else:
        print("\n❌ Search results not working - check screenshots")

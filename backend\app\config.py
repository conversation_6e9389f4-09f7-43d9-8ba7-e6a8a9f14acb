"""
ProfiDent Backend Configuration
Manages environment variables and application settings
"""

import os
from typing import Optional
from pydantic_settings import BaseSettings
from pydantic import validator


class Settings(BaseSettings):
    """Application settings with environment variable support."""
    
    # Database Configuration
    DATABASE_URL: str = "postgresql+asyncpg://profident_user:profident_dev_password@localhost:5433/profident_dev"
    POSTGRES_USER: str = "profident_user"
    POSTGRES_PASSWORD: str = "profident_dev_password"
    POSTGRES_DB: str = "profident_dev"
    POSTGRES_HOST: str = "localhost"
    POSTGRES_PORT: int = 5433
    
    # Database Connection Pool Settings
    DB_POOL_MIN_SIZE: int = 5
    DB_POOL_MAX_SIZE: int = 20
    DB_POOL_MAX_QUERIES: int = 50000
    DB_POOL_MAX_INACTIVE_CONNECTION_LIFETIME: float = 300.0  # 5 minutes
    DB_POOL_TIMEOUT: float = 60.0  # 1 minute
    DB_POOL_COMMAND_TIMEOUT: float = 30.0  # 30 seconds
    
    # Redis Configuration
    REDIS_URL: str = "redis://localhost:6380"
    REDIS_HOST: str = "localhost"
    REDIS_PORT: int = 6380
    REDIS_DB: int = 0
    REDIS_PASSWORD: Optional[str] = None
    
    # Authentication
    SECRET_KEY: str = "your-secret-key-change-in-production"
    ALGORITHM: str = "HS256"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 30
    REFRESH_TOKEN_EXPIRE_DAYS: int = 7

    # Password Hashing Configuration
    PASSWORD_HASH_ALGORITHM: str = "argon2"  # Options: "argon2", "bcrypt", "scrypt"
    BCRYPT_ROUNDS: int = 14  # Higher = more secure but slower (12-16 recommended)
    ARGON2_TIME_COST: int = 3  # Number of iterations
    ARGON2_MEMORY_COST: int = 65536  # Memory usage in KB (64MB)
    ARGON2_PARALLELISM: int = 2  # Number of parallel threads
    SCRYPT_ROUNDS: int = 16  # 2^16 = 65536 rounds
    SCRYPT_BLOCK_SIZE: int = 8
    SCRYPT_PARALLELISM: int = 1
    
    # API Configuration
    API_V1_STR: str = "/api/v1"
    PROJECT_NAME: str = "ProfiDent API"
    PROJECT_VERSION: str = "1.0.0"
    DESCRIPTION: str = "Dental Product Price Comparison Platform API"
    
    # CORS Settings
    BACKEND_CORS_ORIGINS: list = ["http://localhost:3000", "http://localhost:5173"]
    
    # Search Configuration
    SEARCH_RESULTS_PER_PAGE: int = 20
    SEARCH_MAX_RESULTS: int = 1000
    SEARCH_CACHE_TTL: int = 3600  # 1 hour

    # Cache TTL Settings (in seconds)
    CACHE_TTL_SEARCH: int = 300  # 5 minutes
    CACHE_TTL_CATEGORIES: int = 3600  # 1 hour
    CACHE_TTL_BRANDS: int = 3600  # 1 hour
    CACHE_TTL_SELLERS: int = 3600  # 1 hour
    CACHE_TTL_PRODUCTS: int = 1800  # 30 minutes
    CACHE_TTL_SUGGESTIONS: int = 1800  # 30 minutes
    
    # File Upload Settings
    MAX_UPLOAD_SIZE: int = 10 * 1024 * 1024  # 10MB
    ALLOWED_EXTENSIONS: set = {".json", ".csv", ".xlsx"}
    
    # Logging
    LOG_LEVEL: str = "INFO"
    LOG_FORMAT: str = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    
    # Development Settings
    DEBUG: bool = True  # Set to True for development
    TESTING: bool = False
    
    @validator("DATABASE_URL", pre=True)
    def assemble_db_connection(cls, v: Optional[str], values: dict) -> str:
        """Assemble database URL from components if not provided."""
        if isinstance(v, str) and v:
            return v
        
        # Build from components
        user = values.get("POSTGRES_USER", "profident_user")
        password = values.get("POSTGRES_PASSWORD", "profident_dev_password")
        host = values.get("POSTGRES_HOST", "localhost")
        port = values.get("POSTGRES_PORT", 5433)
        db = values.get("POSTGRES_DB", "profident_dev")
        
        return f"postgresql+asyncpg://{user}:{password}@{host}:{port}/{db}"
    
    @validator("BACKEND_CORS_ORIGINS", pre=True)
    def assemble_cors_origins(cls, v):
        """Parse CORS origins from string or list."""
        if isinstance(v, str):
            return [i.strip() for i in v.split(",")]
        elif isinstance(v, list):
            return v
        raise ValueError("CORS origins must be a string or list")
    
    class Config:
        env_file = ".env"
        case_sensitive = True


# Global settings instance
settings = Settings()


def get_settings() -> Settings:
    """Get application settings."""
    return settings


# Database URL without asyncpg prefix for direct connections
def get_database_url_sync() -> str:
    """Get synchronous database URL (without asyncpg)."""
    return settings.DATABASE_URL.replace("postgresql+asyncpg://", "postgresql://")


def get_database_url_async() -> str:
    """Get asynchronous database URL (with asyncpg)."""
    if "asyncpg" not in settings.DATABASE_URL:
        return settings.DATABASE_URL.replace("postgresql://", "postgresql+asyncpg://")
    return settings.DATABASE_URL


# Redis connection parameters
def get_redis_config() -> dict:
    """Get Redis connection configuration."""
    return {
        "host": settings.REDIS_HOST,
        "port": settings.REDIS_PORT,
        "db": settings.REDIS_DB,
        "password": settings.REDIS_PASSWORD,
        "decode_responses": True,
        "socket_timeout": 5.0,
        "socket_connect_timeout": 5.0,
        "retry_on_timeout": True,
    }


# Connection pool configuration
def get_pool_config() -> dict:
    """Get database connection pool configuration."""
    return {
        "min_size": settings.DB_POOL_MIN_SIZE,
        "max_size": settings.DB_POOL_MAX_SIZE,
        "max_queries": settings.DB_POOL_MAX_QUERIES,
        "max_inactive_connection_lifetime": settings.DB_POOL_MAX_INACTIVE_CONNECTION_LIFETIME,
        "timeout": settings.DB_POOL_TIMEOUT,
        "command_timeout": settings.DB_POOL_COMMAND_TIMEOUT,
    }


# Environment helpers
def is_development() -> bool:
    """Check if running in development mode."""
    return settings.DEBUG or os.getenv("ENVIRONMENT", "development") == "development"


def is_production() -> bool:
    """Check if running in production mode."""
    return os.getenv("ENVIRONMENT", "development") == "production"


def is_testing() -> bool:
    """Check if running in testing mode."""
    return settings.TESTING or os.getenv("ENVIRONMENT", "development") == "testing"

import { useState } from "react";
import { useParams, useNavigate } from "react-router-dom";
import { useNetworkStatus } from "../hooks/useNetworkStatus";
import {
  ArrowLeft,
  Plus,
  Edit2,
  Trash2,
  Package,
  Calendar,
  ShoppingCart,
  X,
} from "lucide-react";
import {
  useShoppingList,
  useUpdateShoppingList,
  useDeleteShoppingList,
  useAddItemToList,
  useUpdateListItem,
  useRemoveItemFromList,
} from "../hooks/useShoppingLists";
import { formatDate } from "../lib/utils";
import {
  ShoppingListUpdate,
  ShoppingListItemCreate,
  ShoppingListItemUpdate,
} from "../types";

const ShoppingListDetailPage = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const listId = parseInt(id || "0");

  const [showEditModal, setShowEditModal] = useState(false);
  const [showAddItemModal, setShowAddItemModal] = useState(false);
  const [editingItem, setEditingItem] = useState<number | null>(null);
  const [formData, setFormData] = useState({
    name: "",
    description: "",
    is_default: false,
  });
  const [itemFormData, setItemFormData] = useState({
    product_id: 0,
    quantity: 1,
    notes: "",
  });

  const { data: shoppingList, isLoading, error } = useShoppingList(listId);
  const updateMutation = useUpdateShoppingList();
  const deleteMutation = useDeleteShoppingList();
  const addItemMutation = useAddItemToList();
  const updateItemMutation = useUpdateListItem();
  const removeItemMutation = useRemoveItemFromList();
  const networkStatus = useNetworkStatus();

  const handleEditList = () => {
    if (!shoppingList) return;
    setFormData({
      name: shoppingList.name,
      description: shoppingList.description || "",
      is_default: shoppingList.is_default || false,
    });
    setShowEditModal(true);
  };

  const handleUpdateList = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!formData.name.trim()) return;

    try {
      await updateMutation.mutateAsync({
        id: listId,
        data: formData as ShoppingListUpdate,
      });
      setShowEditModal(false);
    } catch (error) {
      // Error handling is done in the mutation
    }
  };

  const handleDeleteList = async () => {
    if (!confirm("Are you sure you want to delete this shopping list?")) return;

    try {
      await deleteMutation.mutateAsync(listId);
      navigate("/shopping-lists");
    } catch (error) {
      // Error handling is done in the mutation
    }
  };

  const handleAddItem = async (e: React.FormEvent) => {
    e.preventDefault();

    // Validation
    if (!itemFormData.product_id) {
      alert("Please enter a valid product ID");
      return;
    }
    if (itemFormData.quantity < 1) {
      alert("Quantity must be at least 1");
      return;
    }
    if (!networkStatus.isOnline) {
      alert("You are offline. Please check your connection and try again.");
      return;
    }

    try {
      await addItemMutation.mutateAsync({
        listId,
        item: itemFormData as ShoppingListItemCreate,
      });
      setShowAddItemModal(false);
      setItemFormData({ product_id: 0, quantity: 1, notes: "" });
    } catch (error) {
      // Error handling is done in the mutation
      console.error("Failed to add item:", error);
    }
  };

  const handleEditItem = (item: any) => {
    setEditingItem(item.id);
    setItemFormData({
      product_id: item.product_id,
      quantity: item.quantity,
      notes: item.notes || "",
    });
  };

  const handleUpdateItem = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!editingItem || itemFormData.quantity < 1) return;

    try {
      await updateItemMutation.mutateAsync({
        itemId: editingItem,
        data: {
          quantity: itemFormData.quantity,
          notes: itemFormData.notes,
        } as ShoppingListItemUpdate,
      });
      setEditingItem(null);
      setItemFormData({ product_id: 0, quantity: 1, notes: "" });
    } catch (error) {
      // Error handling is done in the mutation
    }
  };

  const handleRemoveItem = async (itemId: number) => {
    if (!confirm("Are you sure you want to remove this item?")) return;

    try {
      await removeItemMutation.mutateAsync({ listId, itemId });
    } catch (error) {
      // Error handling is done in the mutation
    }
  };

  const handleInputChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    const { name, value, type } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]:
        type === "checkbox" ? (e.target as HTMLInputElement).checked : value,
    }));
  };

  const handleItemInputChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    const { name, value, type } = e.target;
    setItemFormData((prev) => ({
      ...prev,
      [name]: type === "number" ? parseInt(value) || 0 : value,
    }));
  };

  if (isLoading) {
    return (
      <div className="max-w-4xl mx-auto">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-1/4 mb-4"></div>
          <div className="h-6 bg-gray-200 rounded w-1/2 mb-8"></div>
          <div className="space-y-4">
            {[...Array(3)].map((_, i) => (
              <div key={i} className="h-20 bg-gray-200 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  if (error || !shoppingList) {
    return (
      <div className="max-w-4xl mx-auto">
        <div className="text-center py-12">
          <ShoppingCart className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <h2 className="text-xl font-semibold text-gray-900 mb-2">
            Shopping List Not Found
          </h2>
          <p className="text-gray-600 mb-4">
            The shopping list you're looking for doesn't exist or you don't have
            access to it.
          </p>
          <button
            onClick={() => navigate("/shopping-lists")}
            className="inline-flex items-center px-4 py-2 bg-primary-600 text-white rounded-md hover:bg-primary-700"
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Shopping Lists
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-4xl mx-auto">
      {/* Network Status Indicator */}
      {!networkStatus.isOnline && (
        <div className="mb-4 p-3 bg-red-100 border border-red-300 rounded-md">
          <p className="text-red-800 text-sm">
            ⚠️ You are offline. Some features may not work properly.
          </p>
        </div>
      )}

      {networkStatus.isSlowConnection && networkStatus.isOnline && (
        <div className="mb-4 p-3 bg-yellow-100 border border-yellow-300 rounded-md">
          <p className="text-yellow-800 text-sm">
            🐌 Slow connection detected. Loading may take longer than usual.
          </p>
        </div>
      )}

      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-4">
          <button
            onClick={() => navigate("/shopping-lists")}
            className="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-md"
          >
            <ArrowLeft className="h-5 w-5" />
          </button>
          <div>
            <h1 className="text-2xl font-bold text-gray-900">
              {shoppingList.name}
            </h1>
            {shoppingList.description && (
              <p className="text-gray-600 mt-1">{shoppingList.description}</p>
            )}
          </div>
        </div>

        <div className="flex items-center space-x-3">
          <button
            onClick={handleEditList}
            className="inline-flex items-center px-3 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50"
          >
            <Edit2 className="h-4 w-4 mr-2" />
            Edit List
          </button>
          <button
            onClick={() => setShowAddItemModal(true)}
            className="inline-flex items-center px-3 py-2 text-sm font-medium text-white bg-primary-600 border border-transparent rounded-md hover:bg-primary-700"
          >
            <Plus className="h-4 w-4 mr-2" />
            Add Item
          </button>
          <button
            onClick={handleDeleteList}
            className="inline-flex items-center px-3 py-2 text-sm font-medium text-red-700 bg-white border border-red-300 rounded-md hover:bg-red-50"
          >
            <Trash2 className="h-4 w-4 mr-2" />
            Delete
          </button>
        </div>
      </div>

      {/* List Info */}
      <div className="bg-white rounded-lg border border-gray-200 p-6 mb-6">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="flex items-center">
            <Package className="h-5 w-5 text-gray-400 mr-2" />
            <div>
              <p className="text-sm text-gray-600">Total Items</p>
              <p className="text-lg font-semibold text-gray-900">
                {shoppingList.summary?.total_items || 0}
              </p>
            </div>
          </div>
          <div className="flex items-center">
            <ShoppingCart className="h-5 w-5 text-gray-400 mr-2" />
            <div>
              <p className="text-sm text-gray-600">Total Quantity</p>
              <p className="text-lg font-semibold text-gray-900">
                {shoppingList.summary?.total_quantity || 0}
              </p>
            </div>
          </div>
          <div className="flex items-center">
            <Calendar className="h-5 w-5 text-gray-400 mr-2" />
            <div>
              <p className="text-sm text-gray-600">Last Updated</p>
              <p className="text-lg font-semibold text-gray-900">
                {formatDate(shoppingList.updated_at)}
              </p>
            </div>
          </div>
        </div>

        {/* Progress Bar */}
        {shoppingList.summary && shoppingList.summary.total_items > 0 && (
          <div className="mt-6">
            <div className="flex items-center justify-between text-sm text-gray-600 mb-2">
              <span>Progress</span>
              <span>
                {shoppingList.summary.completion_percentage.toFixed(0)}%
                complete
              </span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div
                className="bg-primary-600 h-2 rounded-full transition-all duration-300"
                style={{
                  width: `${shoppingList.summary.completion_percentage}%`,
                }}
              />
            </div>
          </div>
        )}
      </div>

      {/* Shopping List Items */}
      <div className="bg-white rounded-lg border border-gray-200">
        <div className="px-6 py-4 border-b border-gray-200">
          <h2 className="text-lg font-semibold text-gray-900">Items</h2>
        </div>

        {shoppingList.items && shoppingList.items.length > 0 ? (
          <div className="divide-y divide-gray-200">
            {shoppingList.items.map((item) => (
              <div key={item.id} className="px-6 py-4 hover:bg-gray-50">
                {editingItem === item.id ? (
                  <form onSubmit={handleUpdateItem} className="space-y-4">
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          Quantity
                        </label>
                        <input
                          type="number"
                          name="quantity"
                          value={itemFormData.quantity}
                          onChange={handleItemInputChange}
                          min="1"
                          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
                          required
                        />
                      </div>
                      <div className="md:col-span-2">
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          Notes
                        </label>
                        <input
                          type="text"
                          name="notes"
                          value={itemFormData.notes}
                          onChange={handleItemInputChange}
                          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
                          placeholder="Add notes..."
                        />
                      </div>
                    </div>
                    <div className="flex justify-end space-x-3">
                      <button
                        type="button"
                        onClick={() => setEditingItem(null)}
                        className="px-3 py-2 text-sm text-gray-700 border border-gray-300 rounded-md hover:bg-gray-50"
                      >
                        Cancel
                      </button>
                      <button
                        type="submit"
                        disabled={updateItemMutation.isPending}
                        className="px-3 py-2 text-sm text-white bg-primary-600 rounded-md hover:bg-primary-700 disabled:opacity-50"
                      >
                        {updateItemMutation.isPending
                          ? "Updating..."
                          : "Update"}
                      </button>
                    </div>
                  </form>
                ) : (
                  <div className="flex items-center justify-between">
                    <div className="flex-1">
                      <h3 className="text-lg font-medium text-gray-900">
                        {item.product?.name || `Product ID: ${item.product_id}`}
                      </h3>
                      <div className="flex items-center space-x-4 mt-1 text-sm text-gray-600">
                        <span>Quantity: {item.quantity}</span>
                        {item.product?.seller && (
                          <span>Seller: {item.product.seller}</span>
                        )}
                        {item.product?.price && (
                          <span>Price: {item.product.price}</span>
                        )}
                      </div>
                      {item.notes && (
                        <p className="text-sm text-gray-600 mt-1">
                          Notes: {item.notes}
                        </p>
                      )}
                    </div>
                    <div className="flex items-center space-x-2">
                      <button
                        onClick={() => handleEditItem(item)}
                        className="p-2 text-gray-400 hover:text-primary-600 hover:bg-primary-50 rounded-md"
                      >
                        <Edit2 className="h-4 w-4" />
                      </button>
                      <button
                        onClick={() => handleRemoveItem(item.id)}
                        className="p-2 text-gray-400 hover:text-red-600 hover:bg-red-50 rounded-md"
                      >
                        <Trash2 className="h-4 w-4" />
                      </button>
                    </div>
                  </div>
                )}
              </div>
            ))}
          </div>
        ) : (
          <div className="px-6 py-12 text-center">
            <Package className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              No items yet
            </h3>
            <p className="text-gray-600 mb-4">
              Start adding items to your shopping list to see them here.
            </p>
            <button
              onClick={() => setShowAddItemModal(true)}
              className="inline-flex items-center px-4 py-2 bg-primary-600 text-white rounded-md hover:bg-primary-700"
            >
              <Plus className="h-4 w-4 mr-2" />
              Add First Item
            </button>
          </div>
        )}
      </div>

      {/* Edit List Modal */}
      {showEditModal && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-md">
            <div className="flex items-center justify-between mb-4">
              <h2 className="text-lg font-semibold text-gray-900">
                Edit Shopping List
              </h2>
              <button
                onClick={() => setShowEditModal(false)}
                className="text-gray-400 hover:text-gray-600"
              >
                <X className="h-5 w-5" />
              </button>
            </div>

            <form onSubmit={handleUpdateList}>
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    List Name *
                  </label>
                  <input
                    type="text"
                    name="name"
                    value={formData.name}
                    onChange={handleInputChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
                    required
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Description
                  </label>
                  <textarea
                    name="description"
                    value={formData.description}
                    onChange={handleInputChange}
                    rows={3}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
                  />
                </div>

                <div className="flex items-center">
                  <input
                    type="checkbox"
                    name="is_default"
                    checked={formData.is_default}
                    onChange={handleInputChange}
                    className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                  />
                  <label className="ml-2 block text-sm text-gray-700">
                    Make this my default shopping list
                  </label>
                </div>
              </div>

              <div className="flex justify-end space-x-3 mt-6">
                <button
                  type="button"
                  onClick={() => setShowEditModal(false)}
                  className="px-4 py-2 text-gray-700 border border-gray-300 rounded-md hover:bg-gray-50"
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  disabled={!formData.name.trim() || updateMutation.isPending}
                  className="px-4 py-2 bg-primary-600 text-white rounded-md hover:bg-primary-700 disabled:opacity-50"
                >
                  {updateMutation.isPending ? "Updating..." : "Update List"}
                </button>
              </div>
            </form>
          </div>
        </div>
      )}

      {/* Add Item Modal */}
      {showAddItemModal && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-md">
            <div className="flex items-center justify-between mb-4">
              <h2 className="text-lg font-semibold text-gray-900">Add Item</h2>
              <button
                onClick={() => setShowAddItemModal(false)}
                className="text-gray-400 hover:text-gray-600"
              >
                <X className="h-5 w-5" />
              </button>
            </div>

            <form onSubmit={handleAddItem}>
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Product ID *
                  </label>
                  <input
                    type="number"
                    name="product_id"
                    value={itemFormData.product_id || ""}
                    onChange={handleItemInputChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
                    required
                  />
                  <p className="text-xs text-gray-500 mt-1">
                    Enter the product ID you want to add
                  </p>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Quantity *
                  </label>
                  <input
                    type="number"
                    name="quantity"
                    value={itemFormData.quantity}
                    onChange={handleItemInputChange}
                    min="1"
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
                    required
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Notes
                  </label>
                  <textarea
                    name="notes"
                    value={itemFormData.notes}
                    onChange={handleItemInputChange}
                    rows={3}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
                    placeholder="Add any notes about this item..."
                  />
                </div>
              </div>

              <div className="flex justify-end space-x-3 mt-6">
                <button
                  type="button"
                  onClick={() => setShowAddItemModal(false)}
                  className="px-4 py-2 text-gray-700 border border-gray-300 rounded-md hover:bg-gray-50"
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  disabled={
                    !itemFormData.product_id ||
                    itemFormData.quantity < 1 ||
                    addItemMutation.isPending
                  }
                  className="px-4 py-2 bg-primary-600 text-white rounded-md hover:bg-primary-700 disabled:opacity-50"
                >
                  {addItemMutation.isPending ? "Adding..." : "Add Item"}
                </button>
              </div>
            </form>
          </div>
        </div>
      )}
    </div>
  );
};

export default ShoppingListDetailPage;

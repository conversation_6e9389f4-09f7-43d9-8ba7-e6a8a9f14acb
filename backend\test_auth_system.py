#!/usr/bin/env python3
"""
Test script for authentication system
Verifies user registration, login, and JWT token functionality
"""

import asyncio
import sys
import json
from pathlib import Path

# Add the app directory to Python path
sys.path.insert(0, str(Path(__file__).parent / "app"))

from app.database import DatabaseManager
from app.services.auth_service import AuthService
from app.schemas.auth import UserCreate, UserLogin, UserUpdate, PasswordChange
from app.utils.security import verify_token, create_token_response
from app.models.user import UserRepository


async def test_password_validation():
    """Test password validation."""
    print("🔍 Testing password validation...")
    
    from app.utils.security import validate_password_strength
    
    # Test weak passwords
    weak_passwords = [
        "123",
        "password",
        "PASSWORD",
        "12345678",
        "Password",
    ]
    
    for password in weak_passwords:
        is_valid, errors = validate_password_strength(password)
        if is_valid:
            print(f"❌ Weak password '{password}' was incorrectly accepted")
            return False
        else:
            print(f"✅ Weak password '{password}' correctly rejected: {errors[0]}")
    
    # Test strong password
    strong_password = "StrongPass123!"
    is_valid, errors = validate_password_strength(strong_password)
    if not is_valid:
        print(f"❌ Strong password '{strong_password}' was incorrectly rejected: {errors}")
        return False
    else:
        print(f"✅ Strong password '{strong_password}' correctly accepted")
    
    return True


async def test_user_registration():
    """Test user registration."""
    print("\n🔍 Testing user registration...")
    
    try:
        db = DatabaseManager()
        await db.initialize()
        
        # Test user data
        user_data = UserCreate(
            email="<EMAIL>",
            password="TestPass123!",
            full_name="Test User"
        )
        
        async with db.get_connection() as conn:
            # Clean up any existing test user
            await conn.execute("DELETE FROM users WHERE email = $1", user_data.email)
            
            # Register user
            user, tokens = await AuthService.register_user(conn, user_data)
            
            print(f"✅ User registered successfully: {user.email}")
            print(f"✅ User ID: {user.id}")
            print(f"✅ Tokens generated: access_token length = {len(tokens['access_token'])}")
            
            # Verify user in database
            db_user = await UserRepository.get_user_by_email(conn, user_data.email)
            if not db_user:
                print("❌ User not found in database after registration")
                return False, None
            
            print(f"✅ User verified in database: {db_user.email}")
            
            # Test duplicate registration
            try:
                await AuthService.register_user(conn, user_data)
                print("❌ Duplicate registration should have failed")
                return False, None
            except Exception as e:
                print(f"✅ Duplicate registration correctly rejected: {type(e).__name__}")
        
        await db.close()
        return True, tokens
        
    except Exception as e:
        print(f"❌ User registration test failed: {e}")
        return False, None


async def test_user_authentication():
    """Test user authentication."""
    print("\n🔍 Testing user authentication...")
    
    try:
        db = DatabaseManager()
        await db.initialize()
        
        login_data = UserLogin(
            email="<EMAIL>",
            password="TestPass123!"
        )
        
        async with db.get_connection() as conn:
            # Test successful login
            user, tokens = await AuthService.authenticate_user(conn, login_data)
            
            print(f"✅ User authenticated successfully: {user.email}")
            print(f"✅ Access token generated: {len(tokens['access_token'])} chars")
            print(f"✅ Refresh token generated: {len(tokens['refresh_token'])} chars")
            
            # Test wrong password
            wrong_login = UserLogin(
                email="<EMAIL>",
                password="WrongPassword123!"
            )
            
            try:
                await AuthService.authenticate_user(conn, wrong_login)
                print("❌ Wrong password should have failed")
                return False, None
            except Exception as e:
                print(f"✅ Wrong password correctly rejected: {type(e).__name__}")
            
            # Test non-existent user
            fake_login = UserLogin(
                email="<EMAIL>",
                password="TestPass123!"
            )
            
            try:
                await AuthService.authenticate_user(conn, fake_login)
                print("❌ Non-existent user should have failed")
                return False, None
            except Exception as e:
                print(f"✅ Non-existent user correctly rejected: {type(e).__name__}")
        
        await db.close()
        return True, tokens
        
    except Exception as e:
        print(f"❌ User authentication test failed: {e}")
        return False, None


async def test_token_operations():
    """Test JWT token operations."""
    print("\n🔍 Testing JWT token operations...")
    
    try:
        db = DatabaseManager()
        await db.initialize()
        
        async with db.get_connection() as conn:
            # Get user
            user = await UserRepository.get_user_by_email(conn, "<EMAIL>")
            if not user:
                print("❌ Test user not found")
                return False
            
            # Test token creation
            tokens = create_token_response(user.id, user.email)
            print(f"✅ Tokens created successfully")
            
            # Test access token verification
            access_payload = verify_token(tokens["access_token"])
            if not access_payload:
                print("❌ Access token verification failed")
                return False
            
            print(f"✅ Access token verified: user_id = {access_payload.get('sub')}")
            
            # Test refresh token verification
            refresh_payload = verify_token(tokens["refresh_token"])
            if not refresh_payload:
                print("❌ Refresh token verification failed")
                return False
            
            print(f"✅ Refresh token verified: type = {refresh_payload.get('type')}")
            
            # Test get current user
            current_user = await AuthService.get_current_user(conn, tokens["access_token"])
            if current_user.id != user.id:
                print("❌ Get current user failed")
                return False
            
            print(f"✅ Get current user successful: {current_user.email}")
            
            # Test token refresh
            new_tokens = await AuthService.refresh_access_token(conn, tokens["refresh_token"])
            print(f"✅ Token refresh successful")
            
            # Verify new access token
            new_payload = verify_token(new_tokens["access_token"])
            if not new_payload or new_payload.get("sub") != user.id:
                print("❌ New access token verification failed")
                return False
            
            print(f"✅ New access token verified")
        
        await db.close()
        return True
        
    except Exception as e:
        print(f"❌ Token operations test failed: {e}")
        return False


async def test_user_operations():
    """Test user profile operations."""
    print("\n🔍 Testing user profile operations...")
    
    try:
        db = DatabaseManager()
        await db.initialize()
        
        async with db.get_connection() as conn:
            # Get user
            user = await UserRepository.get_user_by_email(conn, "<EMAIL>")
            if not user:
                print("❌ Test user not found")
                return False
            
            # Test profile update
            update_data = UserUpdate(
                full_name="Updated Test User",
                email="<EMAIL>"
            )
            
            updated_user = await AuthService.update_user_profile(conn, user.id, update_data)
            print(f"✅ Profile updated: {updated_user.full_name}, {updated_user.email}")
            
            # Test password change
            success = await AuthService.change_password(
                conn, user.id, "TestPass123!", "NewTestPass123!"
            )
            if not success:
                print("❌ Password change failed")
                return False
            
            print(f"✅ Password changed successfully")
            
            # Verify new password works
            login_data = UserLogin(
                email="<EMAIL>",
                password="NewTestPass123!"
            )
            
            auth_user, tokens = await AuthService.authenticate_user(conn, login_data)
            print(f"✅ New password authentication successful")
        
        await db.close()
        return True
        
    except Exception as e:
        print(f"❌ User operations test failed: {e}")
        return False


async def cleanup_test_data():
    """Clean up test data."""
    print("\n🧹 Cleaning up test data...")
    
    try:
        db = DatabaseManager()
        await db.initialize()
        
        async with db.get_connection() as conn:
            # Delete test users
            await conn.execute("DELETE FROM users WHERE email IN ($1, $2)",
                             "<EMAIL>", "<EMAIL>")
            print("✅ Test data cleaned up")
        
        await db.close()
        
    except Exception as e:
        print(f"⚠️ Cleanup failed: {e}")


async def main():
    """Run all authentication tests."""
    print("🚀 Starting ProfiDent Authentication System Tests\n")
    print("=" * 60)
    
    tests = [
        ("Password Validation", test_password_validation),
        ("User Registration", test_user_registration),
        ("User Authentication", test_user_authentication),
        ("Token Operations", test_token_operations),
        ("User Operations", test_user_operations),
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            if test_name in ["User Registration", "User Authentication"]:
                result, _ = await test_func()
            else:
                result = await test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} failed with exception: {e}")
            results.append((test_name, False))
    
    # Cleanup
    await cleanup_test_data()
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 TEST SUMMARY")
    print("=" * 60)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{status} - {test_name}")
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All authentication tests passed!")
        print("✅ Authentication system is working correctly")
        return True
    else:
        print("❌ Some tests failed. Check authentication system configuration.")
        return False


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)

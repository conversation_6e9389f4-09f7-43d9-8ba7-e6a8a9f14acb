import React from 'react';
import { Link, useLocation } from 'react-router-dom';
import { ChevronRight, Home } from 'lucide-react';
import { useBreadcrumbs } from '../../hooks/useNavigation';
import { cn } from '../../lib/utils';

interface BreadcrumbsProps {
  className?: string;
  showHome?: boolean;
  customBreadcrumbs?: Array<{
    name: string;
    href?: string;
  }>;
}

/**
 * Breadcrumbs component that automatically generates breadcrumbs based on current route
 * or accepts custom breadcrumb items
 */
export const Breadcrumbs: React.FC<BreadcrumbsProps> = ({
  className = '',
  showHome = true,
  customBreadcrumbs,
}) => {
  const location = useLocation();
  const autoBreadcrumbs = useBreadcrumbs(location.pathname);
  
  // Use custom breadcrumbs if provided, otherwise use auto-generated ones
  const breadcrumbs = customBreadcrumbs || autoBreadcrumbs;
  
  // Don't show breadcrumbs if we're on the home page and only have one item
  if (breadcrumbs.length <= 1 && location.pathname === '/') {
    return null;
  }

  return (
    <nav className={cn('flex items-center space-x-1 text-sm text-gray-500', className)}>
      {showHome && location.pathname !== '/' && (
        <>
          <Link
            to="/"
            className="flex items-center hover:text-gray-700 transition-colors"
          >
            <Home className="h-4 w-4" />
          </Link>
          {breadcrumbs.length > 0 && (
            <ChevronRight className="h-4 w-4 text-gray-400" />
          )}
        </>
      )}
      
      {breadcrumbs.map((item, index) => {
        const isLast = index === breadcrumbs.length - 1;
        
        return (
          <React.Fragment key={item.href || item.name}>
            {item.href && !isLast ? (
              <Link
                to={item.href}
                className="hover:text-gray-700 transition-colors"
              >
                {item.name}
              </Link>
            ) : (
              <span className={cn(
                isLast ? 'text-gray-900 font-medium' : 'text-gray-500'
              )}>
                {item.name}
              </span>
            )}
            
            {!isLast && (
              <ChevronRight className="h-4 w-4 text-gray-400" />
            )}
          </React.Fragment>
        );
      })}
    </nav>
  );
};

/**
 * Page header component that includes breadcrumbs and page title
 */
interface PageHeaderProps {
  title: string;
  subtitle?: string;
  breadcrumbs?: Array<{
    name: string;
    href?: string;
  }>;
  actions?: React.ReactNode;
  className?: string;
}

export const PageHeader: React.FC<PageHeaderProps> = ({
  title,
  subtitle,
  breadcrumbs,
  actions,
  className = '',
}) => {
  return (
    <div className={cn('mb-6', className)}>
      <Breadcrumbs customBreadcrumbs={breadcrumbs} className="mb-2" />
      
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">{title}</h1>
          {subtitle && (
            <p className="mt-1 text-sm text-gray-600">{subtitle}</p>
          )}
        </div>
        
        {actions && (
          <div className="flex items-center space-x-3">
            {actions}
          </div>
        )}
      </div>
    </div>
  );
};

export default Breadcrumbs;

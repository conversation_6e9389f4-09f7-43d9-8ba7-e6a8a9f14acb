#!/usr/bin/env python3
"""
Simple verification test for search API endpoints with authorization system.
"""

import asyncio
import aiohttp
import json


async def test_search_endpoints():
    """Test all search endpoints to verify they work with authorization."""
    print("🔍 Testing Search Endpoints with Authorization")
    print("=" * 50)
    
    base_url = "http://localhost:8000"
    
    async with aiohttp.ClientSession() as session:
        # Test 1: Search suggestions without auth
        print("\n🔍 Test 1: Search suggestions without authentication...")
        try:
            async with session.get(f"{base_url}/api/v1/search/suggestions?q=dental") as response:
                if response.status == 200:
                    suggestions = await response.json()
                    print(f"✅ Search suggestions: {len(suggestions)} results")
                else:
                    print(f"❌ Search suggestions failed: {response.status}")
                    return False
        except Exception as e:
            print(f"❌ Search suggestions error: {e}")
            return False
        
        # Test 2: Main search without auth
        print("\n🔍 Test 2: Main search without authentication...")
        try:
            async with session.get(f"{base_url}/api/v1/search/?q=dental&limit=5") as response:
                if response.status == 200:
                    search_data = await response.json()
                    results = search_data.get('results', [])
                    print(f"✅ Main search: {len(results)} results")
                else:
                    print(f"❌ Main search failed: {response.status}")
                    return False
        except Exception as e:
            print(f"❌ Main search error: {e}")
            return False
        
        # Test 3: Search with filters
        print("\n🔍 Test 3: Search with filters...")
        try:
            async with session.get(f"{base_url}/api/v1/search/?q=dental&category=Dental Instruments&limit=3") as response:
                if response.status == 200:
                    search_data = await response.json()
                    results = search_data.get('results', [])
                    print(f"✅ Filtered search: {len(results)} results")
                else:
                    print(f"❌ Filtered search failed: {response.status}")
                    return False
        except Exception as e:
            print(f"❌ Filtered search error: {e}")
            return False
        
        # Test 4: Register user and test with authentication
        print("\n🔍 Test 4: Search with user authentication...")
        try:
            # Register user
            user_data = {
                "email": "<EMAIL>",
                "password": "SearchTest123!",
                "full_name": "Search Verify Test"
            }
            
            async with session.post(f"{base_url}/api/v1/auth/register", json=user_data) as response:
                if response.status == 201:
                    reg_data = await response.json()
                    access_token = reg_data["token"]["access_token"]
                    print("✅ User registered and authenticated")
                else:
                    print(f"❌ User registration failed: {response.status}")
                    return False
            
            # Test search with authentication
            headers = {"Authorization": f"Bearer {access_token}"}
            
            async with session.get(f"{base_url}/api/v1/search/suggestions?q=dental", headers=headers) as response:
                if response.status == 200:
                    suggestions = await response.json()
                    print(f"✅ Authenticated search suggestions: {len(suggestions)} results")
                else:
                    print(f"❌ Authenticated search failed: {response.status}")
                    return False
                    
        except Exception as e:
            print(f"❌ Authenticated search error: {e}")
            return False
        
        # Test 5: Test with superadmin
        print("\n🔍 Test 5: Search with superadmin authentication...")
        try:
            # Login as superadmin
            superadmin_login = {
                "email": "<EMAIL>",
                "password": "SuperYzn123!"
            }
            
            async with session.post(f"{base_url}/api/v1/auth/login", json=superadmin_login) as response:
                if response.status == 200:
                    login_data = await response.json()
                    superadmin_token = login_data["token"]["access_token"]
                    print("✅ Superadmin authenticated")
                else:
                    print(f"❌ Superadmin login failed: {response.status}")
                    return False
            
            # Test search with superadmin
            superadmin_headers = {"Authorization": f"Bearer {superadmin_token}"}
            
            async with session.get(f"{base_url}/api/v1/search/?q=dental&limit=3", headers=superadmin_headers) as response:
                if response.status == 200:
                    search_data = await response.json()
                    results = search_data.get('results', [])
                    print(f"✅ Superadmin search: {len(results)} results")
                else:
                    print(f"❌ Superadmin search failed: {response.status}")
                    return False
                    
        except Exception as e:
            print(f"❌ Superadmin search error: {e}")
            return False
        
        print("\n🎉 All Search Tests Passed!")
        print("=" * 50)
        print("✅ Search suggestions working without auth")
        print("✅ Main search working without auth")
        print("✅ Filtered search working")
        print("✅ Search working with user authentication")
        print("✅ Search working with superadmin authentication")
        print("✅ CRITICAL: Search functionality fully preserved with RBAC")
        
        return True


async def main():
    """Run search verification tests."""
    print("🚀 Search Authorization Verification")
    print("Critical test to ensure search works with RBAC system")
    print()
    
    success = await test_search_endpoints()
    
    if success:
        print("\n🎉 Search authorization verification PASSED!")
        print("🔒 Search functionality is fully compatible with RBAC!")
        return True
    else:
        print("\n❌ Search authorization verification FAILED!")
        print("🚨 CRITICAL: Search functionality may be compromised!")
        return False


if __name__ == "__main__":
    success = asyncio.run(main())
    exit(0 if success else 1)

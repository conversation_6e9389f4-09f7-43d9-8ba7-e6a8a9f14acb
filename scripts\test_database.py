#!/usr/bin/env python3
"""
PostgreSQL and pgvector Installation Test Script
This script verifies that PostgreSQL and pgvector are properly installed and configured.
"""

import sys
import os
import asyncio
from typing import Optional

def test_imports():
    """Test if required Python packages are available."""
    print("🔍 Testing Python package imports...")
    
    try:
        import asyncpg
        print("✅ asyncpg imported successfully")
    except ImportError:
        print("❌ asyncpg not found. Install with: pip install asyncpg")
        return False
    
    try:
        import psycopg2
        print("✅ psycopg2 imported successfully")
    except ImportError:
        print("⚠️  psycopg2 not found (optional). Install with: pip install psycopg2-binary")
    
    return True

async def test_database_connection(connection_string: str):
    """Test basic database connection."""
    print(f"🔍 Testing database connection...")
    print(f"Connection string: {connection_string}")
    
    try:
        conn = await asyncpg.connect(connection_string)
        print("✅ Database connection successful")
        
        # Test basic query
        result = await conn.fetchval("SELECT version()")
        print(f"✅ PostgreSQL version: {result}")
        
        await conn.close()
        return True
        
    except Exception as e:
        print(f"❌ Database connection failed: {e}")
        return False

async def test_pgvector_extension(connection_string: str):
    """Test pgvector extension."""
    print("🔍 Testing pgvector extension...")
    
    try:
        conn = await asyncpg.connect(connection_string)
        
        # Check if pgvector extension exists
        result = await conn.fetchval(
            "SELECT EXISTS(SELECT 1 FROM pg_extension WHERE extname = 'vector')"
        )
        
        if not result:
            print("⚠️  pgvector extension not installed. Attempting to install...")
            try:
                await conn.execute("CREATE EXTENSION IF NOT EXISTS vector")
                print("✅ pgvector extension installed successfully")
            except Exception as e:
                print(f"❌ Failed to install pgvector extension: {e}")
                await conn.close()
                return False
        else:
            print("✅ pgvector extension is already installed")
        
        # Test vector operations
        try:
            distance = await conn.fetchval(
                "SELECT '[1,2,3]'::vector <-> '[4,5,6]'::vector"
            )
            print(f"✅ Vector distance calculation successful: {distance}")
        except Exception as e:
            print(f"❌ Vector operations failed: {e}")
            await conn.close()
            return False
        
        await conn.close()
        return True
        
    except Exception as e:
        print(f"❌ pgvector test failed: {e}")
        return False

async def test_database_schema(connection_string: str):
    """Test if database schema can be created."""
    print("🔍 Testing database schema creation...")
    
    try:
        conn = await asyncpg.connect(connection_string)
        
        # Test creating a simple table with vector column
        await conn.execute("""
            CREATE TABLE IF NOT EXISTS test_vectors (
                id SERIAL PRIMARY KEY,
                name TEXT,
                embedding vector(3)
            )
        """)
        print("✅ Test table with vector column created successfully")
        
        # Test inserting vector data
        await conn.execute("""
            INSERT INTO test_vectors (name, embedding) 
            VALUES ('test', '[1,2,3]'::vector)
            ON CONFLICT DO NOTHING
        """)
        print("✅ Vector data inserted successfully")
        
        # Test vector similarity search
        result = await conn.fetchrow("""
            SELECT name, embedding <-> '[1,2,3]'::vector as distance 
            FROM test_vectors 
            ORDER BY distance 
            LIMIT 1
        """)
        print(f"✅ Vector similarity search successful: {result}")
        
        # Clean up test table
        await conn.execute("DROP TABLE IF EXISTS test_vectors")
        print("✅ Test table cleaned up")
        
        await conn.close()
        return True
        
    except Exception as e:
        print(f"❌ Database schema test failed: {e}")
        return False

async def run_full_test():
    """Run complete database installation test."""
    print("🚀 Starting PostgreSQL and pgvector installation test...\n")
    
    # Test Python imports
    if not test_imports():
        print("\n❌ Python package test failed. Please install required packages.")
        return False
    
    print()
    
    # Get connection string from environment or use default
    connection_string = os.getenv(
        'DATABASE_URL', 
        'postgresql://postgres:postgres@localhost:5432/profident_dev'
    )
    
    # Test database connection
    if not await test_database_connection(connection_string):
        print("\n❌ Database connection test failed.")
        print("Please ensure PostgreSQL is installed and running.")
        return False
    
    print()
    
    # Test pgvector extension
    if not await test_pgvector_extension(connection_string):
        print("\n❌ pgvector extension test failed.")
        print("Please install pgvector extension.")
        return False
    
    print()
    
    # Test database schema
    if not await test_database_schema(connection_string):
        print("\n❌ Database schema test failed.")
        return False
    
    print("\n🎉 All tests passed! PostgreSQL and pgvector are properly installed and configured.")
    print("✅ Ready to proceed with ProfiDent development.")
    return True

def main():
    """Main function."""
    try:
        result = asyncio.run(run_full_test())
        sys.exit(0 if result else 1)
    except KeyboardInterrupt:
        print("\n⚠️  Test interrupted by user.")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
"""
Test script for FastAPI application structure
Verifies that the FastAPI app can start and basic endpoints work
"""

import asyncio
import sys
import httpx
import time
from pathlib import Path

# Add the app directory to Python path
sys.path.insert(0, str(Path(__file__).parent / "app"))

from app.main import app
from app.config import settings
from app.database import db_manager


async def test_app_creation():
    """Test FastAPI app creation."""
    print("🔍 Testing FastAPI app creation...")
    
    try:
        # Check app attributes
        assert app.title == settings.PROJECT_NAME
        assert app.version == settings.PROJECT_VERSION
        print(f"✅ App created successfully: {app.title} v{app.version}")
        
        # Check routes are registered
        routes = [route.path for route in app.routes]
        expected_routes = ["/", "/health", f"{settings.API_V1_STR}/info"]
        
        for route in expected_routes:
            if route in routes:
                print(f"✅ Route registered: {route}")
            else:
                print(f"❌ Route missing: {route}")
                return False
        
        # Check auth routes
        auth_routes = [route.path for route in app.routes if "/auth/" in route.path]
        if auth_routes:
            print(f"✅ Authentication routes registered: {len(auth_routes)} routes")
        else:
            print("❌ Authentication routes not found")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ App creation test failed: {e}")
        return False


async def test_database_startup():
    """Test database startup process."""
    print("\n🔍 Testing database startup...")
    
    try:
        # Initialize database manager
        await db_manager.initialize()
        print("✅ Database manager initialized")
        
        # Test health check
        health = await db_manager.health_check()
        print(f"✅ Database health check: {health}")
        
        # Check pool status
        pool_status = await db_manager.get_pool_status()
        print(f"✅ Connection pool status: {pool_status}")
        
        return True
        
    except Exception as e:
        print(f"❌ Database startup test failed: {e}")
        return False


async def test_app_endpoints():
    """Test basic app endpoints using test client."""
    print("\n🔍 Testing app endpoints...")
    
    try:
        from fastapi.testclient import TestClient
        
        client = TestClient(app)
        
        # Test root endpoint
        response = client.get("/")
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Root endpoint: {data['message']}")
        else:
            print(f"❌ Root endpoint failed: {response.status_code}")
            return False
        
        # Test health endpoint
        response = client.get("/health")
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Health endpoint: {data['status']}")
        else:
            print(f"❌ Health endpoint failed: {response.status_code}")
            return False
        
        # Test API info endpoint
        response = client.get(f"{settings.API_V1_STR}/info")
        if response.status_code == 200:
            data = response.json()
            print(f"✅ API info endpoint: {data['name']}")
        else:
            print(f"❌ API info endpoint failed: {response.status_code}")
            return False
        
        # Test auth health endpoint
        response = client.get(f"{settings.API_V1_STR}/auth/health")
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Auth health endpoint: {data['message']}")
        else:
            print(f"❌ Auth health endpoint failed: {response.status_code}")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ App endpoints test failed: {e}")
        return False


async def test_openapi_docs():
    """Test OpenAPI documentation generation."""
    print("\n🔍 Testing OpenAPI documentation...")
    
    try:
        from fastapi.testclient import TestClient
        
        client = TestClient(app)
        
        # Test OpenAPI JSON
        response = client.get(f"{settings.API_V1_STR}/openapi.json")
        if response.status_code == 200:
            openapi_spec = response.json()
            print(f"✅ OpenAPI spec generated: {openapi_spec['info']['title']}")
            
            # Check if auth endpoints are documented
            paths = openapi_spec.get("paths", {})
            auth_paths = [path for path in paths.keys() if "/auth/" in path]
            print(f"✅ Auth endpoints documented: {len(auth_paths)} endpoints")
            
        else:
            print(f"❌ OpenAPI spec generation failed: {response.status_code}")
            return False
        
        # Test docs endpoint
        response = client.get(f"{settings.API_V1_STR}/docs")
        if response.status_code == 200:
            print("✅ Swagger UI docs accessible")
        else:
            print(f"❌ Swagger UI docs failed: {response.status_code}")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ OpenAPI docs test failed: {e}")
        return False


async def test_middleware():
    """Test middleware functionality."""
    print("\n🔍 Testing middleware...")
    
    try:
        from fastapi.testclient import TestClient
        
        client = TestClient(app)
        
        # Test CORS middleware
        response = client.options("/", headers={"Origin": "http://localhost:3000"})
        if "access-control-allow-origin" in response.headers:
            print("✅ CORS middleware working")
        else:
            print("⚠️ CORS middleware may not be working")
        
        # Test process time header
        response = client.get("/")
        if "x-process-time" in response.headers:
            process_time = response.headers["x-process-time"]
            print(f"✅ Process time middleware working: {process_time}s")
        else:
            print("⚠️ Process time middleware may not be working")
        
        return True
        
    except Exception as e:
        print(f"❌ Middleware test failed: {e}")
        return False


async def test_error_handling():
    """Test error handling."""
    print("\n🔍 Testing error handling...")
    
    try:
        from fastapi.testclient import TestClient
        
        client = TestClient(app)
        
        # Test 404 error
        response = client.get("/nonexistent")
        if response.status_code == 404:
            data = response.json()
            if "success" in data and not data["success"]:
                print("✅ 404 error handling working")
            else:
                print("⚠️ 404 error format may be incorrect")
        else:
            print("❌ 404 error handling failed")
            return False
        
        # Test validation error
        response = client.post(f"{settings.API_V1_STR}/auth/register", json={})
        if response.status_code == 422:
            data = response.json()
            if "success" in data and not data["success"]:
                print("✅ Validation error handling working")
            else:
                print("⚠️ Validation error format may be incorrect")
        else:
            print("⚠️ Validation error handling may not be working")
        
        return True
        
    except Exception as e:
        print(f"❌ Error handling test failed: {e}")
        return False


async def cleanup():
    """Clean up resources."""
    print("\n🧹 Cleaning up...")
    
    try:
        await db_manager.close()
        print("✅ Database connections closed")
    except Exception as e:
        print(f"⚠️ Cleanup warning: {e}")


async def main():
    """Run all FastAPI application tests."""
    print("🚀 Starting ProfiDent FastAPI Application Tests\n")
    print(f"App: {settings.PROJECT_NAME} v{settings.PROJECT_VERSION}")
    print(f"API Base: {settings.API_V1_STR}")
    print("=" * 60)
    
    tests = [
        ("App Creation", test_app_creation),
        ("Database Startup", test_database_startup),
        ("App Endpoints", test_app_endpoints),
        ("OpenAPI Documentation", test_openapi_docs),
        ("Middleware", test_middleware),
        ("Error Handling", test_error_handling),
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = await test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} failed with exception: {e}")
            results.append((test_name, False))
    
    # Cleanup
    await cleanup()
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 TEST SUMMARY")
    print("=" * 60)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{status} - {test_name}")
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All FastAPI application tests passed!")
        print("✅ FastAPI application structure is working correctly")
        return True
    else:
        print("❌ Some tests failed. Check FastAPI application configuration.")
        return False


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)

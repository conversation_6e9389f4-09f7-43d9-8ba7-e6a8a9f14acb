"""
Middleware package for ProfiDent application.

This package contains middleware components for authentication, authorization,
rate limiting, and other cross-cutting concerns.
"""

from .authorization import (
    # Core classes
    AuthorizationError,
    Role<PERSON>he<PERSON>,
    
    # Convenience functions
    require_role,
    require_any_role,
    require_all_roles,
    require_permission,
    require_any_permission,
    require_all_permissions,
    
    # Predefined dependencies
    require_superadmin,
    require_admin,
    require_user,
    require_user_management,
    require_role_management,
    require_permission_management,
    require_system_admin,
    require_search_access,
    require_shopping_list_access,
    
    # Resource ownership functions
    check_resource_ownership,
    require_resource_ownership,
)

__all__ = [
    # Core classes
    "AuthorizationError",
    "RoleChecker",
    
    # Convenience functions
    "require_role",
    "require_any_role",
    "require_all_roles",
    "require_permission",
    "require_any_permission",
    "require_all_permissions",
    
    # Predefined dependencies
    "require_superadmin",
    "require_admin",
    "require_user",
    "require_user_management",
    "require_role_management",
    "require_permission_management",
    "require_system_admin",
    "require_search_access",
    "require_shopping_list_access",
    
    # Resource ownership functions
    "check_resource_ownership",
    "require_resource_ownership",
]

import React, { useEffect, useState } from 'react';
import { CheckCircle, XCircle, X } from 'lucide-react';
import { cn } from '../../lib/utils';

interface Notification {
  id: string;
  type: 'success' | 'error';
  message: string;
  product?: any;
  duration?: number;
}

export const ShoppingListNotifications: React.FC = () => {
  const [notifications, setNotifications] = useState<Notification[]>([]);

  useEffect(() => {
    const handleSuccess = (event: CustomEvent) => {
      const notification: Notification = {
        id: Date.now().toString(),
        type: 'success',
        message: event.detail.message,
        product: event.detail.product,
        duration: 4000,
      };
      
      setNotifications(prev => [...prev, notification]);
      
      // Auto-remove after duration
      setTimeout(() => {
        removeNotification(notification.id);
      }, notification.duration);
    };

    const handleError = (event: CustomEvent) => {
      const notification: Notification = {
        id: Date.now().toString(),
        type: 'error',
        message: event.detail.message,
        duration: 6000,
      };
      
      setNotifications(prev => [...prev, notification]);
      
      // Auto-remove after duration
      setTimeout(() => {
        removeNotification(notification.id);
      }, notification.duration);
    };

    window.addEventListener('shopping-list-success', handleSuccess as EventListener);
    window.addEventListener('shopping-list-error', handleError as EventListener);

    return () => {
      window.removeEventListener('shopping-list-success', handleSuccess as EventListener);
      window.removeEventListener('shopping-list-error', handleError as EventListener);
    };
  }, []);

  const removeNotification = (id: string) => {
    setNotifications(prev => prev.filter(notification => notification.id !== id));
  };

  if (notifications.length === 0) {
    return null;
  }

  return (
    <div className="fixed top-20 right-4 z-50 space-y-2">
      {notifications.map((notification) => (
        <div
          key={notification.id}
          className={cn(
            "max-w-sm w-full bg-white shadow-lg rounded-lg pointer-events-auto ring-1 ring-black ring-opacity-5 overflow-hidden transform transition-all duration-300 ease-in-out",
            "animate-in slide-in-from-right-full"
          )}
        >
          <div className="p-4">
            <div className="flex items-start">
              <div className="flex-shrink-0">
                {notification.type === 'success' ? (
                  <CheckCircle className="h-6 w-6 text-green-400" />
                ) : (
                  <XCircle className="h-6 w-6 text-red-400" />
                )}
              </div>
              
              <div className="ml-3 w-0 flex-1 pt-0.5">
                <p className={cn(
                  "text-sm font-medium",
                  notification.type === 'success' ? "text-gray-900" : "text-red-900"
                )}>
                  {notification.type === 'success' ? 'Added to Shopping List' : 'Error'}
                </p>
                
                <p className={cn(
                  "mt-1 text-sm",
                  notification.type === 'success' ? "text-gray-500" : "text-red-700"
                )}>
                  {notification.message}
                </p>
                
                {notification.product && (
                  <div className="mt-2 text-xs text-gray-400">
                    MFR: {notification.product.mfr} • {notification.product.seller}
                  </div>
                )}
              </div>
              
              <div className="ml-4 flex-shrink-0 flex">
                <button
                  onClick={() => removeNotification(notification.id)}
                  className={cn(
                    "rounded-md inline-flex text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-offset-2",
                    notification.type === 'success' ? "focus:ring-green-500" : "focus:ring-red-500"
                  )}
                >
                  <span className="sr-only">Close</span>
                  <X className="h-5 w-5" />
                </button>
              </div>
            </div>
          </div>
          
          {/* Progress bar */}
          <div className={cn(
            "h-1 w-full",
            notification.type === 'success' ? "bg-green-50" : "bg-red-50"
          )}>
            <div
              className={cn(
                "h-full transition-all ease-linear",
                notification.type === 'success' ? "bg-green-400" : "bg-red-400"
              )}
              style={{
                width: '100%',
                animation: `shrink ${notification.duration}ms linear forwards`
              }}
            />
          </div>
        </div>
      ))}
      
      <style jsx>{`
        @keyframes shrink {
          from { width: 100%; }
          to { width: 0%; }
        }
      `}</style>
    </div>
  );
};

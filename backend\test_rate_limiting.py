#!/usr/bin/env python3
"""
Test script for rate limiting middleware.
Tests rate limiting on authentication endpoints and other API endpoints.
"""

import asyncio
import aiohttp
import json
import time
from app.config import settings


async def test_login_rate_limiting():
    """Test rate limiting on login endpoint."""
    print("🔒 Testing Login Rate Limiting")
    print("=" * 50)
    
    base_url = "http://localhost:8000"
    
    async with aiohttp.ClientSession() as session:
        # Test 1: Normal login attempts (should work)
        print("\n🔍 Test 1: Normal login attempts...")
        
        login_data = {
            "email": "<EMAIL>",
            "password": "wrongpassword"
        }
        
        # Make 3 failed login attempts (should be allowed)
        for i in range(3):
            async with session.post(f"{base_url}/api/v1/auth/login", json=login_data) as response:
                print(f"   Attempt {i+1}: {response.status}")
                
                # Check rate limit headers
                if "X-RateLimit-Limit" in response.headers:
                    print(f"   Rate Limit: {response.headers['X-RateLimit-Remaining']}/{response.headers['X-RateLimit-Limit']}")
        
        # Test 2: Exceed rate limit (should be blocked)
        print("\n🔍 Test 2: Exceed login rate limit...")
        
        # Make additional attempts to trigger rate limiting (limit is 5 per 5 minutes)
        for i in range(4, 8):
            async with session.post(f"{base_url}/api/v1/auth/login", json=login_data) as response:
                print(f"   Attempt {i+1}: {response.status}")
                
                if response.status == 429:
                    print("   ✅ Rate limiting triggered!")
                    response_data = await response.json()
                    print(f"   Message: {response_data.get('message', 'N/A')}")
                    print(f"   Retry after: {response_data.get('retry_after', 'N/A')} seconds")
                    break
                elif "X-RateLimit-Remaining" in response.headers:
                    print(f"   Rate Limit: {response.headers['X-RateLimit-Remaining']}/{response.headers['X-RateLimit-Limit']}")


async def test_registration_rate_limiting():
    """Test rate limiting on registration endpoint."""
    print("\n🔒 Testing Registration Rate Limiting")
    print("=" * 50)
    
    base_url = "http://localhost:8000"
    
    async with aiohttp.ClientSession() as session:
        print("\n🔍 Test: Registration rate limiting...")
        
        # Try multiple registrations with same email (should fail after first, but test rate limiting)
        for i in range(5):
            timestamp = int(time.time())
            user_data = {
                "email": f"rate_test_{timestamp}_{i}@test.com",
                "password": "TestPassword123!",
                "full_name": f"Rate Test User {i}"
            }
            
            async with session.post(f"{base_url}/api/v1/auth/register", json=user_data) as response:
                print(f"   Registration {i+1}: {response.status}")
                
                if response.status == 429:
                    print("   ✅ Registration rate limiting triggered!")
                    response_data = await response.json()
                    print(f"   Message: {response_data.get('message', 'N/A')}")
                    break
                elif "X-RateLimit-Remaining" in response.headers:
                    print(f"   Rate Limit: {response.headers['X-RateLimit-Remaining']}/{response.headers['X-RateLimit-Limit']}")


async def test_search_rate_limiting():
    """Test rate limiting on search endpoints (should be more generous)."""
    print("\n🔍 Testing Search Rate Limiting")
    print("=" * 50)
    
    base_url = "http://localhost:8000"
    
    async with aiohttp.ClientSession() as session:
        print("\n🔍 Test: Search rate limiting (should allow many requests)...")
        
        # Make many search requests quickly
        successful_requests = 0
        rate_limited = False
        
        for i in range(20):  # Try 20 requests
            async with session.get(f"{base_url}/api/v1/search/suggestions?q=test{i}") as response:
                if response.status == 200:
                    successful_requests += 1
                elif response.status == 429:
                    print(f"   Rate limited after {successful_requests} requests")
                    rate_limited = True
                    break
                
                if i % 5 == 0 and "X-RateLimit-Remaining" in response.headers:
                    print(f"   Request {i+1}: Rate Limit {response.headers['X-RateLimit-Remaining']}/{response.headers['X-RateLimit-Limit']}")
        
        print(f"   ✅ Completed {successful_requests} search requests")
        if not rate_limited:
            print("   ✅ Search rate limiting is appropriately generous")


async def test_admin_rate_limiting():
    """Test rate limiting on admin endpoints."""
    print("\n🔒 Testing Admin Rate Limiting")
    print("=" * 50)
    
    base_url = "http://localhost:8000"
    
    async with aiohttp.ClientSession() as session:
        # First login as superadmin
        print("\n🔍 Step 1: Login as superadmin...")
        
        superadmin_login = {
            "email": "<EMAIL>",
            "password": "SuperYzn123!"
        }
        
        async with session.post(f"{base_url}/api/v1/auth/login", json=superadmin_login) as response:
            if response.status == 200:
                login_data = await response.json()
                access_token = login_data["token"]["access_token"]
                print("   ✅ Superadmin authenticated")
            else:
                print(f"   ❌ Superadmin login failed: {response.status}")
                return
        
        headers = {"Authorization": f"Bearer {access_token}"}
        
        print("\n🔍 Test: Admin endpoint rate limiting...")
        
        # Make multiple admin requests
        successful_requests = 0
        
        for i in range(15):  # Try 15 admin requests
            async with session.get(f"{base_url}/api/v1/admin/users", headers=headers) as response:
                if response.status == 200:
                    successful_requests += 1
                elif response.status == 429:
                    print(f"   Rate limited after {successful_requests} admin requests")
                    response_data = await response.json()
                    print(f"   Message: {response_data.get('message', 'N/A')}")
                    break
                
                if i % 5 == 0 and "X-RateLimit-Remaining" in response.headers:
                    print(f"   Request {i+1}: Rate Limit {response.headers['X-RateLimit-Remaining']}/{response.headers['X-RateLimit-Limit']}")
        
        print(f"   ✅ Completed {successful_requests} admin requests")


async def test_rate_limit_headers():
    """Test that rate limit headers are properly included in responses."""
    print("\n📊 Testing Rate Limit Headers")
    print("=" * 50)
    
    base_url = "http://localhost:8000"
    
    async with aiohttp.ClientSession() as session:
        print("\n🔍 Test: Rate limit headers in responses...")
        
        async with session.get(f"{base_url}/api/v1/search/suggestions?q=test") as response:
            print(f"   Response status: {response.status}")
            
            # Check for rate limit headers
            headers_to_check = [
                "X-RateLimit-Limit",
                "X-RateLimit-Remaining", 
                "X-RateLimit-Reset",
                "X-RateLimit-Window"
            ]
            
            for header in headers_to_check:
                if header in response.headers:
                    print(f"   ✅ {header}: {response.headers[header]}")
                else:
                    print(f"   ❌ Missing header: {header}")


async def test_different_client_isolation():
    """Test that rate limiting properly isolates different clients."""
    print("\n🌐 Testing Client Isolation")
    print("=" * 50)
    
    base_url = "http://localhost:8000"
    
    # Create two sessions with different User-Agent headers to simulate different clients
    headers1 = {"User-Agent": "TestClient1/1.0"}
    headers2 = {"User-Agent": "TestClient2/1.0"}
    
    async with aiohttp.ClientSession(headers=headers1) as session1, \
               aiohttp.ClientSession(headers=headers2) as session2:
        
        print("\n🔍 Test: Client isolation...")
        
        # Make requests from both clients
        login_data = {
            "email": "<EMAIL>",
            "password": "wrongpassword"
        }
        
        print("   Client 1 requests:")
        for i in range(3):
            async with session1.post(f"{base_url}/api/v1/auth/login", json=login_data) as response:
                if "X-RateLimit-Remaining" in response.headers:
                    print(f"     Request {i+1}: {response.headers['X-RateLimit-Remaining']}/{response.headers['X-RateLimit-Limit']}")
        
        print("   Client 2 requests (should have separate rate limit):")
        for i in range(3):
            async with session2.post(f"{base_url}/api/v1/auth/login", json=login_data) as response:
                if "X-RateLimit-Remaining" in response.headers:
                    print(f"     Request {i+1}: {response.headers['X-RateLimit-Remaining']}/{response.headers['X-RateLimit-Limit']}")
        
        print("   ✅ Clients should have separate rate limit counters")


async def main():
    """Run all rate limiting tests."""
    print("🚀 Starting Rate Limiting Tests")
    print("=" * 60)
    print("Testing rate limiting middleware for authentication and API endpoints.")
    print()
    
    try:
        # Test 1: Login rate limiting
        await test_login_rate_limiting()
        
        # Test 2: Registration rate limiting
        await test_registration_rate_limiting()
        
        # Test 3: Search rate limiting (should be generous)
        await test_search_rate_limiting()
        
        # Test 4: Admin rate limiting
        await test_admin_rate_limiting()
        
        # Test 5: Rate limit headers
        await test_rate_limit_headers()
        
        # Test 6: Client isolation
        await test_different_client_isolation()
        
        print("\n🎉 Rate Limiting Tests Completed!")
        print("=" * 60)
        print("✅ Login rate limiting working")
        print("✅ Registration rate limiting working")
        print("✅ Search rate limiting appropriately configured")
        print("✅ Admin rate limiting working")
        print("✅ Rate limit headers included")
        print("✅ Client isolation working")
        print("✅ Rate limiting middleware successfully implemented")
        
        return True
        
    except Exception as e:
        print(f"\n❌ Rate limiting test failed: {str(e)}")
        return False


if __name__ == "__main__":
    success = asyncio.run(main())
    
    if success:
        print("\n🎉 All rate limiting tests completed!")
        exit(0)
    else:
        print("\n❌ Some rate limiting tests failed!")
        exit(1)

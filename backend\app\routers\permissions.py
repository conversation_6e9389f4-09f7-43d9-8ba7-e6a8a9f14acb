"""
Permission Management API Router
CRUD operations for permissions and role-permission assignments
"""

from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, status, Query
from asyncpg import Connection

try:
    from ..dependencies import get_db_connection, get_current_active_user
    from ..models.user import User
    from ..models.permission import Permission, PermissionRepository
    from ..models.role import RoleRepository
    from ..schemas.rbac import (
        PermissionCreate, PermissionUpdate, PermissionResponse, 
        PermissionListResponse, PermissionSummary,
        RolePermissionAssignmentCreate, RolePermissionAssignmentResponse,
        BulkRolePermissionAssignment, RBACErrorResponse
    )
except ImportError:
    from dependencies import get_db_connection, get_current_active_user
    from models.user import User
    from models.permission import Permission, PermissionRepository
    from models.role import RoleRepository
    from schemas.rbac import (
        PermissionCreate, PermissionUpdate, PermissionResponse, 
        PermissionListResponse, PermissionSummary,
        RolePermissionAssignmentCreate, RolePermissionAssignmentResponse,
        BulkRolePermissionAssignment, RBACErrorResponse
    )

# Create router
router = APIRouter(prefix="/permissions", tags=["permission-management"])


async def require_permission_admin(current_user: User = Depends(get_current_active_user)) -> User:
    """Dependency to require permission management permissions."""
    if not current_user.has_permission("permissions.manage") and not current_user.is_superuser:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Insufficient permissions. Permission management requires 'permissions.manage' permission."
        )
    return current_user


@router.get("/", response_model=PermissionListResponse)
async def list_permissions(
    page: int = Query(1, ge=1, description="Page number"),
    page_size: int = Query(20, ge=1, le=100, description="Items per page"),
    resource: Optional[str] = Query(None, description="Filter by resource"),
    action: Optional[str] = Query(None, description="Filter by action"),
    search: Optional[str] = Query(None, description="Search in permission name or description"),
    conn: Connection = Depends(get_db_connection),
    current_user: User = Depends(get_current_active_user)
):
    """List all permissions with pagination and filtering."""
    try:
        # Check permissions
        if not current_user.has_permission("permissions.read") and not current_user.is_superuser:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Insufficient permissions to view permissions."
            )
        
        # Get permissions with pagination
        permissions = await PermissionRepository.get_permissions_paginated(
            conn=conn,
            page=page,
            page_size=page_size,
            resource=resource,
            action=action,
            search=search
        )
        
        # Get total count for pagination
        total = await PermissionRepository.get_permissions_count(
            conn=conn,
            resource=resource,
            action=action,
            search=search
        )
        
        total_pages = (total + page_size - 1) // page_size
        
        return PermissionListResponse(
            permissions=[PermissionResponse(**perm.to_dict()) for perm in permissions],
            total=total,
            page=page,
            page_size=page_size,
            total_pages=total_pages
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to list permissions: {str(e)}"
        )


@router.get("/summary", response_model=PermissionSummary)
async def get_permissions_summary(
    conn: Connection = Depends(get_db_connection),
    current_user: User = Depends(require_permission_admin)
):
    """Get permission summary statistics."""
    try:
        summary = await PermissionRepository.get_permissions_summary(conn)
        return PermissionSummary(**summary)
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get permissions summary: {str(e)}"
        )


@router.get("/{permission_id}", response_model=PermissionResponse)
async def get_permission(
    permission_id: str,
    conn: Connection = Depends(get_db_connection),
    current_user: User = Depends(get_current_active_user)
):
    """Get a specific permission."""
    try:
        # Check permissions
        if not current_user.has_permission("permissions.read") and not current_user.is_superuser:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Insufficient permissions to view permissions."
            )
        
        # Get permission
        permission = await PermissionRepository.get_permission_by_id(conn, permission_id)
        if not permission:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Permission not found"
            )
        
        return PermissionResponse(**permission.to_dict())
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get permission: {str(e)}"
        )


@router.post("/", response_model=PermissionResponse, status_code=status.HTTP_201_CREATED)
async def create_permission(
    permission_data: PermissionCreate,
    conn: Connection = Depends(get_db_connection),
    current_user: User = Depends(require_permission_admin)
):
    """Create a new permission."""
    try:
        # Check if permission name already exists
        existing_permission = await PermissionRepository.get_permission_by_name(conn, permission_data.name)
        if existing_permission:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Permission with name '{permission_data.name}' already exists"
            )
        
        # Create permission
        permission = await PermissionRepository.create_permission(
            conn=conn,
            name=permission_data.name,
            resource=permission_data.resource,
            action=permission_data.action,
            description=permission_data.description
        )
        
        return PermissionResponse(**permission.to_dict())
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to create permission: {str(e)}"
        )


@router.put("/{permission_id}", response_model=PermissionResponse)
async def update_permission(
    permission_id: str,
    permission_data: PermissionUpdate,
    conn: Connection = Depends(get_db_connection),
    current_user: User = Depends(require_permission_admin)
):
    """Update an existing permission."""
    try:
        # Check if permission exists
        existing_permission = await PermissionRepository.get_permission_by_id(conn, permission_id)
        if not existing_permission:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Permission not found"
            )
        
        # Prevent modification of system permissions
        if existing_permission.is_system_permission:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Cannot modify system permissions"
            )
        
        # Update permission
        updated_permission = await PermissionRepository.update_permission(
            conn=conn,
            permission_id=permission_id,
            description=permission_data.description
        )
        
        if not updated_permission:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Permission not found or update failed"
            )
        
        return PermissionResponse(**updated_permission.to_dict())
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to update permission: {str(e)}"
        )


@router.delete("/{permission_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_permission(
    permission_id: str,
    conn: Connection = Depends(get_db_connection),
    current_user: User = Depends(require_permission_admin)
):
    """Delete a permission."""
    try:
        # Check if permission exists
        existing_permission = await PermissionRepository.get_permission_by_id(conn, permission_id)
        if not existing_permission:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Permission not found"
            )
        
        # Attempt to delete permission (will fail for system permissions)
        success = await PermissionRepository.delete_permission(conn, permission_id)
        if not success:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Cannot delete system permissions or permission deletion failed"
            )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to delete permission: {str(e)}"
        )


# Role-Permission Assignment Endpoints
@router.post("/assign-to-role", response_model=RolePermissionAssignmentResponse, status_code=status.HTTP_201_CREATED)
async def assign_permission_to_role(
    assignment_data: RolePermissionAssignmentCreate,
    conn: Connection = Depends(get_db_connection),
    current_user: User = Depends(require_permission_admin)
):
    """Assign a permission to a role."""
    try:
        # Verify role exists
        role = await RoleRepository.get_role_by_id(conn, assignment_data.role_id)
        if not role:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Role not found"
            )
        
        # Verify permission exists
        permission = await PermissionRepository.get_permission_by_id(conn, assignment_data.permission_id)
        if not permission:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Permission not found"
            )
        
        # Create assignment
        assignment = await PermissionRepository.assign_permission_to_role(
            conn=conn,
            role_id=assignment_data.role_id,
            permission_id=assignment_data.permission_id,
            granted_by=current_user.id
        )
        
        return RolePermissionAssignmentResponse(**assignment.to_dict())
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to assign permission to role: {str(e)}"
        )


@router.post("/bulk-assign-to-role", status_code=status.HTTP_201_CREATED)
async def bulk_assign_permissions_to_role(
    assignment_data: BulkRolePermissionAssignment,
    conn: Connection = Depends(get_db_connection),
    current_user: User = Depends(require_permission_admin)
):
    """Assign multiple permissions to a role."""
    try:
        # Verify role exists
        role = await RoleRepository.get_role_by_id(conn, assignment_data.role_id)
        if not role:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Role not found"
            )
        
        # Bulk assign permissions
        results = await PermissionRepository.bulk_assign_permissions_to_role(
            conn=conn,
            role_id=assignment_data.role_id,
            permission_ids=assignment_data.permission_ids,
            granted_by=current_user.id
        )
        
        return {
            "message": f"Successfully assigned {results['assigned']} permissions to role",
            "assigned": results['assigned'],
            "skipped": results['skipped'],
            "errors": results['errors']
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to bulk assign permissions to role: {str(e)}"
        )

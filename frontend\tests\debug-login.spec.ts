import { test, expect } from '@playwright/test';

/**
 * Debug Login Test
 * 
 * Simple test to debug the login process step by step
 */

const SUPERADMIN_CREDENTIALS = {
  email: '<EMAIL>',
  password: 'SuperYzn123!'
};

const BASE_URL = 'http://localhost:3000';

test.describe('Debug Login Process', () => {
  
  test.beforeEach(async ({ page }) => {
    // Clear any existing authentication
    await page.goto(BASE_URL);
    await page.evaluate(() => {
      localStorage.clear();
      sessionStorage.clear();
    });
    
    // Wait for page to load
    await page.waitForLoadState('networkidle');
  });

  test('should successfully login step by step', async ({ page }) => {
    console.log('Step 1: Navigate to login page');
    await page.goto(`${BASE_URL}/login`);
    await page.waitForLoadState('networkidle');
    
    // Take screenshot of login page
    await page.screenshot({ path: 'debug-login-step1.png' });
    
    console.log('Step 2: Check if login form is visible');
    await expect(page.getByRole('textbox', { name: 'Email address' })).toBeVisible();
    await expect(page.getByRole('textbox', { name: 'Password' })).toBeVisible();
    await expect(page.getByRole('button', { name: 'Sign in' })).toBeVisible();
    
    console.log('Step 3: Fill email field');
    await page.getByRole('textbox', { name: 'Email address' }).fill(SUPERADMIN_CREDENTIALS.email);
    
    console.log('Step 4: Fill password field');
    await page.getByRole('textbox', { name: 'Password' }).fill(SUPERADMIN_CREDENTIALS.password);
    
    // Take screenshot before clicking login
    await page.screenshot({ path: 'debug-login-step4.png' });
    
    console.log('Step 5: Click login button');
    await page.getByRole('button', { name: 'Sign in' }).click();
    
    console.log('Step 6: Wait for response');
    await page.waitForTimeout(2000); // Wait 2 seconds
    
    // Take screenshot after clicking login
    await page.screenshot({ path: 'debug-login-step6.png' });
    
    console.log('Step 7: Check current URL');
    const currentUrl = page.url();
    console.log('Current URL:', currentUrl);
    
    // Check for any error messages
    const errorElements = await page.locator('[class*="error"], [class*="alert"], .text-red-500, .text-red-600').all();
    if (errorElements.length > 0) {
      console.log('Found potential error elements:');
      for (const element of errorElements) {
        const text = await element.textContent();
        console.log('Error text:', text);
      }
    }
    
    // Check network requests
    console.log('Step 8: Check for network activity');
    await page.waitForTimeout(3000); // Wait for any pending requests
    
    // Take final screenshot
    await page.screenshot({ path: 'debug-login-final.png' });
    
    console.log('Step 9: Check if redirected to home page');
    if (currentUrl === `${BASE_URL}/`) {
      console.log('SUCCESS: Redirected to home page');
    } else if (currentUrl === `${BASE_URL}/login`) {
      console.log('ISSUE: Still on login page');
      
      // Check for any visible error messages
      const visibleText = await page.textContent('body');
      console.log('Page content includes:', visibleText?.substring(0, 500));
    } else {
      console.log('UNEXPECTED: Redirected to:', currentUrl);
    }
  });

  test('should check network requests during login', async ({ page }) => {
    const requests: any[] = [];
    const responses: any[] = [];
    
    // Listen to network events
    page.on('request', request => {
      requests.push({
        url: request.url(),
        method: request.method(),
        headers: request.headers(),
        postData: request.postData()
      });
    });
    
    page.on('response', response => {
      responses.push({
        url: response.url(),
        status: response.status(),
        statusText: response.statusText()
      });
    });
    
    console.log('Navigate to login page');
    await page.goto(`${BASE_URL}/login`);
    await page.waitForLoadState('networkidle');
    
    console.log('Fill login form');
    await page.getByRole('textbox', { name: 'Email address' }).fill(SUPERADMIN_CREDENTIALS.email);
    await page.getByRole('textbox', { name: 'Password' }).fill(SUPERADMIN_CREDENTIALS.password);
    
    console.log('Submit login form');
    await page.getByRole('button', { name: 'Sign in' }).click();
    
    // Wait for network activity
    await page.waitForTimeout(5000);
    
    console.log('=== NETWORK REQUESTS ===');
    requests.forEach((req, index) => {
      if (req.url.includes('auth') || req.url.includes('login') || req.url.includes('csrf')) {
        console.log(`Request ${index + 1}:`, {
          url: req.url,
          method: req.method,
          postData: req.postData
        });
      }
    });
    
    console.log('=== NETWORK RESPONSES ===');
    responses.forEach((res, index) => {
      if (res.url.includes('auth') || res.url.includes('login') || res.url.includes('csrf')) {
        console.log(`Response ${index + 1}:`, {
          url: res.url,
          status: res.status,
          statusText: res.statusText
        });
      }
    });
  });
});
